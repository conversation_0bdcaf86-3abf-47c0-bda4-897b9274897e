import aiomysql
import asyncio
import logging

from src.config.load_config import load_config


logging.basicConfig(level=logging.INFO)


config = load_config(
    [
        "MYSQL_CHARSET",
        "MYSQL_DB",
        "MYSQL_HOST",
        "MYSQL_PASSWORD",
        "MYSQL_PORT",
        "MYSQL_USERNAME",
    ]
)


async def insert_row(insert_sql: str, args: list | tuple):
    try:
        pool = await aiomysql.create_pool(
            host=config.get("MYSQL_HOST"),
            user=config.get("MYSQL_USERNAME"),
            password=config.get("MYSQL_PASSWORD"),
            db=config.get("MYSQL_DB"),
            port=int(config.get("MYSQL_PORT", 3306)),
            charset=config.get("MYSQL_CHARSET"),
            autocommit=True,
            loop=asyncio.get_event_loop(),
        )
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                await cursor.execute(insert_sql, args)
        pool.close()
        await pool.wait_closed()
    except Exception as e:
        logging.error(f"Error inserting row: {e}")


async def select_row(select_sql: str, args: list | tuple):
    try:
        pool = await aiomysql.create_pool(
            host=config.get("MYSQL_HOST"),
            user=config.get("MYSQL_USERNAME"),
            password=config.get("MYSQL_PASSWORD"),
            db=config.get("MYSQL_DB"),
            port=int(config.get("MYSQL_PORT", 3306)),
            charset=config.get("MYSQL_CHARSET"),
            autocommit=True,
            loop=asyncio.get_event_loop(),
        )
        async with pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(select_sql, args)
                row = await cursor.fetchall()
        pool.close()
        await pool.wait_closed()
        return row
    except Exception as e:
        logging.error(f"Error inserting row: {e}")
        return None


__all__ = ["insert_row", "select_row"]
