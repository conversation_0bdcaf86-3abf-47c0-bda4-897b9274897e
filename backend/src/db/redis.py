import redis.asyncio as aioredis
import redis

from src.config.load_config import load_config

config = load_config(
    ["REDIS_DB", "REDIS_HOST", "REDIS_MAX_CONNECTIONS", "REDIS_PORT", "REDIS_PREFIX"]
)
redis_url = (
    f"redis://{config['REDIS_HOST']}:{config['REDIS_PORT']}/{config['REDIS_DB']}"
)

async_connection_pool = aioredis.ConnectionPool(
    max_connections=int(config["REDIS_MAX_CONNECTIONS"])
).from_url(url=redis_url)
connection_pool = redis.ConnectionPool(
    max_connections=int(config["REDIS_MAX_CONNECTIONS"])
).from_url(url=redis_url)


class AsyncRedis:
    def __init__(self):
        self.prefix = config.get("REDIS_PREFIX")
        self.client = aioredis.Redis(connection_pool=async_connection_pool)

    def _add_prefix(self, key):
        if isinstance(key, bytes):
            key = key.decode()
        return f"{self.prefix}:{key}"

    async def set(self, key, value, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.set(key, value, *args, **kwargs)

    async def get(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.get(key, *args, **kwargs)

    async def exists(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.exists(key, *args, **kwargs)

    async def enqueue(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.rpush(key, *args, **kwargs)

    async def dequeue(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.lpop(key, *args, **kwargs)


class Redis:
    def __init__(self):
        self.prefix = config.get("REDIS_PREFIX")
        self.client = redis.Redis(connection_pool=connection_pool)

    def _add_prefix(self, key):
        if isinstance(key, bytes):
            key = key.decode()
        return f"{self.prefix}:{key}"

    def set(self, key, value, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.set(key, value, *args, **kwargs)

    def get(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.get(key, *args, **kwargs)

    def exists(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.exists(key, *args, **kwargs)

    def enqueue(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.rpush(key, *args, **kwargs)

    def dequeue(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.lpop(key, *args, **kwargs)


__all__ = ["AsyncRedis", "Redis"]