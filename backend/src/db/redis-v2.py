import redis.asyncio as aioredis
import redis
from loguru import logger

from src.config.load_config import load_config

config = load_config(
    [
        "REDIS_HOST",
        "REDIS_MAX_CONNECTIONS",
        "REDIS_PASSWORD",
        "REDIS_PORT",
        "REDIS_PREFIX",
        "REDIS_USE_CLUSTER",
        "REDIS_CLUSTER_NODES",
    ]
)


def _create_async_client():
    """创建异步 Redis 客户端（单机或集群）"""
    use_cluster = config.get("REDIS_USE_CLUSTER", "false").lower() == "true"
    
    if use_cluster:
        # 集群模式
        cluster_nodes = config.get("REDIS_CLUSTER_NODES", "").strip()
        if cluster_nodes:
            # 解析集群节点配置
            startup_nodes = []
            for node in cluster_nodes.split(","):
                node = node.strip()
                if ":" in node:
                    host, port = node.split(":", 1)
                    startup_nodes.append({"host": host, "port": int(port)})
            
            if startup_nodes:
                logger.info(f"使用 Redis 集群模式，节点: {startup_nodes}")
                return aioredis.RedisCluster(
                    startup_nodes=startup_nodes,
                    password=config.get("REDIS_PASSWORD"),
                    max_connections=int(config.get("REDIS_MAX_CONNECTIONS", 50)),
                    decode_responses=True
                )
        
        # 如果没有配置集群节点，回退到单节点集群
        logger.warning("REDIS_USE_CLUSTER=true 但未配置 REDIS_CLUSTER_NODES，使用单节点集群")
        return aioredis.RedisCluster(
            host=config["REDIS_HOST"],
            port=int(config["REDIS_PORT"]),
            password=config.get("REDIS_PASSWORD"),
            max_connections=int(config.get("REDIS_MAX_CONNECTIONS", 50))
        )
    else:
        # 单机模式
        logger.info(f"使用 Redis 单机模式: {config['REDIS_HOST']}:{config['REDIS_PORT']}")
        return aioredis.Redis(
            host=config["REDIS_HOST"],
            port=int(config["REDIS_PORT"]),
            password=config.get("REDIS_PASSWORD"),
            max_connections=int(config.get("REDIS_MAX_CONNECTIONS", 50)),
            decode_responses=True
        )


def _create_sync_client():
    """创建同步 Redis 客户端（单机或集群）"""
    use_cluster = config.get("REDIS_USE_CLUSTER", "false").lower() == "true"
    
    if use_cluster:
        # 集群模式
        cluster_nodes = config.get("REDIS_CLUSTER_NODES", "").strip()
        if cluster_nodes:
            # 解析集群节点配置
            startup_nodes = []
            for node in cluster_nodes.split(","):
                node = node.strip()
                if ":" in node:
                    host, port = node.split(":", 1)
                    startup_nodes.append({"host": host, "port": int(port)})
            
            if startup_nodes:
                logger.info(f"使用 Redis 集群模式，节点: {startup_nodes}")
                return redis.RedisCluster(
                    startup_nodes=startup_nodes,
                    password=config.get("REDIS_PASSWORD"),
                    max_connections=int(config.get("REDIS_MAX_CONNECTIONS", 50)),
                    decode_responses=True
                )
        
        # 如果没有配置集群节点，回退到单节点集群
        logger.warning("REDIS_USE_CLUSTER=true 但未配置 REDIS_CLUSTER_NODES，使用单节点集群")
        return redis.RedisCluster(
            host=config["REDIS_HOST"],
            port=int(config["REDIS_PORT"]),
            password=config.get("REDIS_PASSWORD"),
            max_connections=int(config.get("REDIS_MAX_CONNECTIONS", 50))
        )
    else:
        # 单机模式
        logger.info(f"使用 Redis 单机模式: {config['REDIS_HOST']}:{config['REDIS_PORT']}")
        return redis.Redis(
            host=config["REDIS_HOST"],
            port=int(config["REDIS_PORT"]),
            password=config.get("REDIS_PASSWORD"),
            max_connections=int(config.get("REDIS_MAX_CONNECTIONS", 50)),
            decode_responses=True
        )


class AsyncRedis:
    def __init__(self):
        self.prefix = config.get("REDIS_PREFIX")
        self.client = _create_async_client()

    def _add_prefix(self, key):
        if isinstance(key, bytes):
            key = key.decode()
        return f"{self.prefix}:{key}"

    async def set(self, key, value, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.set(key, value, *args, **kwargs)

    async def get(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.get(key, *args, **kwargs)

    async def exists(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.exists(key, *args, **kwargs)

    async def enqueue(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.rpush(key, *args, **kwargs)

    async def dequeue(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.lpop(key, *args, **kwargs)

    async def delete(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return await self.client.delete(key, *args, **kwargs)

    async def close(self):
        """关闭连接"""
        await self.client.close()


class Redis:
    def __init__(self):
        self.prefix = config.get("REDIS_PREFIX")
        self.client = _create_sync_client()

    def _add_prefix(self, key):
        if isinstance(key, bytes):
            key = key.decode()
        return f"{self.prefix}:{key}"

    def set(self, key, value, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.set(key, value, *args, **kwargs)

    def get(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.get(key, *args, **kwargs)

    def exists(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.exists(key, *args, **kwargs)

    def enqueue(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.rpush(key, *args, **kwargs)

    def dequeue(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.lpop(key, *args, **kwargs)

    def delete(self, key, *args, **kwargs):
        key = self._add_prefix(key)
        return self.client.delete(key, *args, **kwargs)

    def close(self):
        """关闭连接"""
        self.client.close()


# 创建全局实例（可选）
_async_redis_instance = None
_sync_redis_instance = None


def get_async_redis() -> AsyncRedis:
    """获取异步 Redis 实例（单例）"""
    global _async_redis_instance
    if _async_redis_instance is None:
        _async_redis_instance = AsyncRedis()
    return _async_redis_instance


def get_redis() -> Redis:
    """获取同步 Redis 实例（单例）"""
    global _sync_redis_instance
    if _sync_redis_instance is None:
        _sync_redis_instance = Redis()
    return _sync_redis_instance


__all__ = ["AsyncRedis", "Redis", "get_async_redis", "get_redis"]