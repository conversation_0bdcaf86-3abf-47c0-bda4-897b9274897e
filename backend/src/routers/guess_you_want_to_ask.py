from fastapi import APIRouter, Depends
from fastapi.exceptions import HTTPException
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from typing import Annotated

from src.financial_advisor.api.customer import async_query_customer_basic_info
from src.financial_advisor.api.product import async_query_products
from src.utils.auth_utils import verify_token
from src.utils.conversation_utils import generate_similar_question


guess_you_want_to_ask_router = APIRouter(prefix="/guess_you_want_to_ask")
security = HTTPBearer()


@guess_you_want_to_ask_router.get("")
async def guess_you_want_to_ask(
    thread_id: str,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        data = await generate_similar_question(
            thread_id=thread_id,
            access_token=token.credentials
        )
        return {"message": "User question generated successfully", "data": data}
    except Exception as e:
        return {"message": "Error generating user question", "error": str(e)}


__all__ = ["guess_you_want_to_ask_router"]


@guess_you_want_to_ask_router.get("/hook")
async def guess_you_want_to_ask_hook(
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        try:
            user_info = await verify_token(token.credentials)
        except Exception as e:
            raise HTTPException(status_code=401, detail="Unauthorized")

        users = await async_query_customer_basic_info(user_info)
        products = await async_query_products(user_info)

        data = [
            {
                "category": "智能问答",
                "scene": "问客户信息",
                "question": f"介绍一下客户{users[0].customer_name}的基本信息",
            },
            {
                "category": "智能问答",
                "scene": "问客户信息",
                "question": f"介绍一下客户{users[0].customer_name}的风测信息",
            },
            {
                "category": "智能问答",
                "scene": "问客户信息",
                "question": f"介绍一下客户{users[0].customer_name}的持仓信息",
            },
            {
                "category": "智能问答",
                "scene": "问信托产品",
                "question": f"查询{products[0].product_name}的起投金额、风险等级和业绩比较基准",
            },
            {
                "category": "智能问答",
                "scene": "问信托产品",
                "question": f"对比{products[0].product_name}和{products[1].product_name}的产品信息",
            },
            # {
            #     "category": "智能投顾",
            #     "scene": "财富规划",
            #     "question": "有个新客户叫李华，帮我做个新的规划方案，他大概30岁左右，手里有很充裕的现金流动性，对于投资有自己的见解，高知人群，希望一些稳定低波动的产品。",
            # },
            # {
            #     "category": "智能投顾",
            #     "scene": "标准组合",
            #     "question": "我有一个用户李华，客户ID000006596195，给他推荐个标准化组合",
            # },
        ]
        return {"message": "User question generated successfully", "data": data}
    except Exception as e:
        return {"message": "Error generating user question", "error": str(e)}
