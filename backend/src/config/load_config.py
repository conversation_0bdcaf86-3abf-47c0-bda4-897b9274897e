import os

from dotenv import load_dotenv
from typing import overload

load_dotenv()


@overload
def load_config(keys: str) -> str:
    pass


@overload
def load_config(keys: list[str]) -> dict[str, str]:
    pass


def load_config(keys: list[str] | str) -> dict[str, str] | str:
    if type(keys) is str:
        if keys not in os.environ:
            raise ValueError(f"Missing environment variable: {keys}")
        return os.environ[keys]

    config: dict[str, str] = {}
    for key in keys:
        if key not in os.environ:
            raise ValueError(f"Missing environment variable: {key}")
        config[key] = os.environ[key]
    return config


__all__ = ["load_config"]
