from datetime import datetime
from typing import Optional

from sqlalchemy import Column, Integer, String, DateTime, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

Base = declarative_base()


class UploadFileRecord(Base):
    """文件上传记录表模型"""
    __tablename__ = 'upload_file_record'
    
    record_id = Column(Integer, primary_key=True, autoincrement=True)
    file_name = Column(String(240), nullable=False, comment='文件名')
    file_path = Column(String(500), nullable=True, comment='文件路径')
    dataset_id = Column(String(144), nullable=False, comment='知识库ID')
    document_id = Column(String(144), nullable=False, comment='文档ID')
    source = Column(String(24), nullable=False, comment='来源：local/wealth_app')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    def __repr__(self):
        return f"<UploadFileRecord(file_name='{self.file_name}', dataset_id='{self.dataset_id}')>"


def create_tables(engine):
    """创建数据表"""
    Base.metadata.create_all(engine)


def get_session(database_url: str):
    """获取数据库会话"""
    engine = create_engine(database_url)
    Session = sessionmaker(bind=engine)
    return Session()