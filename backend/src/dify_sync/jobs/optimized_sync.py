"""
优化的同步模块 - 支持大量文件的高效同步
"""

import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
from concurrent.futures import ThreadPoolExecutor
import hashlib

from loguru import logger
from sqlalchemy import create_engine, text
from sqlalchemy.orm import Session, sessionmaker

from src.config.load_config import load_config
from src.dify_sync.models.upload_record import UploadFileRecord, get_session
from src.dify_sync.utils.batch_processor import BatchProcessor, ProcessResult
from src.dify_sync.utils.checkpoint_manager import CheckpointManager
from src.dify_sync.utils.db_utils import get_mysql_engine
from src.dify_sync.utils.file_classifier import FileClassifier
from src.financial_advisor.jobs.upload_file_to_dify import upload_file_to_dify


class OptimizedLocalFolderSync:
    """优化的本地文件夹同步器 - 支持大量文件"""
    
    def __init__(
        self, 
        sync_folder: str,
        batch_size: int = 100,
        max_workers: int = 5,
        enable_checkpoint: bool = True,
        rate_limit: Optional[int] = None
    ):
        """
        初始化优化的同步器
        
        Args:
            sync_folder: 同步文件夹路径
            batch_size: 批处理大小
            max_workers: 最大工作线程数
            enable_checkpoint: 是否启用断点续传
            rate_limit: 速率限制（每秒上传数量）
        """
        self.sync_folder = Path(sync_folder)
        if not self.sync_folder.exists():
            raise ValueError(f"同步文件夹不存在: {sync_folder}")
        
        self.batch_size = batch_size
        self.max_workers = max_workers
        self.enable_checkpoint = enable_checkpoint
        self.rate_limit = rate_limit
        
        self.classifier = FileClassifier()
        self.db_engine = get_mysql_engine()
        
        # 创建会话工厂
        self.Session = sessionmaker(bind=self.db_engine)
        
        # 断点管理器
        self.checkpoint_manager = CheckpointManager() if enable_checkpoint else None
        
        # 批处理器
        self.batch_processor = BatchProcessor(
            batch_size=batch_size,
            max_workers=max_workers,
            rate_limit=rate_limit,
            show_progress=True
        )
        
        # 缓存已上传文件名，减少数据库查询
        self._uploaded_files_cache: Optional[Set[str]] = None
    
    def _load_uploaded_files_cache(self):
        """加载已上传文件缓存"""
        logger.info("加载已上传文件列表到缓存...")
        with self.Session() as session:
            # 批量查询所有已上传文件名
            result = session.query(UploadFileRecord.file_name).all()
            self._uploaded_files_cache = {row[0] for row in result}
        logger.info(f"缓存加载完成，已上传文件数: {len(self._uploaded_files_cache)}")
    
    def _get_files_to_sync(self) -> List[Tuple[Path, str]]:
        """
        获取需要同步的文件列表
        
        Returns:
            [(文件路径, 文件ID), ...] 的列表
        """
        supported_extensions = {'.pdf', '.doc', '.docx', '.txt', '.ppt', '.pptx', '.xls', '.xlsx'}
        files_to_sync = []
        
        # 加载缓存
        if self._uploaded_files_cache is None:
            self._load_uploaded_files_cache()
        
        logger.info("扫描文件夹中的文件...")
        for ext in supported_extensions:
            for file_path in self.sync_folder.rglob(f"*{ext}"):
                filename = file_path.name
                
                # 快速检查缓存
                if filename not in self._uploaded_files_cache:
                    # 生成文件ID（用于断点续传）
                    file_id = self._generate_file_id(file_path)
                    files_to_sync.append((file_path, file_id))
        
        logger.info(f"找到 {len(files_to_sync)} 个需要同步的文件")
        return files_to_sync
    
    def _generate_file_id(self, file_path: Path) -> str:
        """生成文件唯一ID"""
        # 使用相对路径和文件名生成ID
        relative_path = file_path.relative_to(self.sync_folder)
        return hashlib.md5(str(relative_path).encode()).hexdigest()
    
    def _process_file(self, file_info: Tuple[Path, str], checkpoint=None) -> ProcessResult:
        """
        处理单个文件
        
        Args:
            file_info: (文件路径, 文件ID)
            checkpoint: 断点对象
            
        Returns:
            处理结果
        """
        file_path, file_id = file_info
        
        # 检查断点
        if checkpoint and checkpoint.is_processed(file_id):
            return ProcessResult(success=True, data={"action": "skipped_checkpoint"})
        
        try:
            # 获取新的数据库会话
            with self.Session() as session:
                filename = file_path.name
                
                # 二次检查数据库（防止并发问题）
                if self._is_file_uploaded(session, filename):
                    # 更新缓存
                    self._uploaded_files_cache.add(filename)
                    return ProcessResult(success=True, data={"action": "skipped_exists"})
                
                # 分类文件
                dataset_name, dataset_id = self.classifier.classify_file(filename)
                if not dataset_id:
                    return ProcessResult(
                        success=False, 
                        data={"action": "unclassified", "filename": filename}
                    )
                
                # 上传文件
                document_id = self._upload_file(file_path, dataset_id)
                if document_id:
                    # 保存记录
                    self._save_upload_record(
                        session, filename, str(file_path), dataset_id, document_id
                    )
                    
                    # 更新缓存
                    self._uploaded_files_cache.add(filename)
                    
                    # 更新断点
                    if checkpoint:
                        checkpoint.add_processed_item(file_id)
                    
                    return ProcessResult(
                        success=True,
                        data={"action": "uploaded", "document_id": document_id}
                    )
                else:
                    return ProcessResult(
                        success=False,
                        data={"action": "upload_failed", "filename": filename}
                    )
                    
        except Exception as e:
            logger.error(f"处理文件异常 {file_path}: {str(e)}")
            if checkpoint:
                checkpoint.add_failed_item(file_id, str(e))
            return ProcessResult(success=False, error=e)
    
    def _is_file_uploaded(self, session: Session, filename: str) -> bool:
        """检查文件是否已上传"""
        record = session.query(UploadFileRecord).filter(
            UploadFileRecord.file_name == filename
        ).first()
        return record is not None
    
    def _upload_file(self, file_path: Path, dataset_id: str) -> Optional[str]:
        """上传文件到Dify"""
        try:
            response = upload_file_to_dify(dataset_id, str(file_path))
            
            if response.get("document"):
                document_id = response["document"]["id"]
                return document_id
            else:
                logger.error(f"文件上传失败: {file_path.name}, 响应: {response}")
                return None
                
        except Exception as e:
            logger.error(f"上传文件异常: {file_path.name}, 错误: {str(e)}")
            return None
    
    def _save_upload_record(
        self, 
        session: Session, 
        filename: str, 
        file_path: str, 
        dataset_id: str, 
        document_id: str
    ):
        """保存上传记录"""
        record = UploadFileRecord(
            file_name=filename,
            file_path=file_path,
            dataset_id=dataset_id,
            document_id=document_id,
            source="local"
        )
        session.add(record)
        session.commit()
    
    def sync(self, task_id: Optional[str] = None, dry_run: bool = False) -> dict:
        """
        执行同步任务
        
        Args:
            task_id: 任务ID（用于断点续传）
            dry_run: 是否模拟运行
            
        Returns:
            同步统计结果
        """
        # 生成任务ID
        if not task_id:
            task_id = f"local_sync_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 创建断点
        checkpoint = None
        if self.enable_checkpoint:
            checkpoint = self.checkpoint_manager.create_checkpoint(task_id)
            checkpoint.set_status("running")
            checkpoint.set_metadata("sync_folder", str(self.sync_folder))
        
        try:
            # 获取需要同步的文件
            files_to_sync = self._get_files_to_sync()
            
            if checkpoint:
                checkpoint.update_progress(total=len(files_to_sync))
                
                # 过滤已处理的文件
                processed_items = checkpoint.get_processed_items()
                files_to_sync = [
                    (path, fid) for path, fid in files_to_sync 
                    if fid not in processed_items
                ]
                logger.info(f"断点续传: 跳过已处理文件 {len(processed_items)} 个")
            
            if not files_to_sync:
                logger.info("没有需要同步的文件")
                if checkpoint:
                    checkpoint.complete()
                return {
                    "total_files": 0,
                    "uploaded": 0,
                    "skipped": 0,
                    "failed": 0,
                    "unclassified": 0
                }
            
            if dry_run:
                logger.info(f"[模拟运行] 将同步 {len(files_to_sync)} 个文件")
                return {
                    "total_files": len(files_to_sync),
                    "uploaded": len(files_to_sync),
                    "skipped": 0,
                    "failed": 0,
                    "unclassified": 0
                }
            
            # 定义处理函数
            def process_func(file_info):
                return self._process_file(file_info, checkpoint)
            
            # 批量处理文件
            logger.info(f"开始批量处理 {len(files_to_sync)} 个文件...")
            batch_stats = self.batch_processor.process_items(
                items=files_to_sync,
                process_func=process_func
            )
            
            # 统计结果
            stats = {
                "total_files": batch_stats["total"],
                "uploaded": 0,
                "skipped": 0,
                "failed": batch_stats["failed"],
                "unclassified": 0
            }
            
            # 分析处理结果
            for i in range(batch_stats["success"]):
                # 这里可以通过checkpoint获取详细统计
                pass
            
            stats["uploaded"] = batch_stats["success"] - stats["skipped"]
            
            # 完成断点
            if checkpoint:
                checkpoint.update_progress(
                    processed=batch_stats["processed"],
                    success=batch_stats["success"],
                    failed=batch_stats["failed"]
                )
                checkpoint.complete()
            
            logger.info(f"同步完成: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"同步任务失败: {str(e)}")
            if checkpoint:
                checkpoint.fail(str(e))
            raise
    
    def resume_sync(self, task_id: str) -> dict:
        """恢复中断的同步任务"""
        logger.info(f"恢复同步任务: {task_id}")
        return self.sync(task_id=task_id)
    
    def cleanup_cache(self):
        """清理缓存"""
        self._uploaded_files_cache = None
        logger.info("文件缓存已清理")


def sync_local_folder_optimized(
    folder_path: Optional[str] = None,
    batch_size: int = 100,
    max_workers: int = 5,
    enable_checkpoint: bool = True,
    rate_limit: Optional[int] = None,
    task_id: Optional[str] = None,
    dry_run: bool = False
) -> dict:
    """
    优化的本地文件夹同步函数
    
    Args:
        folder_path: 文件夹路径
        batch_size: 批处理大小
        max_workers: 最大工作线程数
        enable_checkpoint: 是否启用断点续传
        rate_limit: 速率限制
        task_id: 任务ID（用于恢复）
        dry_run: 模拟运行
        
    Returns:
        同步统计结果
    """
    if not folder_path:
        config = load_config(["DIFY_SYNC_LOCAL_FOLDER"])
        folder_path = config.get("DIFY_SYNC_LOCAL_FOLDER")
        if not folder_path:
            raise ValueError("未配置本地同步文件夹路径 DIFY_SYNC_LOCAL_FOLDER")
    
    syncer = OptimizedLocalFolderSync(
        sync_folder=folder_path,
        batch_size=batch_size,
        max_workers=max_workers,
        enable_checkpoint=enable_checkpoint,
        rate_limit=rate_limit
    )
    
    if task_id:
        return syncer.resume_sync(task_id)
    else:
        return syncer.sync(dry_run=dry_run)