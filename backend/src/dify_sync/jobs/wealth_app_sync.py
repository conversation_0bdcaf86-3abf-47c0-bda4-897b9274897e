import os
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

from loguru import logger
from sqlalchemy.orm import Session

from src.config.load_config import load_config
from src.dify_sync.api.wealth_app_client import WealthAppClient
from src.dify_sync.models.upload_record import UploadFileRecord, get_session
from src.dify_sync.utils.db_utils import get_mysql_engine
from src.dify_sync.utils.file_classifier import FileClassifier
from src.financial_advisor.jobs.upload_file_to_dify import upload_file_to_dify


class WealthAppSync:
    """财富APP文件同步到Dify知识库"""
    
    def __init__(self):
        """初始化财富APP同步器"""
        # 加载配置
        config = load_config([
            "WEALTH_APP_BASE_URL",
            "WEALTH_APP_SECRET_KEY"
        ])
        
        # 获取基础URL和密钥
        base_url = config.get("WEALTH_APP_BASE_URL")
        secret_key = config.get("WEALTH_APP_SECRET_KEY")
        
        if not base_url:
            raise ValueError("未配置财富APP基础URL WEALTH_APP_BASE_URL")
        if not secret_key:
            raise ValueError("未配置财富APP密钥 WEALTH_APP_SECRET_KEY")
        
        self.client = WealthAppClient(base_url, secret_key)
        self.classifier = FileClassifier()
        self.db_engine = get_mysql_engine()
        
        # 创建临时目录用于存储下载的文件
        self.temp_dir = tempfile.mkdtemp(prefix="wealth_app_sync_")
        logger.info(f"临时文件目录: {self.temp_dir}")
    
    def __del__(self):
        """清理临时目录"""
        if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
            import shutil
            shutil.rmtree(self.temp_dir)
            logger.info(f"已清理临时目录: {self.temp_dir}")
    
    def _is_file_uploaded(self, session: Session, filename: str) -> bool:
        """检查文件是否已上传"""
        record = session.query(UploadFileRecord).filter(
            UploadFileRecord.file_name == filename
        ).first()
        return record is not None
    
    def _upload_file(self, file_path: str, dataset_id: str) -> Optional[str]:
        """上传文件到Dify"""
        try:
            response = upload_file_to_dify(dataset_id, file_path)
            
            if response.get("document"):
                document_id = response["document"]["id"]
                logger.info(f"文件上传成功: {os.path.basename(file_path)} -> 文档ID: {document_id}")
                return document_id
            else:
                logger.error(f"文件上传失败: {os.path.basename(file_path)}, 响应: {response}")
                return None
                
        except Exception as e:
            logger.error(f"上传文件异常: {os.path.basename(file_path)}, 错误: {str(e)}")
            return None
    
    def _save_upload_record(self, session: Session, filename: str, file_path: str, dataset_id: str, document_id: str):
        """保存上传记录到数据库"""
        record = UploadFileRecord(
            file_name=filename,
            file_path=file_path,
            dataset_id=dataset_id,
            document_id=document_id,
            source="wealth_app"
        )
        session.add(record)
        session.commit()
        logger.info(f"上传记录已保存: {filename}")
    
    def _process_file(self, session: Session, file_info: Dict, dry_run: bool = False) -> Dict[str, int]:
        """
        处理单个文件
        
        Returns:
            处理结果统计
        """
        stats = {"uploaded": 0, "skipped": 0, "failed": 0, "unclassified": 0}
        filename = file_info["fileName"]
        
        # 检查是否已上传
        if self._is_file_uploaded(session, filename):
            logger.info(f"文件已存在，跳过: {filename}")
            stats["skipped"] = 1
            return stats
        
        # 分类文件
        dataset_name, dataset_id = self.classifier.classify_file(filename)
        if not dataset_id:
            logger.warning(f"无法分类文件，跳过: {filename}")
            stats["unclassified"] = 1
            return stats
        
        if dry_run:
            logger.info(f"[模拟] 将上传文件: {filename} -> {dataset_name}")
            stats["uploaded"] = 1
            return stats
        
        try:
            # 下载文件到临时目录
            file_path = self.client.save_file(file_info, self.temp_dir)
            
            # 上传到Dify
            document_id = self._upload_file(file_path, dataset_id)
            if document_id:
                # 保存上传记录，保存文件路径用于记录
                self._save_upload_record(session, filename, file_path, dataset_id, document_id)
                stats["uploaded"] = 1
            else:
                stats["failed"] = 1
                
            # 删除临时文件
            os.remove(file_path)
            
        except Exception as e:
            logger.error(f"处理文件失败: {filename}, 错误: {str(e)}")
            stats["failed"] = 1
            
        return stats
    
    def sync(self, query_date: Optional[datetime] = None, dry_run: bool = False) -> dict:
        """
        执行同步任务
        
        Args:
            query_date: 查询日期，默认为最近一个月
            dry_run: 是否只进行模拟运行
            
        Returns:
            同步结果统计
        """
        if query_date is None:
            query_date = datetime.now() - timedelta(days=30)
            
        logger.info(f"开始同步财富APP文件，查询日期: {query_date.strftime('%Y-%m-%d')}")
        
        stats = {
            "total_files": 0,
            "uploaded": 0,
            "skipped": 0,
            "failed": 0,
            "unclassified": 0
        }
        
        # 获取所有文件
        try:
            all_files = self.client.get_all_files(query_date)
            stats["total_files"] = len(all_files)
            
            if not all_files:
                logger.info("没有找到需要同步的文件")
                return stats
                
        except Exception as e:
            logger.error(f"获取文件列表失败: {str(e)}")
            raise
        
        # 获取数据库会话
        session = get_session(self.db_engine.url)
        
        try:
            for file_info in all_files:
                # 处理单个文件
                file_stats = self._process_file(session, file_info, dry_run)
                
                # 更新统计
                for key in ["uploaded", "skipped", "failed", "unclassified"]:
                    stats[key] += file_stats.get(key, 0)
                    
        except Exception as e:
            logger.error(f"同步过程出错: {str(e)}")
            raise
        finally:
            session.close()
        
        # 输出统计信息
        logger.info(f"同步完成: 总文件数={stats['total_files']}, "
                   f"已上传={stats['uploaded']}, "
                   f"已跳过={stats['skipped']}, "
                   f"失败={stats['failed']}, "
                   f"未分类={stats['unclassified']}")
        
        return stats


def sync_wealth_app_files(query_date: Optional[datetime] = None, dry_run: bool = False):
    """
    同步财富APP文件的便捷函数
    
    Args:
        query_date: 查询日期，默认为最近一个月
        dry_run: 是否只进行模拟运行
    """
    syncer = WealthAppSync()
    return syncer.sync(query_date=query_date, dry_run=dry_run)