import os
from datetime import datetime
from pathlib import Path
from typing import List, Optional

from loguru import logger
from sqlalchemy.orm import Session

from src.config.load_config import load_config
from src.dify_sync.models.upload_record import UploadFileRecord, get_session
from src.dify_sync.utils.db_utils import get_mysql_engine
from src.dify_sync.utils.file_classifier import FileClassifier
from src.financial_advisor.jobs.upload_file_to_dify import upload_file_to_dify


class LocalFolderSync:
    """本地文件夹同步到Dify知识库"""
    
    def __init__(self, sync_folder: str):
        """
        初始化本地文件夹同步器
        
        Args:
            sync_folder: 要同步的文件夹路径
        """
        self.sync_folder = Path(sync_folder)
        if not self.sync_folder.exists():
            raise ValueError(f"同步文件夹不存在: {sync_folder}")
            
        self.classifier = FileClassifier()
        self.db_engine = get_mysql_engine()
        
    def _get_supported_files(self) -> List[Path]:
        """
        递归获取支持的文件类型
        
        Returns:
            文件路径列表
        """
        supported_extensions = {'.pdf', '.doc', '.docx', '.txt', '.ppt', '.pptx', '.xls', '.xlsx'}
        files = []
        
        for ext in supported_extensions:
            files.extend(self.sync_folder.rglob(f"*{ext}"))
            
        logger.info(f"找到 {len(files)} 个支持的文件")
        return files
    
    def _is_file_uploaded(self, session: Session, filename: str) -> bool:
        """
        检查文件是否已上传
        
        Args:
            session: 数据库会话
            filename: 文件名
            
        Returns:
            是否已上传
        """
        record = session.query(UploadFileRecord).filter(
            UploadFileRecord.file_name == filename
        ).first()
        return record is not None
    
    def _upload_file(self, file_path: Path, dataset_id: str) -> Optional[str]:
        """
        上传文件到Dify
        
        Args:
            file_path: 文件路径
            dataset_id: 知识库ID
            
        Returns:
            文档ID，如果上传失败返回None
        """
        try:
            response = upload_file_to_dify(dataset_id, str(file_path))
            
            if response.get("document"):
                document_id = response["document"]["id"]
                logger.info(f"文件上传成功: {file_path.name} -> 文档ID: {document_id}")
                return document_id
            else:
                logger.error(f"文件上传失败: {file_path.name}, 响应: {response}")
                return None
                
        except Exception as e:
            logger.error(f"上传文件异常: {file_path.name}, 错误: {str(e)}")
            return None
    
    def _save_upload_record(self, session: Session, filename: str, file_path: str, dataset_id: str, document_id: str):
        """保存上传记录到数据库"""
        record = UploadFileRecord(
            file_name=filename,
            file_path=file_path,
            dataset_id=dataset_id,
            document_id=document_id,
            source="local"
        )
        session.add(record)
        session.commit()
        logger.info(f"上传记录已保存: {filename}")
    
    def sync(self, dry_run: bool = False) -> dict:
        """
        执行同步任务
        
        Args:
            dry_run: 是否只进行模拟运行（不实际上传）
            
        Returns:
            同步结果统计
        """
        stats = {
            "total_files": 0,
            "uploaded": 0,
            "skipped": 0,
            "failed": 0,
            "unclassified": 0
        }
        
        # 获取所有支持的文件
        files = self._get_supported_files()
        stats["total_files"] = len(files)
        
        if not files:
            logger.info("没有找到需要同步的文件")
            return stats
        
        # 获取数据库会话
        session = get_session(self.db_engine.url)
        
        try:
            for file_path in files:
                filename = file_path.name
                
                # 检查是否已上传
                if self._is_file_uploaded(session, filename):
                    logger.info(f"文件已存在，跳过: {filename}")
                    stats["skipped"] += 1
                    continue
                
                # 分类文件
                dataset_name, dataset_id = self.classifier.classify_file(filename)
                if not dataset_id:
                    logger.warning(f"无法分类文件，跳过: {filename}")
                    stats["unclassified"] += 1
                    continue
                
                if dry_run:
                    logger.info(f"[模拟] 将上传文件: {filename} -> {dataset_name}")
                    stats["uploaded"] += 1
                    continue
                
                # 上传文件
                document_id = self._upload_file(file_path, dataset_id)
                if document_id:
                    # 保存上传记录
                    self._save_upload_record(session, filename, str(file_path), dataset_id, document_id)
                    stats["uploaded"] += 1
                else:
                    stats["failed"] += 1
                    
        except Exception as e:
            logger.error(f"同步过程出错: {str(e)}")
            raise
        finally:
            session.close()
        
        # 输出统计信息
        logger.info(f"同步完成: 总文件数={stats['total_files']}, "
                   f"已上传={stats['uploaded']}, "
                   f"已跳过={stats['skipped']}, "
                   f"失败={stats['failed']}, "
                   f"未分类={stats['unclassified']}")
        
        return stats


def sync_local_folder(folder_path: Optional[str] = None, dry_run: bool = False):
    """
    同步本地文件夹的便捷函数
    
    Args:
        folder_path: 文件夹路径，如果不提供则从配置中读取
        dry_run: 是否只进行模拟运行
    """
    if not folder_path:
        config = load_config(["DIFY_SYNC_LOCAL_FOLDER"])
        folder_path = config.get("DIFY_SYNC_LOCAL_FOLDER")
        if not folder_path:
            raise ValueError("未配置本地同步文件夹路径 DIFY_SYNC_LOCAL_FOLDER")
    
    syncer = LocalFolderSync(folder_path)
    return syncer.sync(dry_run=dry_run)