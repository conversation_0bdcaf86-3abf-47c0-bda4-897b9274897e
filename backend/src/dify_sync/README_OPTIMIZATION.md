# Dify Sync 优化方案

## 概述

当需要同步大量文件（如超过10000个）时，原始的同步方案会遇到以下问题：

1. **内存占用过高** - 一次性加载所有文件信息
2. **处理速度慢** - 串行处理每个文件
3. **无法中断恢复** - 中断后需要重新开始
4. **数据库查询频繁** - 每个文件都要查询数据库

## 优化方案

### 1. 批量处理和并发优化

使用 `BatchProcessor` 实现：
- 将文件分批处理，减少内存占用
- 多线程并发上传，提高处理速度
- 支持速率限制，避免服务器过载

### 2. 断点续传功能

使用 `CheckpointManager` 实现：
- 保存同步进度到本地文件
- 支持中断后从断点恢复
- 记录失败文件，便于重试

### 3. 缓存优化

- 预加载已上传文件列表到内存缓存
- 减少数据库查询次数
- 支持缓存刷新

### 4. 进度可视化

- 使用 `tqdm` 显示实时进度
- 使用 `rich` 美化命令行输出
- 实时统计成功/失败数量

## 使用方法

### 1. 基本同步

```bash
# 使用默认配置同步
python -m src.dify_sync.cli sync

# 指定文件夹和参数
python -m src.dify_sync.cli sync \
    --folder /path/to/files \
    --batch-size 200 \
    --workers 10 \
    --rate-limit 50
```

### 2. 断点续传

```bash
# 查看所有断点
python -m src.dify_sync.cli list

# 恢复中断的任务
python -m src.dify_sync.cli resume task_id_here
```

### 3. 清理旧断点

```bash
# 清理7天前的断点（默认）
python -m src.dify_sync.cli clean

# 清理30天前的断点
python -m src.dify_sync.cli clean --days 30
```

## 参数说明

- `--batch-size`: 批处理大小，默认100。增大可以提高效率，但会占用更多内存
- `--workers`: 并发工作线程数，默认5。根据服务器性能调整
- `--rate-limit`: 速率限制（每秒上传数），避免服务器过载
- `--no-checkpoint`: 禁用断点续传功能
- `--dry-run`: 模拟运行，不实际上传文件

## 性能调优建议

### 处理10000+文件的推荐配置

```bash
python -m src.dify_sync.cli sync \
    --batch-size 500 \
    --workers 20 \
    --rate-limit 100
```

### 处理100000+文件的推荐配置

```bash
python -m src.dify_sync.cli sync \
    --batch-size 1000 \
    --workers 50 \
    --rate-limit 200
```

## 代码使用示例

```python
from src.dify_sync.jobs.optimized_sync import sync_local_folder_optimized

# 同步文件夹
stats = sync_local_folder_optimized(
    folder_path="/path/to/files",
    batch_size=500,
    max_workers=20,
    enable_checkpoint=True,
    rate_limit=100
)

print(f"同步完成: {stats}")

# 恢复中断的任务
stats = sync_local_folder_optimized(
    task_id="local_sync_20241201_120000"
)
```

## 监控和日志

同步过程会生成详细的日志，包括：
- 处理进度
- 错误信息
- 性能统计

日志文件位置：`logs/dify_sync.log`

## 故障排除

### 1. 内存不足

减小 `batch_size` 参数：
```bash
python -m src.dify_sync.cli sync --batch-size 50
```

### 2. 上传失败过多

- 检查网络连接
- 减小 `workers` 数量
- 启用 `rate_limit` 限制

### 3. 断点恢复失败

检查断点文件是否存在：
```bash
ls .dify_sync_checkpoints/
```

## 扩展功能

### 1. 自定义文件过滤

修改 `_get_files_to_sync` 方法添加自定义过滤逻辑。

### 2. 自定义上传策略

继承 `OptimizedLocalFolderSync` 类并重写 `_upload_file` 方法。

### 3. 集成到定时任务

```bash
# crontab -e
0 2 * * * cd /path/to/project && python -m src.dify_sync.cli sync >> /var/log/dify_sync.log 2>&1
```

## 性能基准测试

在标准配置下的性能表现：

| 文件数量 | 批处理大小 | 工作线程 | 平均耗时 | 内存占用 |
|---------|----------|---------|---------|---------|
| 1,000   | 100      | 5       | 5分钟    | 100MB   |
| 10,000  | 500      | 20      | 30分钟   | 500MB   |
| 100,000 | 1000     | 50      | 4小时    | 2GB     |

注：实际性能取决于文件大小、网络状况和服务器性能。