"""
Dify Sync 命令行工具 - 支持大规模文件同步
"""

import argparse
import sys
from datetime import datetime
from pathlib import Path

from loguru import logger
from rich.console import Console
from rich.table import Table

from src.dify_sync.jobs.optimized_sync import sync_local_folder_optimized
from src.dify_sync.utils.checkpoint_manager import CheckpointManager


console = Console()


def list_checkpoints(args):
    """列出所有断点"""
    manager = CheckpointManager()
    checkpoints = manager.list_checkpoints()
    
    if not checkpoints:
        console.print("[yellow]没有找到断点记录[/yellow]")
        return
    
    # 创建表格
    table = Table(title="同步断点列表")
    table.add_column("任务ID", style="cyan")
    table.add_column("状态", style="green")
    table.add_column("进度", style="yellow")
    table.add_column("创建时间", style="magenta")
    table.add_column("更新时间", style="magenta")
    
    for cp in checkpoints:
        progress = cp["progress"]
        progress_str = f"{progress.get('processed', 0)}/{progress.get('total', 0)}"
        
        table.add_row(
            cp["task_id"],
            cp["status"],
            progress_str,
            cp["created_at"][:19],
            cp["updated_at"][:19]
        )
    
    console.print(table)


def sync_folder(args):
    """同步文件夹"""
    try:
        console.print(f"[blue]开始同步文件夹: {args.folder or '默认文件夹'}[/blue]")
        console.print(f"批处理大小: {args.batch_size}")
        console.print(f"工作线程数: {args.workers}")
        console.print(f"速率限制: {args.rate_limit or '无限制'}")
        console.print(f"断点续传: {'启用' if args.checkpoint else '禁用'}")
        
        if args.dry_run:
            console.print("[yellow]模拟运行模式 - 不会实际上传文件[/yellow]")
        
        # 执行同步
        stats = sync_local_folder_optimized(
            folder_path=args.folder,
            batch_size=args.batch_size,
            max_workers=args.workers,
            enable_checkpoint=args.checkpoint,
            rate_limit=args.rate_limit,
            task_id=args.task_id,
            dry_run=args.dry_run
        )
        
        # 显示结果
        console.print("\n[green]同步完成![/green]")
        console.print(f"总文件数: {stats['total_files']}")
        console.print(f"已上传: {stats['uploaded']}")
        console.print(f"已跳过: {stats['skipped']}")
        console.print(f"失败: {stats['failed']}")
        console.print(f"未分类: {stats['unclassified']}")
        
    except Exception as e:
        console.print(f"[red]同步失败: {str(e)}[/red]")
        sys.exit(1)


def resume_sync(args):
    """恢复同步任务"""
    if not args.task_id:
        console.print("[red]请指定要恢复的任务ID[/red]")
        sys.exit(1)
    
    try:
        console.print(f"[blue]恢复同步任务: {args.task_id}[/blue]")
        
        stats = sync_local_folder_optimized(
            task_id=args.task_id,
            batch_size=args.batch_size,
            max_workers=args.workers,
            rate_limit=args.rate_limit
        )
        
        # 显示结果
        console.print("\n[green]同步完成![/green]")
        console.print(f"总文件数: {stats['total_files']}")
        console.print(f"已上传: {stats['uploaded']}")
        console.print(f"已跳过: {stats['skipped']}")
        console.print(f"失败: {stats['failed']}")
        console.print(f"未分类: {stats['unclassified']}")
        
    except Exception as e:
        console.print(f"[red]恢复同步失败: {str(e)}[/red]")
        sys.exit(1)


def clean_checkpoints(args):
    """清理旧断点"""
    manager = CheckpointManager()
    console.print(f"[yellow]清理 {args.days} 天前的断点文件...[/yellow]")
    manager.clean_old_checkpoints(days=args.days)
    console.print("[green]清理完成![/green]")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Dify Sync - 高效的文件同步工具",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # sync 命令
    sync_parser = subparsers.add_parser("sync", help="同步文件夹")
    sync_parser.add_argument("--folder", "-f", help="要同步的文件夹路径")
    sync_parser.add_argument("--batch-size", "-b", type=int, default=100, 
                           help="批处理大小 (默认: 100)")
    sync_parser.add_argument("--workers", "-w", type=int, default=5,
                           help="工作线程数 (默认: 5)")
    sync_parser.add_argument("--rate-limit", "-r", type=int,
                           help="速率限制 (每秒上传数)")
    sync_parser.add_argument("--no-checkpoint", dest="checkpoint", 
                           action="store_false", default=True,
                           help="禁用断点续传")
    sync_parser.add_argument("--task-id", "-t", help="任务ID (用于恢复)")
    sync_parser.add_argument("--dry-run", "-d", action="store_true",
                           help="模拟运行，不实际上传")
    sync_parser.set_defaults(func=sync_folder)
    
    # resume 命令
    resume_parser = subparsers.add_parser("resume", help="恢复中断的同步任务")
    resume_parser.add_argument("task_id", help="要恢复的任务ID")
    resume_parser.add_argument("--batch-size", "-b", type=int, default=100,
                             help="批处理大小 (默认: 100)")
    resume_parser.add_argument("--workers", "-w", type=int, default=5,
                             help="工作线程数 (默认: 5)")
    resume_parser.add_argument("--rate-limit", "-r", type=int,
                             help="速率限制 (每秒上传数)")
    resume_parser.set_defaults(func=resume_sync)
    
    # list 命令
    list_parser = subparsers.add_parser("list", help="列出所有断点")
    list_parser.set_defaults(func=list_checkpoints)
    
    # clean 命令
    clean_parser = subparsers.add_parser("clean", help="清理旧断点")
    clean_parser.add_argument("--days", "-d", type=int, default=7,
                            help="清理多少天前的断点 (默认: 7)")
    clean_parser.set_defaults(func=clean_checkpoints)
    
    # 解析参数
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # 执行命令
    args.func(args)


if __name__ == "__main__":
    main()