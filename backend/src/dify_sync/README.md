# Dify 知识库文件同步模块

这个模块用于将文件从不同来源同步到 Dify 知识库中。

## 功能特性

1. **支持两种文件来源**：
   - 本地文件夹：递归扫描指定目录下的文件
   - 财富APP接口：通过API获取最近的文件

2. **智能文件分类**：
   - 根据文件名中的关键词自动判断所属知识库
   - 支持7个不同类型的知识库

3. **增量同步**：
   - 通过数据库记录避免重复上传
   - 支持断点续传

## 使用方法

### 1. 配置环境变量

复制 `.env.example` 到 `.env` 并填写必要的配置：

```bash
# Dify同步配置
DIFY_SYNC_LOCAL_FOLDER=/path/to/documents  # 本地文件夹路径

# 财富APP配置  
WEALTH_APP_BASE_URL=http://***********:8888  # 必填，API地址
WEALTH_APP_SECRET_KEY=your_secret_key  # 16或32位密钥

# 使用Mock服务器进行测试
# WEALTH_APP_BASE_URL=http://localhost:8888
# WEALTH_APP_SECRET_KEY=1234567890123456
```

### 2. 创建数据库表

运行以下SQL创建上传记录表：

```sql
CREATE TABLE `upload_file_record` (
  `record_id` INT NOT NULL AUTO_INCREMENT,
  `file_name` varchar(240) NOT NULL,
  `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',
  `dataset_id` varchar(144) NOT NULL,
  `document_id` varchar(144) NOT NULL,
  `source` varchar(24) NOT NULL,
  `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`record_id`),
  KEY `idx_file_name` (`file_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 3. 命令行使用

```bash
# 同步所有来源
python -m src.dify_sync.sync_manager

# 仅同步本地文件夹
python -m src.dify_sync.sync_manager --mode local

# 仅同步财富APP文件（最近30天）
python -m src.dify_sync.sync_manager --mode wealth

# 指定查询天数
python -m src.dify_sync.sync_manager --mode wealth --days 7

# 模拟运行（不实际上传）
python -m src.dify_sync.sync_manager --dry-run
```

### 4. 在代码中使用

```python
from src.dify_sync.sync_manager import DifySyncManager

# 同步所有
stats = DifySyncManager.sync_all()

# 仅同步本地
stats = DifySyncManager.sync_local_only()

# 仅同步财富APP
stats = DifySyncManager.sync_wealth_app_only()
```

## 知识库分类规则

系统会根据文件名中的关键词自动分类到以下知识库：

- **产品培训文档库**: 推介PPT、培训材料、QA答疑等
- **投后管理报告库**: 周报、月报、净值报告、投资分析等
- **产品生命周期管理库**: 发行计划、赎回延期、营销方案等
- **资产配置策略库**: 债券研究、市场观点、策略分析等
- **合规操作运营库**: 合格投资者、消保案例、合规营销等
- **服务信托业务库**: 不动产信托、家族信托、KYC表等
- **内部工具使用库**: 操作指引、APP培训、网络安全等

## 注意事项

1. 支持的文件格式：`.pdf`, `.doc`, `.docx`, `.txt`, `.ppt`, `.pptx`, `.xls`, `.xlsx`
2. 文件名重复检查基于完整文件名，包括扩展名
3. 财富APP密钥必须是16位或32位（用于AES加密）
4. 建议定期运行同步任务，如每天凌晨执行