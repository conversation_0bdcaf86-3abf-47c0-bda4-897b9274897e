import argparse
from datetime import datetime, timed<PERSON>ta
from typing import Optional

from loguru import logger

from src.dify_sync.jobs.local_folder_sync import sync_local_folder
from src.dify_sync.jobs.wealth_app_sync import sync_wealth_app_files


class DifySyncManager:
    """Dify知识库同步管理器"""
    
    @staticmethod
    def sync_all(dry_run: bool = False) -> dict:
        """
        同步所有来源的文件
        
        Args:
            dry_run: 是否只进行模拟运行
            
        Returns:
            同步结果统计
        """
        logger.info("开始执行全部同步任务")
        
        total_stats = {
            "local": {},
            "wealth_app": {},
            "total": {
                "total_files": 0,
                "uploaded": 0,
                "skipped": 0,
                "failed": 0,
                "unclassified": 0
            }
        }
        
        # 同步本地文件夹
        try:
            logger.info("开始同步本地文件夹...")
            local_stats = sync_local_folder(dry_run=dry_run)
            total_stats["local"] = local_stats
            
            # 更新总统计
            for key in ["total_files", "uploaded", "skipped", "failed", "unclassified"]:
                total_stats["total"][key] += local_stats.get(key, 0)
                
        except Exception as e:
            logger.error(f"本地文件夹同步失败: {str(e)}")
            total_stats["local"]["error"] = str(e)
        
        # 同步财富APP文件
        try:
            logger.info("开始同步财富APP文件...")
            wealth_stats = sync_wealth_app_files(dry_run=dry_run)
            total_stats["wealth_app"] = wealth_stats
            
            # 更新总统计
            for key in ["total_files", "uploaded", "skipped", "failed", "unclassified"]:
                total_stats["total"][key] += wealth_stats.get(key, 0)
                
        except Exception as e:
            logger.error(f"财富APP文件同步失败: {str(e)}")
            total_stats["wealth_app"]["error"] = str(e)
        
        # 输出总统计
        logger.info(f"全部同步完成: {total_stats['total']}")
        
        return total_stats
    
    @staticmethod
    def sync_local_only(folder_path: Optional[str] = None, dry_run: bool = False) -> dict:
        """仅同步本地文件夹"""
        logger.info("执行本地文件夹同步任务")
        return sync_local_folder(folder_path=folder_path, dry_run=dry_run)
    
    @staticmethod
    def sync_wealth_app_only(query_date: Optional[datetime] = None, dry_run: bool = False) -> dict:
        """仅同步财富APP文件"""
        logger.info("执行财富APP文件同步任务")
        return sync_wealth_app_files(query_date=query_date, dry_run=dry_run)


def main():
    """命令行入口"""
    parser = argparse.ArgumentParser(description="Dify知识库文件同步工具")
    parser.add_argument(
        "--mode", 
        choices=["all", "local", "wealth"],
        default="all",
        help="同步模式: all-全部, local-本地文件夹, wealth-财富APP"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="模拟运行，不实际上传文件"
    )
    parser.add_argument(
        "--folder",
        type=str,
        help="指定本地文件夹路径（仅在local模式下有效）"
    )
    parser.add_argument(
        "--days",
        type=int,
        default=30,
        help="查询最近N天的文件（仅在wealth模式下有效，默认30天）"
    )
    
    args = parser.parse_args()
    
    # 设置日志
    logger.add("dify_sync_{time}.log", rotation="10 MB", retention="7 days")
    
    try:
        if args.mode == "all":
            DifySyncManager.sync_all(dry_run=args.dry_run)
        elif args.mode == "local":
            DifySyncManager.sync_local_only(folder_path=args.folder, dry_run=args.dry_run)
        elif args.mode == "wealth":
            query_date = datetime.now() - timedelta(days=args.days)
            DifySyncManager.sync_wealth_app_only(query_date=query_date, dry_run=args.dry_run)
            
    except Exception as e:
        logger.error(f"同步失败: {str(e)}")
        raise


if __name__ == "__main__":
    main()