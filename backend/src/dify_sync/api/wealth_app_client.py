import base64
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from urllib.parse import urljoin

import requests
from loguru import logger

from src.financial_advisor.api.encrypt import decrypt_aes, encrypt_aes


class WealthAppClient:
    """财富APP接口客户端"""
    
    def __init__(self, base_url: str, secret_key: str):
        """
        初始化客户端
        
        Args:
            base_url: API基础URL，如 http://10.103.30.1:8888
            secret_key: 加密密钥
        """
        self.base_url = base_url
        self.secret_key = secret_key
        self.session = requests.Session()
        
    def _encrypt_request(self, data: Dict) -> str:
        """加密请求数据"""
        json_str = json.dumps(data, ensure_ascii=False)
        return encrypt_aes(json_str, self.secret_key)
    
    def _decrypt_response(self, encrypted_data: str) -> Dict:
        """解密响应数据"""
        decrypted_str = decrypt_aes(encrypted_data, self.secret_key)
        return json.loads(decrypted_str)
    
    def _make_request(self, endpoint: str, data: Dict) -> Dict:
        """发送请求并处理响应"""
        url = urljoin(self.base_url, f"/openapi/ai/{endpoint}")
        encrypted_data = self._encrypt_request(data)
        
        try:
            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求数据: {data}")
            
            response = self.session.post(
                url,
                json={"data": encrypted_data},
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            
            # 检查响应内容
            if not response.text:
                raise Exception("服务器返回空响应")
            
            logger.debug(f"响应状态码: {response.status_code}")
            logger.debug(f"响应内容: {response.text[:200]}...")  # 只显示前200字符
            
            try:
                result = response.json()
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败，响应内容: {response.text}")
                raise Exception(f"服务器响应不是有效的JSON格式: {str(e)}")
            
            if result.get("code") != "0":
                raise Exception(f"API返回错误: {result.get('msg', '未知错误')}")
            
            # 解密响应数据
            if result.get("data"):
                return self._decrypt_response(result["data"])
            return {}
            
        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求失败 {endpoint}: {str(e)}")
            raise Exception(f"网络请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"请求处理失败 {endpoint}: {str(e)}")
            raise
    
    def query_disclosure_files(
        self, 
        query_date: Optional[datetime] = None,
        page_size: int = 20,
        page_no: int = 1
    ) -> Dict:
        """
        查询信息披露附件
        
        Args:
            query_date: 查询日期，默认为最近一个月
            page_size: 每页条数
            page_no: 页码
            
        Returns:
            包含totalCount和list的字典
        """
        if query_date is None:
            query_date = datetime.now() - timedelta(days=30)
            
        data = {
            "queryDate": query_date.strftime("%Y-%m-%d %H:%M:%S"),
            "pageSize": str(page_size),
            "pageNo": str(page_no)
        }
        
        return self._make_request("disCloseFileQry.do", data)
    
    def query_project_files(
        self,
        query_date: Optional[datetime] = None,
        page_size: int = 20,
        page_no: int = 1
    ) -> Dict:
        """
        查询项目附件
        
        Args:
            query_date: 查询日期，默认为最近一个月
            page_size: 每页条数
            page_no: 页码
            
        Returns:
            包含totalCount和list的字典
        """
        if query_date is None:
            query_date = datetime.now() - timedelta(days=30)
            
        data = {
            "queryDate": query_date.strftime("%Y-%m-%d %H:%M:%S"),
            "pageSize": str(page_size),
            "pageNo": str(page_no)
        }
        
        return self._make_request("projFileQry.do", data)
    
    def download_file(self, file_id: str, file_type: str) -> Dict:
        """
        下载附件
        
        Args:
            file_id: 文件ID
            file_type: 文件类型 0-项目附件 1-信息披露
            
        Returns:
            包含base64和fileSuffix的字典
        """
        data = {
            "fileId": file_id,
            "fileType": file_type
        }
        
        return self._make_request("downloadCrmFile.do", data)
    
    def get_all_files(self, query_date: Optional[datetime] = None) -> List[Dict]:
        """
        获取所有文件（信息披露和项目附件）
        
        Args:
            query_date: 查询日期，默认为最近一个月
            
        Returns:
            所有文件列表
        """
        all_files = []
        
        # 获取信息披露文件
        page_no = 1
        while True:
            result = self.query_disclosure_files(query_date, page_size=100, page_no=page_no)
            files = result.get("list", [])
            if not files:
                break
                
            for file in files:
                file["fileType"] = "1"  # 信息披露
                file["source"] = "disclosure"
            all_files.extend(files)
            
            total_count = int(result.get("totalCount", 0))
            if page_no * 100 >= total_count:
                break
            page_no += 1
            
        # 获取项目附件
        page_no = 1
        while True:
            result = self.query_project_files(query_date, page_size=100, page_no=page_no)
            files = result.get("list", [])
            if not files:
                break
                
            for file in files:
                file["fileType"] = "0"  # 项目附件
                file["source"] = "project"
            all_files.extend(files)
            
            total_count = int(result.get("totalCount", 0))
            if page_no * 100 >= total_count:
                break
            page_no += 1
            
        logger.info(f"共获取到 {len(all_files)} 个文件")
        return all_files
    
    def save_file(self, file_info: Dict, save_path: str) -> str:
        """
        下载并保存文件
        
        Args:
            file_info: 文件信息字典
            save_path: 保存路径
            
        Returns:
            保存的文件完整路径
        """
        # 下载文件
        download_result = self.download_file(
            file_info["fileId"],
            file_info["fileType"]
        )
        
        # 解码base64内容
        file_content = base64.b64decode(download_result["base64"])
        
        # 构建文件名
        file_suffix = download_result.get("fileSuffix", "pdf")
        file_name = file_info["fileName"]
        if not file_name.endswith(f".{file_suffix}"):
            file_name = f"{file_name}.{file_suffix}"
        
        # 保存文件
        import os
        os.makedirs(save_path, exist_ok=True)
        file_path = os.path.join(save_path, file_name)
        
        with open(file_path, "wb") as f:
            f.write(file_content)
            
        logger.info(f"文件已保存: {file_path}")
        return file_path