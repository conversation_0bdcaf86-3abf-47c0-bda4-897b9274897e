"""
批量处理器 - 用于高效处理大量文件同步
"""

import asyncio
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from typing import Any, Callable, Dict, List, Optional, Tuple
from queue import Queue
import threading

from loguru import logger
from tqdm import tqdm


@dataclass
class ProcessResult:
    """处理结果"""
    success: bool
    data: Any = None
    error: Optional[Exception] = None


class BatchProcessor:
    """批量处理器 - 支持并发处理和进度跟踪"""
    
    def __init__(
        self, 
        batch_size: int = 100,
        max_workers: int = 5,
        rate_limit: Optional[int] = None,
        show_progress: bool = True
    ):
        """
        初始化批量处理器
        
        Args:
            batch_size: 批次大小
            max_workers: 最大并发工作线程数
            rate_limit: 速率限制（每秒处理数量），None表示不限制
            show_progress: 是否显示进度条
        """
        self.batch_size = batch_size
        self.max_workers = max_workers
        self.rate_limit = rate_limit
        self.show_progress = show_progress
        
        # 速率限制相关
        self._rate_limiter = None
        if rate_limit:
            self._rate_limiter = RateLimiter(rate_limit)
    
    def process_items(
        self,
        items: List[Any],
        process_func: Callable[[Any], ProcessResult],
        error_handler: Optional[Callable[[Any, Exception], None]] = None
    ) -> Dict[str, int]:
        """
        批量处理项目
        
        Args:
            items: 要处理的项目列表
            process_func: 处理函数，接收单个项目，返回ProcessResult
            error_handler: 错误处理函数
            
        Returns:
            处理统计结果
        """
        stats = {
            "total": len(items),
            "success": 0,
            "failed": 0,
            "processed": 0
        }
        
        if not items:
            return stats
        
        # 创建进度条
        progress_bar = None
        if self.show_progress:
            progress_bar = tqdm(total=len(items), desc="Processing", unit="items")
        
        # 使用线程池进行并发处理
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 分批提交任务
            for batch_start in range(0, len(items), self.batch_size):
                batch_end = min(batch_start + self.batch_size, len(items))
                batch_items = items[batch_start:batch_end]
                
                # 提交批次任务
                futures = {}
                for item in batch_items:
                    # 速率限制
                    if self._rate_limiter:
                        self._rate_limiter.acquire()
                    
                    future = executor.submit(self._process_with_error_handling, 
                                           process_func, item, error_handler)
                    futures[future] = item
                
                # 收集结果
                for future in as_completed(futures):
                    item = futures[future]
                    try:
                        result = future.result()
                        if result.success:
                            stats["success"] += 1
                        else:
                            stats["failed"] += 1
                    except Exception as e:
                        logger.error(f"处理项目异常: {str(e)}")
                        stats["failed"] += 1
                    
                    stats["processed"] += 1
                    
                    # 更新进度条
                    if progress_bar:
                        progress_bar.update(1)
                        progress_bar.set_postfix({
                            "成功": stats["success"],
                            "失败": stats["failed"]
                        })
        
        # 关闭进度条
        if progress_bar:
            progress_bar.close()
        
        return stats
    
    def _process_with_error_handling(
        self,
        process_func: Callable,
        item: Any,
        error_handler: Optional[Callable]
    ) -> ProcessResult:
        """带错误处理的处理函数包装器"""
        try:
            return process_func(item)
        except Exception as e:
            if error_handler:
                error_handler(item, e)
            return ProcessResult(success=False, error=e)


class RateLimiter:
    """简单的速率限制器"""
    
    def __init__(self, rate: int):
        """
        初始化速率限制器
        
        Args:
            rate: 每秒允许的操作数
        """
        self.rate = rate
        self.allowance = rate
        self.last_check = time.time()
        self.lock = threading.Lock()
    
    def acquire(self):
        """获取执行权限，必要时等待"""
        with self.lock:
            current = time.time()
            time_passed = current - self.last_check
            self.last_check = current
            self.allowance += time_passed * self.rate
            
            if self.allowance > self.rate:
                self.allowance = self.rate
            
            if self.allowance < 1.0:
                sleep_time = (1.0 - self.allowance) / self.rate
                time.sleep(sleep_time)
                self.allowance = 0.0
            else:
                self.allowance -= 1.0


class AsyncBatchProcessor:
    """异步批量处理器 - 支持异步操作"""
    
    def __init__(
        self,
        batch_size: int = 100,
        max_concurrent: int = 10,
        show_progress: bool = True
    ):
        """
        初始化异步批量处理器
        
        Args:
            batch_size: 批次大小
            max_concurrent: 最大并发数
            show_progress: 是否显示进度条
        """
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        self.show_progress = show_progress
        self.semaphore = asyncio.Semaphore(max_concurrent)
    
    async def process_items(
        self,
        items: List[Any],
        process_func: Callable[[Any], asyncio.Future],
        error_handler: Optional[Callable[[Any, Exception], None]] = None
    ) -> Dict[str, int]:
        """
        异步批量处理项目
        
        Args:
            items: 要处理的项目列表
            process_func: 异步处理函数
            error_handler: 错误处理函数
            
        Returns:
            处理统计结果
        """
        stats = {
            "total": len(items),
            "success": 0,
            "failed": 0,
            "processed": 0
        }
        
        if not items:
            return stats
        
        # 创建进度条
        progress_bar = None
        if self.show_progress:
            progress_bar = tqdm(total=len(items), desc="Processing", unit="items")
        
        # 创建所有任务
        tasks = []
        for item in items:
            task = self._process_with_semaphore(
                process_func, item, error_handler, stats, progress_bar
            )
            tasks.append(task)
        
        # 并发执行所有任务
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # 关闭进度条
        if progress_bar:
            progress_bar.close()
        
        return stats
    
    async def _process_with_semaphore(
        self,
        process_func: Callable,
        item: Any,
        error_handler: Optional[Callable],
        stats: Dict[str, int],
        progress_bar: Optional[tqdm]
    ):
        """带信号量控制的处理函数"""
        async with self.semaphore:
            try:
                result = await process_func(item)
                if result.success:
                    stats["success"] += 1
                else:
                    stats["failed"] += 1
            except Exception as e:
                if error_handler:
                    error_handler(item, e)
                stats["failed"] += 1
            
            stats["processed"] += 1
            
            # 更新进度条
            if progress_bar:
                progress_bar.update(1)
                progress_bar.set_postfix({
                    "成功": stats["success"],
                    "失败": stats["failed"]
                })


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """将列表分块"""
    for i in range(0, len(lst), chunk_size):
        yield lst[i:i + chunk_size]