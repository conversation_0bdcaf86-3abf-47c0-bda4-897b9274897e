"""数据库工具函数"""
from sqlalchemy import create_engine
from sqlalchemy.engine import Engine

from src.config.load_config import load_config


def get_mysql_engine() -> Engine:
    """获取MySQL数据库引擎"""
    config = load_config([
        "MYSQL_HOST",
        "MYSQL_PORT",
        "MYSQL_USERNAME",
        "MYSQL_PASSWORD",
        "MYSQL_DB",
        "MYSQL_CHARSET",
    ])
    
    # 构建数据库URL
    mysql_url = (
        f"mysql+pymysql://{config['MYSQL_USERNAME']}:{config['MYSQL_PASSWORD']}"
        f"@{config['MYSQL_HOST']}:{config.get('MYSQL_PORT', 3306)}"
        f"/{config['MYSQL_DB']}?charset={config.get('MYSQL_CHARSET', 'utf8mb4')}"
    )
    
    # 创建引擎
    engine = create_engine(
        mysql_url,
        pool_size=5,
        max_overflow=10,
        pool_pre_ping=True,
        echo=False
    )
    
    return engine