"""
断点续传管理器 - 支持大量文件同步的断点续传功能
"""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

from loguru import logger


class CheckpointManager:
    """断点续传管理器"""
    
    def __init__(self, checkpoint_dir: str = ".dify_sync_checkpoints"):
        """
        初始化断点管理器
        
        Args:
            checkpoint_dir: 断点文件存储目录
        """
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(exist_ok=True)
        
    def create_checkpoint(self, task_id: str) -> "Checkpoint":
        """
        创建新的断点
        
        Args:
            task_id: 任务标识
            
        Returns:
            断点对象
        """
        return Checkpoint(self.checkpoint_dir, task_id)
    
    def list_checkpoints(self) -> List[Dict[str, Any]]:
        """列出所有断点"""
        checkpoints = []
        for file_path in self.checkpoint_dir.glob("*.json"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    checkpoints.append({
                        "task_id": data.get("task_id"),
                        "created_at": data.get("created_at"),
                        "updated_at": data.get("updated_at"),
                        "status": data.get("status"),
                        "progress": data.get("progress", {})
                    })
            except Exception as e:
                logger.error(f"读取断点文件失败 {file_path}: {e}")
        
        return sorted(checkpoints, key=lambda x: x.get("updated_at", ""), reverse=True)
    
    def remove_checkpoint(self, task_id: str) -> bool:
        """删除断点"""
        checkpoint_file = self.checkpoint_dir / f"{task_id}.json"
        if checkpoint_file.exists():
            checkpoint_file.unlink()
            return True
        return False
    
    def clean_old_checkpoints(self, days: int = 7):
        """清理旧断点文件"""
        current_time = datetime.now()
        for file_path in self.checkpoint_dir.glob("*.json"):
            try:
                # 检查文件修改时间
                mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                if (current_time - mtime).days > days:
                    file_path.unlink()
                    logger.info(f"删除过期断点文件: {file_path.name}")
            except Exception as e:
                logger.error(f"清理断点文件失败 {file_path}: {e}")


class Checkpoint:
    """断点对象"""
    
    def __init__(self, checkpoint_dir: Path, task_id: str):
        """
        初始化断点
        
        Args:
            checkpoint_dir: 断点目录
            task_id: 任务标识
        """
        self.checkpoint_dir = checkpoint_dir
        self.task_id = task_id
        self.checkpoint_file = checkpoint_dir / f"{task_id}.json"
        
        # 加载或初始化断点数据
        if self.checkpoint_file.exists():
            self._load()
        else:
            self._initialize()
    
    def _initialize(self):
        """初始化断点数据"""
        self.data = {
            "task_id": self.task_id,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "status": "pending",
            "progress": {
                "total": 0,
                "processed": 0,
                "success": 0,
                "failed": 0,
                "skipped": 0
            },
            "processed_items": [],  # 已处理的项目ID列表
            "failed_items": [],     # 失败的项目列表
            "metadata": {}          # 额外的元数据
        }
        self._save()
    
    def _load(self):
        """加载断点数据"""
        try:
            with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
        except Exception as e:
            logger.error(f"加载断点文件失败: {e}")
            self._initialize()
    
    def _save(self):
        """保存断点数据"""
        try:
            self.data["updated_at"] = datetime.now().isoformat()
            with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存断点文件失败: {e}")
    
    def update_progress(self, **kwargs):
        """更新进度信息"""
        self.data["progress"].update(kwargs)
        self._save()
    
    def add_processed_item(self, item_id: str):
        """添加已处理项目"""
        if item_id not in self.data["processed_items"]:
            self.data["processed_items"].append(item_id)
            self.data["progress"]["processed"] += 1
            self._save()
    
    def add_failed_item(self, item_id: str, error: str):
        """添加失败项目"""
        self.data["failed_items"].append({
            "item_id": item_id,
            "error": error,
            "timestamp": datetime.now().isoformat()
        })
        self.data["progress"]["failed"] += 1
        self._save()
    
    def is_processed(self, item_id: str) -> bool:
        """检查项目是否已处理"""
        return item_id in self.data["processed_items"]
    
    def get_processed_items(self) -> Set[str]:
        """获取已处理项目集合"""
        return set(self.data["processed_items"])
    
    def set_status(self, status: str):
        """设置任务状态"""
        self.data["status"] = status
        self._save()
    
    def set_metadata(self, key: str, value: Any):
        """设置元数据"""
        self.data["metadata"][key] = value
        self._save()
    
    def get_metadata(self, key: str, default=None):
        """获取元数据"""
        return self.data["metadata"].get(key, default)
    
    def get_progress(self) -> Dict[str, int]:
        """获取进度信息"""
        return self.data["progress"].copy()
    
    def get_summary(self) -> Dict[str, Any]:
        """获取摘要信息"""
        return {
            "task_id": self.task_id,
            "status": self.data["status"],
            "progress": self.get_progress(),
            "created_at": self.data["created_at"],
            "updated_at": self.data["updated_at"],
            "failed_count": len(self.data["failed_items"])
        }
    
    def complete(self):
        """标记任务完成"""
        self.set_status("completed")
        logger.info(f"任务 {self.task_id} 完成: {self.get_progress()}")
    
    def fail(self, error: str):
        """标记任务失败"""
        self.set_status("failed")
        self.set_metadata("error", error)
        logger.error(f"任务 {self.task_id} 失败: {error}")