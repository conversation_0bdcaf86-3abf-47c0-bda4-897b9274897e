import jwt

from src.config.load_config import load_config
from src.db.redis import AsyncRedis


config = load_config(["JWT_ALGORITHM", "JWT_CACHE_KEY", "JWT_SECRET"])
redis = AsyncRedis()


async def sign_token(payload):
    token = jwt.encode(
        payload=payload, key=config["JWT_SECRET"], algorithm=config["JWT_ALGORITHM"]
    )
    await redis.set(f"{payload['user_id']}:{config['JWT_CACHE_KEY']}", token)
    return token


async def verify_token(token: str):
    data = jwt.decode(jwt=token, key=config["JWT_SECRET"], algorithms=[config["JWT_ALGORITHM"]])
    existence_count = await redis.exists(
        f"{data['user_id']}:{config['JWT_CACHE_KEY']}", token
    )
    if existence_count == 0:
        raise Exception("Token does not exist")
    return data


__all__ = ["sign_token", "verify_token"]
