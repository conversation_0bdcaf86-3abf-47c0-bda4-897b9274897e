from langchain_core.tools import tool
from langgraph.prebuilt import InjectedState
from typing_extensions import Annotated

from src.financial_advisor.api.customer import (
    query_customer_basic_info,
    query_customer_trade_info,
)


@tool
def fetch_customer_trade_info(
    customer_name: str,
):
    """查询客户的交易信息，如：总资产等。

    Args:
        customer_name (str): 客户名称

    """

    return "很抱歉！当前无法查询客户的交易信息。", None

    customer_info = query_customer_basic_info()
    card_data = query_customer_trade_info()

    return "您好！查询到{}（ID: XXXXXXXXXXXXXXX）的交易信息如下：".format(
        customer_name
    ), {
        "card_id": "customer_trade_info",
        "card_data": {
            "customer_info": customer_info,
            "trade_info": card_data,
        },
    }
