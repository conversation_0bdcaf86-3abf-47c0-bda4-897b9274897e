from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from typing import Optional

from src.financial_advisor.api.customer import query_customer_basic_info
from src.financial_advisor.utils import extractAuthUserInfoFromRunnableConfig


@tool
def fetch_customer_basic_info(
    config: RunnableConfig,
    customer_name: Optional[str] = None,
    customer_no: Optional[str] = None,
):
    """查询客户的基础信息，如：客户号、姓名、性别、年龄等。

    Args:
        customer_name (str): 客户姓名，提问可能包含客户姓名，没有则为None
        customer_no (str): 客户号，提问可能包含客户号，没有则为None
    """

    user_info = extractAuthUserInfoFromRunnableConfig(config)
    if user_info is None:
        return "未能提取用户认证信息", None

    card_data = query_customer_basic_info(
        user_info,
        customer_name=customer_name,
        customer_no=customer_no,
    )

    if len(card_data) == 0:
        return f"很抱歉，未能找到客户{customer_name or customer_no}的基础信息。", None
    if len(card_data) > 1:
        return (
            f"查询到多个客户信息，请提供更具体的客户姓名或客户号以便进一步筛选。当前查询结果包含{len(card_data)}个客户的信息。",
            None,
        )

    return (
        f"您好，查询到客户{customer_name or customer_no}的基础信息为：{card_data[0]}",
        {
            "card_id": "customer_basic_info",
            "card_data": card_data[0],
        },
    )
