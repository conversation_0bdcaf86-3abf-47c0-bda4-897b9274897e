from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from typing import Optional
from src.config.load_config import load_config
from src.financial_advisor.api.mock import portfolio_details

from src.financial_advisor.api.portfolio import (
    query_portfolio_allocation,
    query_portfolio_allocation_products,
    query_stardard_portfolios,
)
from src.financial_advisor.utils import extractAuthUserInfoFromRunnableConfig


@tool
def fetch_portfolio_detail_info(
    config: RunnableConfig,
    portfolio_name: Optional[str] = None,
    portfolio_no: Optional[str] = None,
):
    """查询组合的详细信息，如：大类配置比例、产品配置明细和历史指标等。

    Args:
        portfolio_name (str): 组合名称，提问可能包含组合名称，没有则为None
        portfolio_no (str): 组合代码，提问可能包含组合代码，没有则为None
    """

    user_info = extractAuthUserInfoFromRunnableConfig(config)
    if user_info is None:
        return "未能提取用户认证信息", None

    portfolios = query_stardard_portfolios(user_info)
    portfolios = [p for p in portfolios if p.portfolio_name == portfolio_name or p.portfolio_id == portfolio_no]

    if len(portfolios) == 0:
        return f"很抱歉，未能找到组合{portfolio_name or portfolio_no}的基础信息。", None
    if len(portfolios) > 1:
        return (
            f"查询到多个组合信息，请提供更具体的组合名称或组合代码以便进一步筛选。当前查询结果包含{len(portfolios)}个组合的信息。",
            None,
        )

    portfolio = portfolios[0]
    
    if load_config("MOCK_MODE") == "Y":
        return (
            f"您好，查询到组合{portfolio_name or portfolio_no}的信息如下：{portfolio_details[portfolio.portfolio_id]}",
            None,
        )

    portfolio_allocation = query_portfolio_allocation(current_manager=user_info, portfolio_no=portfolio.portfolio_id)
    portfolio_allocation_products = query_portfolio_allocation_products(
        current_manager=user_info, portfolio_no=portfolio.portfolio_id
    )

    return (
        f"您好，查询到组合{portfolio_name or portfolio_no}的基础信息为：{portfolio}，大类配置为：{portfolio_allocation}，产品配置明细为：{portfolio_allocation_products}",
        None,
    )
