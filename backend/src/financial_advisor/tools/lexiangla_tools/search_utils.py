import re
import requests

from bs4 import BeautifulSoup
from typing_extensions import cast

from src.config.load_config import load_config
from src.db.redis import Redis
from src.financial_advisor.tools.lexiangla_tools.authenticate import authenticate
from src.financial_advisor.tools.lexiangla_tools.types import LeXiangSearchResult


SEARCH_TYPES = ["doc", "clazz"]


def search_request(keyword: str, type: str, page: int = 1) -> list[LeXiangSearchResult]:
    config = load_config(keys=["LEXIANGLA_BASE_URL", "LEXIANGLA_TOKEN_KEY"])

    redis_client = Redis()
    existence_count = redis_client.exists(config.get("LEXIANGLA_TOKEN_KEY", ""))
    if existence_count == 0:
        authenticate()

    token = cast(
        bytes,
        redis_client.get(config.get("LEXIANGLA_TOKEN_KEY", "")),
    ).decode()

    response = requests.get(
        url=f"""{config.get("LEXIANGLA_BASE_URL")}/v1/search?keyword={keyword}&type={type}&page={page}""",
        headers={
            "Accept": "application/json",
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
        },
    )
    return response.json().get("data")


def remove_html_tags(text: str) -> str:
    return re.compile(r"</?em>|em>").sub("", text)
    return BeautifulSoup(text, "html.parser").get_text()


def get_citations(result: list[LeXiangSearchResult]):
    return [
        {
            "name": record.get("attributes").get(
                "name" if record.get("type") == "doc" else "title"
            ),
            "owner": record.get("relationships").get("owner").get("data").get("id"),
            "updated_at": record.get("attributes").get("updated_at"),
            "url": record.get("links").get("platform"),
        }
        for record in result
    ]


def get_result(result: list[LeXiangSearchResult]):
    return [
        f"""{remove_html_tags(item.get("attributes").get("search_summary"))}[{item.get("attributes").get("name" if item.get("type") == "doc" else "title")}]({item.get("links").get("platform")})"""
        for item in result
    ]
