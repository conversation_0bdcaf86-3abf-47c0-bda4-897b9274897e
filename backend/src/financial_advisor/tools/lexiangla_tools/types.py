from typing import TypedDict


class LeXiangSearchResultAttributes(TypedDict):
    name: str
    search_summary: str
    title: str
    updated_at: str


class LeXiangSearchResultLinks(TypedDict):
    platform: str


class LeXiangSearchResultRelationships(TypedDict):
    owner: dict[str, dict]


class LeXiangSearchResult(TypedDict):
    attributes: LeXiangSearchResultAttributes
    links: LeXiangSearchResultLinks
    relationships: LeXiangSearchResultRelationships