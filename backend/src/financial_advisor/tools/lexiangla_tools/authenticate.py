import requests

from src.config.load_config import load_config
from src.db.redis import Redis


def authenticate():
    config = load_config(
        keys=[
            "LEXIANGLA_APP_KEY",
            "LEXIANGLA_APP_SECRET",
            "LEXIANGLA_BASE_URL",
            "LEXIANGLA_TOKEN_KEY",
        ]
    )
    redis_client = Redis()

    response = requests.post(
        url=f"""{config.get("LEXIANGLA_BASE_URL")}/token""",
        headers={"Accept": "application/json", "Content-Type": "application/json"},
        json={
            "grant_type": "client_credentials",
            "app_key": config.get("LEXIANGLA_APP_KEY"),
            "app_secret": config.get("LEXIANGLA_APP_SECRET"),
        },
    )
    data = response.json()
    redis_client.set(
        config.get("LEXIANGLA_TOKEN_KEY", ""),
        data.get("access_token"),
        data.get("expires_in"),
    )
