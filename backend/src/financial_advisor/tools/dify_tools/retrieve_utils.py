import requests

from src.financial_advisor.tools.dify_tools.types import (
    RetrievalPayload,
    RetrievalRecord,
    RetrievalResult,
)
from src.config.load_config import load_config


DATASET_ID_MAP = {
    "产品培训文档库": "7c30d2c2-9811-46e4-bfa1-2514a041c534",
    "投后管理报告库": "5baef91d-8427-4650-9215-fc9485f686e5",
    "产品生命周期管理库": "5e49f020-b567-4abf-8646-4fc2731f5b69",
    "资产配置策略库": "96bc3e5b-c622-4550-9348-070923f8807c",
    "合规操作运营库": "694cffeb-26c6-4881-bc3e-fe604e3e9f0e",
    "服务信托业务库": "9f4bdad9-dfb5-4b55-bcab-30331067c824",
    "内部工具使用库": "6ec71da5-d1c0-4c98-baf8-1e2dc7a524ac",
    "法律法规库": "",
}


def info(dataset: str):
    config = load_config(["DIFY_API_KEY", "DIFY_BASE_URL"])

    response = requests.get(
        url=f"""{config.get("DIFY_BASE_URL")}/datasets/{dataset}""",
        headers={
            "Authorization": f"""Bearer {config.get("DIFY_API_KEY")}""",
            "Content-Type": "application/json",
        },
    )
    return response.json()


def retrieve_request(dataset: str, payload: RetrievalPayload) -> RetrievalResult:
    config = load_config(["DIFY_API_KEY", "DIFY_BASE_URL"])

    response = requests.post(
        url=f"""{config.get("DIFY_BASE_URL")}/datasets/{dataset}/retrieve""",
        headers={
            "Authorization": f"""Bearer {config.get("DIFY_API_KEY")}""",
            "Content-Type": "application/json",
        },
        json=payload,
    )
    return response.json()


def get_citations(records: list[RetrievalRecord]):
    citations = []

    for record in records:
        name = record.get("segment").get("document").get("name")
        metadata = record.get("segment").get("document").get("doc_metadata")
        url = None if metadata is None else metadata.get("platform_url")
        citations.append({"name": name, "url": url})

    return citations


def get_result(records: list[RetrievalRecord]):
    result = ""

    for record in records:
        metadata = record.get("segment").get("document").get("doc_metadata")
        url = None if metadata is None else metadata.get("platform_url")
        res = f"""{record.get("segment").get("content")}[{record.get("segment").get("document").get("name")}]"""
        if url:
            res += f"""({url})"""
        result += res

    return result
