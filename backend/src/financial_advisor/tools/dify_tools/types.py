from typing import TypedDict, Optional


class RetrievalPayload(TypedDict):
    query: str
    retrieval_model: dict


class RetrievalDocument(TypedDict):
    id: str
    data_source_type: str
    name: str
    doc_type: Optional[str]
    doc_metadata: Optional[dict]


class RetrievalSegment(TypedDict):
    id: str
    position: int
    document_id: str
    content: str
    sign_content: str
    answer: Optional[str]
    word_count: int
    tokens: int
    keywords: Optional[str]
    index_node_id: str
    index_node_hash: str
    hit_count: int
    enabled: bool
    disabled_at: Optional[int]
    disabled_by: Optional[str]
    status: str
    created_by: str
    created_at: int
    indexing_at: int
    completed_at: int
    error: Optional[str]
    stopped_at: Optional[int]
    document: RetrievalDocument


class RetrievalRecord(TypedDict):
    segment: RetrievalSegment
    child_chunks: Optional[list]
    score: float
    tsne_position: Optional[list]


class RetrievalResultQuery(TypedDict):
    content: str


class RetrievalResult(TypedDict):
    query: RetrievalResultQuery
    records: list[RetrievalRecord]
