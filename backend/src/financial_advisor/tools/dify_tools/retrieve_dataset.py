from langchain_core.tools import tool
from langchain_core.runnables import RunnableConfig

from src.financial_advisor.tools.dify_tools.retrieve_utils import (
    DATASET_ID_MAP,
    get_citations,
    get_result,
    info,
    retrieve_request,
)


@tool
def retrieve_dataset(
    dataset_name: str,
    query: str,
    metadata_filtering_conditions: dict | None,
    config: RunnableConfig,
):
    """召回知识库。

    Args:
        dataset_name (str): 知识库名称
            产品培训文档库
            投后管理报告库
            产品生命周期管理库
            资产配置策略库
            合规操作运营库
            服务信托业务库
            内部工具使用库
            法律法规库

        query (str): 用户问题

        metadata_filtering_conditions (dict): 元数据过滤条件(可选参数)
            metadata_filtering_conditions.logical_operator (string): 逻辑运算符
                选项为 and | or
            metadata_filtering_conditions.conditions (list): 条件列表
                条件为 dict，包含以下字段：
                    name (string) 元数据字段名
                    comparison_operator (string) 比较运算符，可选值:
                        字符串比较:
                            contains: 包含
                            not contains: 不包含
                            start with: 以...开头
                            end with: 以...结尾
                            is: 等于
                            is not: 不等于
                            empty: 为空
                            not empty: 不为空
                        数值比较:
                            =: 等于
                            ≠: 不等于
                            >: 大于
                            < : 小于
                            ≥: 大于等于
                            ≤: 小于等于
                    value (string|number|null) 比较值
    """

    dataset_id = DATASET_ID_MAP[dataset_name]
    dataset_info = info(dataset_id)
    retrieval_model = dataset_info.get("retrieval_model_dict")
    retrieval_model["score_threshold_enabled"] = True
    retrieval_model["score_threshold"] = 0.3
    retrieval_model["metadata_filtering_conditions"] = metadata_filtering_conditions

    query = query.replace(" ", "")
    result = retrieve_request(
        dataset=dataset_id, payload={"query": query, "retrieval_model": retrieval_model}
    )
    records = result.get("records")

    if len(records) == 0:
        return f"很抱歉，没有找到与“{query}”相关的信息。", None

    return get_result(records), {
        "card_id": "dataset",
        "card_data": get_citations(records),
    }
