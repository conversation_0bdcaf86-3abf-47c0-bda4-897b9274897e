from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from typing import Optional

from src.financial_advisor.api.product import query_products
from src.financial_advisor.utils import extractAuthUserInfoFromRunnableConfig


@tool
def fetch_product_basic_info(
    config: RunnableConfig,
    product_name: Optional[str] = None,
    product_code: Optional[str] = None,
):
    """查询信托产品的基础信息，如：信托产品号、信托产品名称、成立日期等。

    Args:
        product_name (str): 信托产品名称，提问可能包含信托产品名称，没有则为None
        product_code (str): 信托产品号，提问可能包含信托产品代码，没有则为None
    """

    user_info = extractAuthUserInfoFromRunnableConfig(config)
    if user_info is None:
        return "未能提取用户认证信息", None

    card_data = query_products(
        user_info,
        product_name=product_name,
        product_code=product_code,
    )

    print(card_data, product_name, product_code)

    if len(card_data) == 0:
        return f"很抱歉，未能找到产品{product_name or product_code}的基础信息。", None
    if len(card_data) > 1:
        return (
            f"查询到多个产品信息，请提供更具体的产品名称或产品代码以便进一步筛选。当前查询结果包含{len(card_data)}个产品的信息。",
            None,
        )

    return (
        f"您好，查询到产品{product_name or product_code}的基础信息为：{card_data[0]}",
        {
            "card_id": "product_basic_info",
            "card_data": card_data[0],
        },
    )
