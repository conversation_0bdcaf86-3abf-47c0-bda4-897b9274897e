import json
import logging

from langchain_core.messages import AIMessage, HumanMessage, ToolCall
from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, StateGraph
from langgraph.types import Command, interrupt
from uuid import uuid4

from src.db.redis import Redis
from src.financial_advisor.api.portfolio import save_advise_portfolio
from src.financial_advisor.api.product import query_allocation_products
from src.financial_advisor.configuration import Configuration
from src.financial_advisor.models import (
    FinancialPlanningOptions,
    FinancialPlanningProducts,
    PlanningRouteDecision,
)
from src.financial_advisor.prompts import (
    ASSET_ALLOCATION_PROMPT,
    ASSET_ALLOCATION_PRODUCT_PROMPT,
    ASSET_ALLOCATION_RECOMMEND_PROMPT,
    CUSTOMER_BASIC_INFO_PROMPT,
    CUSTOMER_BASIC_INFO_EXTRACT_PROMPT,
    CUSTOMER_HOLD_INFO_PROMPT,
    CUSTOMER_PROFILE_PROMPT,
    FINANCIAL_PLANNING_PROMPT,
    PLANNING_ROUTING_PROMPT,
)
from src.financial_advisor.state import PlanningState
from src.financial_advisor.tools.customer_tools import (
    fetch_customer_basic_info,
    fetch_customer_hold_info,
)
from src.financial_advisor.utils import extractAuthUserInfoFromRunnableConfig
from src.financial_advisor.vos import CustomerKycInfoVO
from src.utils.history_summariser import (
    add_to_state,
    create_history_context,
    get_latest_user_message,
)
from src.utils.llm_utils import create_llm, execute_tool_calls


logging.basicConfig(level=logging.DEBUG)
redis = Redis()


def planning_router_query(state: PlanningState, config: RunnableConfig):
    state = add_to_state(state)
    latest_user_message = get_latest_user_message(state.get("messages"))

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "router_model", temperature=0.3)
    history_context = (
        create_history_context(
            state["conversation_history"], llm, max_history_tokens=1000
        )
        or "No previous conversation"
    )
    structured_llm = llm.with_structured_output(PlanningRouteDecision)

    try:
        decision = structured_llm.invoke(
            PLANNING_ROUTING_PROMPT.format(
                query=latest_user_message, history_context=history_context
            )
        )
        planning_query_type = decision.route

        logging.info(f"Query: '{latest_user_message[:100]}...'")
        logging.info(
            f"Routed to: {planning_query_type} (confidence: {decision.confidence})"
        )
        logging.info(f"Reasoning: {decision.reasoning}")

        # Validate route name
        valid_routes = [
            "planning_for_new_customer",
            "planning_for_existing_customer",
            "planning_for_existing_customer_with_position",
        ]
        if planning_query_type not in valid_routes:
            logging.warning(
                f"Invalid route '{planning_query_type}', defaulting to general_discussion"
            )
            return Command(goto="general_discussion", graph=Command.PARENT)

    except Exception as e:
        logging.error(
            f"Error in structured LLM routing: {e}, falling back to general_discussion"
        )
        return Command(goto="general_discussion", graph=Command.PARENT)

    return {"planning_query_type": planning_query_type}


def prepare_new_customer_info(state: PlanningState, config: RunnableConfig):
    user_question = get_latest_user_message(state["messages"])

    if not user_question:
        return {
            "messages": [AIMessage(content="I couldn't find a question to answer.")]
        }

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)
    structured_llm = llm.with_structured_output(CustomerKycInfoVO)
    formatted_prompt = CUSTOMER_BASIC_INFO_EXTRACT_PROMPT.format(
        user_question=user_question
    )
    customer_basic_info = structured_llm.invoke(formatted_prompt)

    return {
        "current_step": "提取客户基本信息",
        "current_step_detail": "根据用户所给信息提取客户基本信息",
        "customer_basic_info": customer_basic_info.__dict__,
        "customer_hold_info": [],
        "customer_type": "2",
    }


def prepare_existing_customer_info(state: PlanningState, config: RunnableConfig):
    user_question = get_latest_user_message(state["messages"])

    if not user_question:
        return {
            "messages": [AIMessage(content="I couldn't find a question to answer.")]
        }

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)
    history_context = (
        create_history_context(
            state["conversation_history"], llm, max_history_tokens=1500
        )
        or "No previous financial discussions."
    )

    formatted_prompt = CUSTOMER_BASIC_INFO_PROMPT.format(
        user_question=user_question, history_context=history_context
    )
    tools = [fetch_customer_basic_info]
    llm_with_tools = llm.bind_tools(tools)
    c_info_messages: list[AIMessage | HumanMessage] = [
        HumanMessage(content=formatted_prompt)
    ]
    c_info_ai_msg: AIMessage = llm_with_tools.invoke(c_info_messages)
    c_info_messages.append(c_info_ai_msg)
    c_info_messages, c_info_card_list = execute_tool_calls(
        c_info_ai_msg, tools, c_info_messages
    )

    if len(c_info_card_list) == 0:
        return {
            "messages": [AIMessage(content="未找到相关客户的具体信息")],
            "current_step": "查询客户基本信息",
            "current_step_detail": f"""查询客户基本信息数据""",
        }

    return {
        "current_step": "查询客户基本信息",
        "current_step_detail": f"""查询客户名为：{c_info_ai_msg.tool_calls[0].get("args").get("customer_name")} 的基础信息数据""",
        "customer_basic_info": c_info_card_list[0]["card_data"].__dict__,
        "customer_hold_info": [],
        "customer_type": "1",
    }


def prepare_existing_customer_info_with_position(
    state: PlanningState, config: RunnableConfig
):
    user_question = get_latest_user_message(state["messages"])

    if not user_question:
        return {
            "messages": [AIMessage(content="I couldn't find a question to answer.")]
        }

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)
    history_context = (
        create_history_context(
            state["conversation_history"], llm, max_history_tokens=1500
        )
        or "No previous financial discussions."
    )

    formatted_prompt = CUSTOMER_BASIC_INFO_PROMPT.format(
        user_question=user_question, history_context=history_context
    )
    tools = [fetch_customer_basic_info]
    llm_with_tools = llm.bind_tools(tools)
    c_info_messages: list[AIMessage | HumanMessage] = [
        HumanMessage(content=formatted_prompt)
    ]
    c_info_ai_msg: AIMessage = llm_with_tools.invoke(c_info_messages)
    c_info_messages.append(c_info_ai_msg)
    c_info_messages, c_info_card_list = execute_tool_calls(
        c_info_ai_msg, tools, c_info_messages
    )

    if len(c_info_card_list) == 0:
        return {
            "messages": [AIMessage(content="未找到相关客户的具体信息")],
            "current_step": "查询客户基本信息",
            "current_step_detail": f"""查询客户的基本信息数据""",
        }

    formatted_prompt = CUSTOMER_HOLD_INFO_PROMPT.format(
        user_question=user_question, history_context=history_context
    )
    tools = [fetch_customer_hold_info]
    llm_with_tools = llm.bind_tools(tools)
    c_hold_messages: list[AIMessage | HumanMessage] = [
        HumanMessage(content=formatted_prompt)
    ]
    c_hold_ai_msg: AIMessage = llm_with_tools.invoke(c_hold_messages)
    c_hold_messages.append(c_hold_ai_msg)
    c_hold_messages, c_hold_card_list = execute_tool_calls(
        c_hold_ai_msg, tools, c_hold_messages
    )

    info_tool_call_args = c_info_ai_msg.tool_calls[0].get("args")
    return {
        "current_step": "查询客户基本信息、持仓信息",
        "current_step_detail": f"""查询客户名为：{info_tool_call_args.get("customer_name") or info_tool_call_args.get("customer_no")} 的基本信息和持仓信息数据""",
        "customer_basic_info": c_info_card_list[0]["card_data"].__dict__,
        "customer_hold_info": (
            c_hold_card_list
            if len(c_hold_card_list) == 0
            else c_hold_card_list[0]["card_data"]
        ),
        "customer_type": "1",
    }


def wait_for_user_input_at_existing_customer(
    state: PlanningState, config: RunnableConfig
):
    additional_info = interrupt(
        {
            "current_step": state.get("current_step"),
            "current_step_detail": state.get("current_step_detail"),
            "graph_name": "planning_advisor",
            "node_name": "wait_for_user_input_at_existing_customer",
            "message": AIMessage(
                content=f"""我识别到您想给{state.get("customer_basic_info")["customer_name"]}，进行一次财富规划，为了对客户进行更精准的规划，请您尽全面的提供{state.get("customer_basic_info")["customer_name"]}最近的个人、家庭、财富变化情况，包括但不限于基本信息、资产变化信息、投资需求变化、风险偏好变化，以进行更好的财富规划。\n以下是{state.get("customer_basic_info")["customer_name"]}当前的KYC信息：""",
                id=uuid4().hex,
                tool_calls=[
                    ToolCall(
                        id=uuid4().hex,
                        name="fetch_customer_basic_info",
                        args={
                            "card_id": "KYC",
                            "card_data": state.get("customer_basic_info"),
                        },
                    ),
                    ToolCall(
                        id=uuid4().hex,
                        name="collect_customer_basic_info",
                        args={
                            "card_id": "collect_customer_basic_info",
                            "card_data": {
                                "title": "您可以围绕以下维度和客户沟通收集信息，并将这些信息告诉我，我可以为您更好的进行投资财富规划"
                            },
                        },
                    ),
                ],
            ),
        }
    )
    return {
        "additional_info": additional_info,
    }


def wait_for_user_input_at_new_customer(state: PlanningState, config: RunnableConfig):
    additional_info = interrupt(
        {
            "current_step": state.get("current_step"),
            "current_step_detail": state.get("current_step_detail"),
            "graph_name": "planning_advisor",
            "node_name": "wait_for_user_input_at_new_customer",
            "message": AIMessage(
                "我识别到您想给一个新的客户进行一次财富规划。这个客户还没有录入系统信息为了对客户进行更精准的规划，请您尽全面的提供他的个人、家庭、财富情况，包括但不限于基本信息、资产变化信息、投资需求变化、风险偏好变化，以进行更好的财富规划。",
                id=uuid4().hex,
                tool_calls=[
                    ToolCall(
                        id=uuid4().hex,
                        name="fetch_customer_basic_info",
                        args={
                            "card_id": "collect_customer_basic_info",
                            "card_data": {
                                "title": "您可以围绕以下维度和客户沟通收集信息，并将这些信息告诉我，我可以为您更好的进行投资财富规划"
                            },
                        },
                    ),
                ],
            ),
        }
    )
    return {
        "additional_info": additional_info,
    }


def profile_customer_info(state: PlanningState, config: RunnableConfig):
    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)
    formatted_prompt = CUSTOMER_PROFILE_PROMPT.format(
        customer_basic_info=state.get("customer_basic_info"),
        customer_hold_info=None,
        additional_info=state.get("additional_info"),
    )
    customer_profile_message = llm.invoke(formatted_prompt)

    llm_with_structure = llm.with_structured_output(FinancialPlanningOptions)
    financial_planning = llm_with_structure.invoke(FINANCIAL_PLANNING_PROMPT)

    user_info = extractAuthUserInfoFromRunnableConfig(config)
    if user_info is None:
        return {"messages": [AIMessage(content="未能提取用户认证信息")]}

    thread_id = config.get("configurable").get("thread_id")

    redis.set(
        f"""polling:{user_info["user_id"]}:{thread_id}""",
        json.dumps(
            {
                "route": "/aihome/statusDesc",
                "state": {"content": financial_planning},
            },
            ensure_ascii=False,
        ),
    )

    return {
        "current_step": "客户画像分析",
        "current_step_detail": "根据客户基本信息生成客户画像",
        "financial_planning": financial_planning,
        "profile_message": customer_profile_message.content,
    }


def profile_customer_info_with_position(state: PlanningState, config: RunnableConfig):
    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)
    formatted_prompt = CUSTOMER_PROFILE_PROMPT.format(
        customer_basic_info=state.get("customer_basic_info"),
        customer_hold_info=state.get("customer_hold_info"),
        additional_info=state.get("additional_info"),
    )
    customer_profile_message = llm.invoke(formatted_prompt)

    llm_with_structure = llm.with_structured_output(FinancialPlanningOptions)
    financial_planning = llm_with_structure.invoke(FINANCIAL_PLANNING_PROMPT)

    user_info = extractAuthUserInfoFromRunnableConfig(config)
    if user_info is None:
        return {"messages": [AIMessage(content="未能提取用户认证信息")]}

    thread_id = config.get("configurable").get("thread_id")

    redis.set(
        f"""polling:{user_info["user_id"]}:{thread_id}""",
        json.dumps(
            {
                "route": "/aihome/statusDesc",
                "state": {"content": financial_planning},
            },
            ensure_ascii=False,
        ),
    )

    return {
        "current_step": "客户画像分析",
        "current_step_detail": "根据客户基本信息和持仓信息生成客户画像",
        "financial_planning": financial_planning,
        "profile_message": customer_profile_message.content,
    }


def wait_for_user_edition_at_planning(state: PlanningState, config: RunnableConfig):
    financial_planning = interrupt(
        {
            "current_step": "客户画像分析",
            "current_step_detail": "根据客户信息生成客户画像",
            "graph_name": "planning_advisor",
            "node_name": "wait_for_user_edition_at_planning",
            "message": AIMessage(
                id=uuid4().hex,
                content=f"""根据我拿到的{state.get("customer_basic_info")["customer_name"]}的信息及补充信息，我对{state.get("customer_basic_info")["customer_name"]}进行了客户画像分析""",
                tool_calls=[
                    ToolCall(
                        id=uuid4().hex,
                        name="fetch_customer_profile",
                        args={
                            "card_id": "customer_profile",
                            "card_data": {
                                "title": "画像分析",
                                "text": state.get("profile_message"),
                            },
                        },
                    ),
                    ToolCall(
                        id=uuid4().hex,
                        name="fetch_information_provenance",
                        args={
                            "card_id": "information_provenance",
                            "card_data": {
                                "title": "信息溯源",
                                "table": (
                                    [
                                        {
                                            "name": "基本信息",
                                            "data": state.get("customer_basic_info"),
                                        }
                                    ]
                                    if len(state.get("customer_hold_info")) == 0
                                    else [
                                        {
                                            "name": "基本信息",
                                            "data": state.get("customer_basic_info"),
                                        },
                                        {
                                            "name": "持仓信息",
                                            "data": state.get("customer_hold_info"),
                                        },
                                    ]
                                ),
                            },
                        },
                    ),
                    ToolCall(
                        id=uuid4().hex,
                        name="fetch_financial_planning",
                        args={
                            "card_id": "financial_planning",
                            "card_data": {
                                "title": "依据画像分析，我为您生成了本次的规划需求信息，您可以手动调整后确认，我将依次进行财富规划。",
                                "form": state.get("financial_planning"),
                                "completed": False,
                            },
                        },
                    ),
                ],
            ),
        }
    )
    return {
        "financial_planning": financial_planning,
    }


def generate_asset_allocation(state: PlanningState, config: RunnableConfig):
    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)

    user_info = extractAuthUserInfoFromRunnableConfig(config)
    if user_info is None:
        return {"messages": [AIMessage(content="未能提取用户认证信息")]}

    products = query_allocation_products(user_info, {})
    formatted_prompt = ASSET_ALLOCATION_PROMPT.format(
        financial_planning=state.get("financial_planning"),
    )
    asset_allocation = llm.invoke(formatted_prompt)
    asset_allocation_message = asset_allocation.content

    llm_with_structure = llm.with_structured_output(FinancialPlanningProducts)
    product_allocation = llm_with_structure.invoke(
        ASSET_ALLOCATION_PRODUCT_PROMPT.format(
            total_amount=100000,
            asset_allocation=asset_allocation_message,
            products=products,
        )
    )

    thread_id = config.get("configurable").get("thread_id")

    redis.set(
        f"""polling:{user_info["user_id"]}:{thread_id}""",
        json.dumps(
            {
                "route": "/aihome/statusDesc",
                "state": {"content": product_allocation.product_allocation_reasoning},
            },
            ensure_ascii=False,
        ),
    )

    return {
        "asset_allocation_message": asset_allocation_message,
        "product_allocation": product_allocation.product_allocation,
    }


def wait_for_user_confirm_at_allocation(state: PlanningState):
    product_allocation = interrupt(
        {
            "current_step": "生成大类配置",
            "current_step_detail": "根据收集数据生成大类配置",
            "graph_name": "planning_advisor",
            "node_name": "wait_for_user_confirm_at_allocation",
            "message": AIMessage(
                id=uuid4().hex,
                content=state.get("asset_allocation_message"),
                tool_calls=[
                    ToolCall(
                        id=uuid4().hex,
                        name="fetch_product_allocation",
                        args={
                            "card_id": "product_allocation",
                            "card_data": {
                                "amount": 100000,
                                "products": state.get("product_allocation"),
                                "completed": False,
                            },
                        },
                    )
                ],
            ),
        }
    )
    return {"product_allocation": product_allocation}


def commit_asset_allocation(state: PlanningState, config: RunnableConfig):
    print("commit_asset_allocation state:", state)

    user_info = extractAuthUserInfoFromRunnableConfig(config)
    if user_info is None:
        return {"messages": [AIMessage(content="未能提取用户认证信息")]}

    save_result = save_advise_portfolio(
        current_manager=user_info,
        payload={
            "riskCode": "C5",
            "pfRecords": {
                "productWeights": [
                    {
                        **pa,
                        "moneny": pa.get("weightAmount"),
                        "totalVol": pa.get("weightAmount"),
                        "code": pa.get("bankProCode"),
                    }
                    for pa in state.get("product_allocation")
                ]
            },
            "bmRecords": {
                "benchmarkName": "中证500",
                "activeDt": "********",
                "bmWeights": [
                    {
                        "bmCode": "000905",
                        "benchmarkCode": "000905",
                        "bmName": "中证500",
                        "weight": "1",
                    }
                ],
            },
        },
    )
    logging.info(f"save portfolio result: {save_result}")

    return state


def wait_for_user_actions_at_ending(state: PlanningState):
    ending_action = interrupt(
        {
            "current_step": "生成规划方案",
            "current_step_detail": "根据收集数据生成规划方案",
            "graph_name": "planning_advisor",
            "node_name": "wait_for_user_actions_at_ending",
            "message": AIMessage(
                id=uuid4().hex,
                content="本次规划方案已生成",
                tool_calls=[
                    ToolCall(
                        id=uuid4().hex,
                        name="commit_asset_allocation",
                        args={
                            "card_id": "commit_asset_allocation",
                            "card_data": {"completed": False},
                        },
                    )
                ],
            ),
        }
    )
    return {"ending_action": ending_action}


def finish(state: PlanningState, config: RunnableConfig):
    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)
    formatted_prompt = ASSET_ALLOCATION_RECOMMEND_PROMPT.format(
        product_allocation=state.get("product_allocation"),
        position_info=state.get("customer_hold_info"),
    )
    asset_allocation_recommend = llm.invoke(formatted_prompt).content

    return {
        "current_step": "生成推荐话术",
        "current_step_detail": "为本次规划方案生成推荐话术",
        "messages": [
            AIMessage(
                content="",
                tool_calls=[
                    ToolCall(
                        id=uuid4().hex,
                        name="fetch_product_recommend_pitch",
                        args={
                            "card_id": "product_recommend_pitch",
                            "card_data": {
                                "copyable": True,
                                "text": asset_allocation_recommend,
                            },
                        },
                    )
                ],
            )
        ],
    }


def planning_graph():
    """Create a graph for financial planning related queries."""
    planning_graph = StateGraph(PlanningState)

    planning_graph.add_node("planning_router_query", planning_router_query)
    planning_graph.add_node("prepare_new_customer_info", prepare_new_customer_info)
    planning_graph.add_node(
        "prepare_existing_customer_info", prepare_existing_customer_info
    )
    planning_graph.add_node(
        "prepare_existing_customer_info_with_position",
        prepare_existing_customer_info_with_position,
    )
    planning_graph.add_node(
        "wait_for_user_input_at_existing_customer",
        wait_for_user_input_at_existing_customer,
    )
    planning_graph.add_node(
        "wait_for_user_input_at_existing_customer_with_position",
        wait_for_user_input_at_existing_customer,
    )
    planning_graph.add_node(
        "wait_for_user_input_at_new_customer", wait_for_user_input_at_new_customer
    )
    planning_graph.add_node("profile_customer_info", profile_customer_info)
    planning_graph.add_node(
        "profile_customer_info_with_position", profile_customer_info_with_position
    )
    planning_graph.add_node(
        "wait_for_user_edition_at_planning", wait_for_user_edition_at_planning
    )
    planning_graph.add_node("generate_asset_allocation", generate_asset_allocation)
    planning_graph.add_node(
        "wait_for_user_confirm_at_allocation", wait_for_user_confirm_at_allocation
    )
    planning_graph.add_node("commit_asset_allocation", commit_asset_allocation)
    planning_graph.add_node(
        "wait_for_user_actions_at_ending", wait_for_user_actions_at_ending
    )
    planning_graph.add_node("finish", finish)

    planning_graph.set_entry_point("planning_router_query")
    planning_graph.add_conditional_edges(
        "planning_router_query",
        lambda state: state.get("planning_query_type"),
        {
            "planning_for_new_customer": "prepare_new_customer_info",
            "planning_for_existing_customer": "prepare_existing_customer_info",
            "planning_for_existing_customer_with_position": "prepare_existing_customer_info_with_position",
        },
    )
    planning_graph.add_edge(
        "prepare_new_customer_info", "wait_for_user_input_at_new_customer"
    )
    planning_graph.add_edge(
        "prepare_existing_customer_info", "wait_for_user_input_at_existing_customer"
    )
    planning_graph.add_edge(
        "prepare_existing_customer_info_with_position",
        "wait_for_user_input_at_existing_customer_with_position",
    )
    planning_graph.add_edge(
        "wait_for_user_input_at_new_customer", "profile_customer_info"
    )
    planning_graph.add_edge(
        "wait_for_user_input_at_existing_customer", "profile_customer_info"
    )
    planning_graph.add_edge(
        "wait_for_user_input_at_existing_customer_with_position",
        "profile_customer_info_with_position",
    )
    planning_graph.add_edge(
        "profile_customer_info", "wait_for_user_edition_at_planning"
    )
    planning_graph.add_edge(
        "profile_customer_info_with_position", "wait_for_user_edition_at_planning"
    )
    planning_graph.add_edge(
        "wait_for_user_edition_at_planning", "generate_asset_allocation"
    )
    planning_graph.add_edge(
        "generate_asset_allocation", "wait_for_user_confirm_at_allocation"
    )
    planning_graph.add_edge(
        "wait_for_user_confirm_at_allocation", "commit_asset_allocation"
    )
    planning_graph.add_edge(
        "commit_asset_allocation", "wait_for_user_actions_at_ending"
    )
    planning_graph.add_edge("wait_for_user_actions_at_ending", "finish")
    planning_graph.add_edge("finish", END)

    return planning_graph.compile(name="planning_advisor")
