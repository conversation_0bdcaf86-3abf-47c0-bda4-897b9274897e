import logging

from langchain_core.messages import AIMessage, HumanMessage, Too<PERSON>Call
from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, StateGraph
from langgraph.types import Command
from typing import Any

from src.financial_advisor.configuration import Configuration
from src.financial_advisor.models import CustomerRouteDecision
from src.financial_advisor.prompts import (
    CUSTOMER_BASIC_INFO_PROMPT,
    CUSTOMER_HOLD_INFO_PROMPT,
    CUSTOMER_ROUTING_PROMPT,
    CUSTOMER_TRADE_INFO_PROMPT,
)
from src.financial_advisor.state import State
from src.financial_advisor.tools.customer_tools import (
    fetch_customer_basic_info,
    fetch_customer_hold_info,
    fetch_customer_trade_info,
)
from src.utils.history_summariser import (
    add_to_state,
    create_history_context,
    get_latest_user_message,
)
from src.utils.llm_utils import create_llm, execute_tool_calls


logging.basicConfig(level=logging.DEBUG)


def customer_route_query(
    state: State, config: RunnableConfig
) -> dict[str, Any] | Command[str]:
    state = add_to_state(state)
    latest_user_message = get_latest_user_message(state.get("messages"))

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "router_model", temperature=0.3)
    history_context = (
        create_history_context(
            state["conversation_history"], llm, max_history_tokens=1000
        )
        or "No previous conversation"
    )
    structured_llm = llm.with_structured_output(CustomerRouteDecision)

    try:
        decision = structured_llm.invoke(
            CUSTOMER_ROUTING_PROMPT.format(
                query=latest_user_message, history_context=history_context
            )
        )
        customer_query_type = decision.route

        logging.info(f"Query: '{latest_user_message[:100]}...'")
        logging.info(
            f"Routed to: {customer_query_type} (confidence: {decision.confidence})"
        )
        logging.info(f"Reasoning: {decision.reasoning}")

        # Validate route name
        valid_routes = [
            "customer_basic_info",
            "customer_hold_info",
            "customer_trade_info",
        ]
        if customer_query_type not in valid_routes:
            logging.warning(
                f"Invalid route '{customer_query_type}', defaulting to general_discussion"
            )
            return Command(goto="general_discussion", graph=Command.PARENT)

    except Exception as e:
        logging.error(
            f"Error in structured LLM routing: {e}, falling back to general_discussion"
        )
        return Command(goto="general_discussion", graph=Command.PARENT)

    return {"customer_query_type": customer_query_type}


def customer_basic_info(state: State, config: RunnableConfig):
    """Fetch customer basic information."""
    user_question = get_latest_user_message(state["messages"])

    if not user_question:
        return {
            "messages": [AIMessage(content="I couldn't find a question to answer.")]
        }

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)

    history_context = (
        create_history_context(
            state["conversation_history"], llm, max_history_tokens=1500
        )
        or "No previous financial discussions."
    )
    formatted_prompt = CUSTOMER_BASIC_INFO_PROMPT.format(
        user_question=user_question, history_context=history_context
    )

    tools = [fetch_customer_basic_info]
    llm_with_tools = llm.bind_tools(tools)

    messages: list[AIMessage | HumanMessage] = [HumanMessage(content=formatted_prompt)]
    ai_msg: AIMessage = llm_with_tools.invoke(messages)
    messages.append(ai_msg)
    # Execute tool calls if any
    messages, card_list = execute_tool_calls(ai_msg, tools, messages)

    # Get final response if tools were called
    if ai_msg.tool_calls and len(ai_msg.tool_calls) > 0:
        tool_call = ai_msg.tool_calls[0]
        args = tool_call.get("args")
        final_response = llm.invoke(messages)
        return {
            "messages": [
                AIMessage(
                    content=final_response.content,
                    tool_calls=(
                        [
                            ToolCall(
                                id=tool_call.get("id"),
                                name=tool_call.get("name"),
                                args=card_list[0],
                            )
                        ]
                        if len(card_list) > 0
                        else []
                    ),
                )
            ],
            "current_step": "查询客户基础信息",
            "current_step_detail": f"""查询客户：{args.get("customer_name") or args.get("customer_no")} 的基础信息数据""",
        }

    return {
        "messages": [ai_msg],
        "current_step": "查询客户基础信息",
        "current_step_detail": "查询客户数据接口",
    }


def customer_hold_info(state: State, config: RunnableConfig):
    """Fetch customer hold information."""
    user_question = get_latest_user_message(state["messages"])

    if not user_question:
        return {
            "messages": [AIMessage(content="I couldn't find a question to answer.")]
        }

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)

    history_context = (
        create_history_context(
            state["conversation_history"], llm, max_history_tokens=1500
        )
        or "No previous financial discussions."
    )
    formatted_prompt = CUSTOMER_HOLD_INFO_PROMPT.format(
        user_question=user_question, history_context=history_context
    )

    tools = [fetch_customer_hold_info]
    llm_with_tools = llm.bind_tools(tools)

    messages: list[AIMessage | HumanMessage] = [HumanMessage(content=formatted_prompt)]
    ai_msg: AIMessage = llm_with_tools.invoke(messages)
    messages.append(ai_msg)
    # Execute tool calls if any
    messages, card_list = execute_tool_calls(ai_msg, tools, messages)

    # Get final response if tools were called
    if ai_msg.tool_calls and len(ai_msg.tool_calls) > 0:
        tool_call = ai_msg.tool_calls[0]
        args = tool_call.get("args")
        final_response = llm.invoke(messages)
        return {
            "messages": [
                AIMessage(
                    content=final_response.content,
                    tool_calls=(
                        [
                            ToolCall(
                                id=tool_call.get("id"),
                                name=tool_call.get("name"),
                                args=card_list[0],
                            )
                        ]
                        if len(card_list) > 0
                        else []
                    ),
                )
            ],
            "current_step": "查询客户持仓信息",
            "current_step_detail": f"""查询客户：{args.get("customer_name") or args.get("customer_no")} 的持仓信息数据""",
        }

    return {
        "messages": [ai_msg],
        "current_step": "查询客户持仓信息",
        "current_step_detail": "查询客户数据接口",
    }


def customer_trade_info(state: State, config: RunnableConfig):
    """Fetch customer trade information."""
    user_question = get_latest_user_message(state["messages"])

    if not user_question:
        return {
            "messages": [AIMessage(content="I couldn't find a question to answer.")]
        }

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, "analyst_model", temperature=0.2)

    history_context = (
        create_history_context(
            state["conversation_history"], llm, max_history_tokens=1500
        )
        or "No previous financial discussions."
    )
    formatted_prompt = CUSTOMER_TRADE_INFO_PROMPT.format(
        user_question=user_question, history_context=history_context
    )

    tools = [fetch_customer_trade_info]
    llm_with_tools = llm.bind_tools(tools)

    messages: list[AIMessage | HumanMessage] = [HumanMessage(content=formatted_prompt)]
    ai_msg: AIMessage = llm_with_tools.invoke(messages)
    messages.append(ai_msg)
    # Execute tool calls if any
    messages, card_list = execute_tool_calls(ai_msg, tools, messages)

    # Get final response if tools were called
    if ai_msg.tool_calls and len(ai_msg.tool_calls) > 0:
        tool_call = ai_msg.tool_calls[0]
        args = tool_call.get("args")
        final_response = llm.invoke(messages)
        return {
            "messages": [
                AIMessage(
                    content=final_response.content,
                    tool_calls=(
                        [
                            ToolCall(
                                id=tool_call.get("id"),
                                name=tool_call.get("name"),
                                args=card_list[0],
                            )
                        ]
                        if len(card_list) > 0
                        else []
                    ),
                )
            ],
            "current_step": "查询客户交易信息",
            "current_step_detail": f"""查询客户：{args.get("customer_name") or args.get("customer_no")} 的交易信息数据""",
        }

    return {
        "messages": [ai_msg],
        "current_step": "查询客户交易信息",
        "current_step_detail": "查询客户数据接口",
    }


def customer_graph():
    """Create a graph for customer-related queries."""
    customer_graph = StateGraph(State)

    customer_graph.add_node("customer_route_query", customer_route_query)
    customer_graph.add_node("customer_basic_info", customer_basic_info)
    customer_graph.add_node("customer_hold_info", customer_hold_info)
    customer_graph.add_node("customer_trade_info", customer_trade_info)

    customer_graph.set_entry_point("customer_route_query")
    customer_graph.add_conditional_edges(
        "customer_route_query",
        lambda state: state.get("customer_query_type"),
        {
            "customer_basic_info": "customer_basic_info",
            "customer_hold_info": "customer_hold_info",
            "customer_trade_info": "customer_trade_info",
        },
    )
    customer_graph.add_edge("customer_basic_info", END)
    customer_graph.add_edge("customer_hold_info", END)
    customer_graph.add_edge("customer_trade_info", END)

    return customer_graph.compile(name="customer_advisor")
