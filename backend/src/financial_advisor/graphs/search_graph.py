import logging

from langchain_core.messages import AnyMessage, AIMessage, HumanMessage, ToolCall
from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, StateGraph
from typing import Any
from uuid import uuid4

from src.financial_advisor.configuration import Configuration
from src.financial_advisor.prompts import (
    get_current_date,
    SEARCH_ANSWER_INSTRUCTIONS,
)
from src.financial_advisor.state import SearchState
from src.financial_advisor.tools.lexiangla_tools.search_utils import (
    get_citations,
    get_result,
    search_request,
    SEARCH_TYPES,
)
from src.utils.llm_utils import create_llm


logging.basicConfig(level=logging.DEBUG)


def get_research_topic(messages: list[AnyMessage]) -> str:
    """
    Get the research topic from the messages.
    """
    # check if request has a history and combine the messages into a single string
    if len(messages) == 1:
        research_topic = messages[-1].content
    else:
        research_topic = ""
        for message in messages:
            if isinstance(message, HumanMessage):
                research_topic += f"User: {message.content}\n"
            elif isinstance(message, AIMessage):
                research_topic += f"Assistant: {message.content}\n"
    return research_topic


def search(state: SearchState) -> SearchState:
    web_research_result = []
    sources_gathered = []

    for st in SEARCH_TYPES:
        search_data = search_request(state["search_query"], st)

        citations = get_citations(search_data)
        sources_gathered.extend(citations)
        result = get_result(search_data)
        web_research_result.extend(result)

    return {
        **state,
        "search_result": web_research_result,
        "search_sources": sources_gathered,
    }


def finalize_answer(state: SearchState, config: RunnableConfig) -> dict[str, Any]:
    """Finalize the research summary."""

    formatted_prompt = SEARCH_ANSWER_INSTRUCTIONS.format(
        current_date=get_current_date(),
        research_topic=get_research_topic(state["messages"]),
        summaries="\n---\n\n".join(state["search_result"]),
    )

    configuration = Configuration.from_runnable_config(config)
    llm = create_llm(configuration, model_type="reasoning_model", temperature=0)

    result = llm.invoke(formatted_prompt)

    card_data = []
    for source in state["search_sources"]:
        if source["url"] in result.content:
            card_data.append(source)

    return {
        "messages": [
            AIMessage(
                content=result.content,
                tool_calls=(
                    [
                        ToolCall(
                            id=uuid4().hex,
                            name="retrieve_dataset",
                            args={
                                "card_id": "search",
                                "card_data": card_data,
                            },
                        )
                    ]
                ),
            )
        ],
        "current_step": "乐享搜索结果",
        "current_step_detail": f"""查询到{len(card_data)}条关于{state["search_query"]}的信息""",
        "next_node": None,
        "next_state": None,
    }


def search_graph():
    """Create a graph for search-related queries."""
    search_graph = StateGraph(SearchState)

    search_graph.add_node("search", search)
    search_graph.add_node("finalize_answer", finalize_answer)
    search_graph.set_entry_point("search")
    search_graph.add_edge("search", "finalize_answer")
    search_graph.add_edge("finalize_answer", END)

    return search_graph.compile(name="researcher")
