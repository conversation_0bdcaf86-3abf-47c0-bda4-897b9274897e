import json
import requests

from src.config.load_config import load_config


config = load_config(
    [
        "DIFY_API_KEY",
        "DIFY_BASE_URL",
        "DIFY_EMBEDDING_MODEL_NAME",
        "DIFY_EMBEDDING_PROVIDER_NAME",
        "DIFY_RERANKING_MODEL_NAME",
        "DIFY_RERANKING_PROVIDER_NAME",
        "DIFY_RETRIEVAL_TOP_K",
        "DIFY_RETRIEVAL_SCORE_THRESHOLD",
        "DIFY_WEIGHT_KEYWORD",
        "DIFY_WEIGHT_VECTOR",
    ]
)

data = {
    "indexing_technique": "high_quality",
    "doc_form": "text_model",
    "process_rule": {"mode": "automatic"},
    "retrieval_model": {
        "reranking_enable": True,
        "reranking_mode": "reranking_model",
        "reranking_model": {
            "reranking_provider_name": config["DIFY_RERANKING_PROVIDER_NAME"],
            "reranking_model_name": config["DIFY_RERANKING_MODEL_NAME"],
        },
        "score_threshold_enabled": True,
        "score_threshold": float(config["DIFY_RETRIEVAL_SCORE_THRESHOLD"]),
        "top_k": int(config["DIFY_RETRIEVAL_TOP_K"]),
        "search_method": "hybrid_search",
    },
    "embedding_model": config["DIFY_EMBEDDING_MODEL_NAME"],
    "embedding_model_provider": config["DIFY_EMBEDDING_PROVIDER_NAME"],
    "weights": {
        "weight_type": "customized",
        "keyword_setting": {"keyword_weight": float(config["DIFY_WEIGHT_KEYWORD"])},
        "vector_setting": {
            "vector_weight": float(config["DIFY_WEIGHT_VECTOR"]),
            "embedding_model_name": config["DIFY_EMBEDDING_MODEL_NAME"],
            "embedding_provider_name": config["DIFY_EMBEDDING_PROVIDER_NAME"],
        },
    },
}


def upload_file_to_dify(dateset_id: str, filename: str):
    with open(filename, "rb") as f:
        files = {
            "data": (None, json.dumps(data)),
            "file": f,
        }
        response = requests.post(
            f"""{config["DIFY_BASE_URL"]}/datasets/{dateset_id}/document/create-by-file""",
            headers={"Authorization": f"""Bearer {config["DIFY_API_KEY"]}"""},
            files=files,
        )
        f.close()
        return response.json()


__all__ = ["upload_file_to_dify"]
