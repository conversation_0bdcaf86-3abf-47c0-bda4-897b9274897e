import os
import pandas as pd
import requests
from typing_extensions import cast

from src.config.load_config import load_config
from src.db.redis import Redis
from src.financial_advisor.jobs.upload_file_to_dify import upload_file_to_dify
from src.financial_advisor.tools.lexiangla_tools.authenticate import authenticate
from src.financial_advisor.tools.dify_tools.retrieve_utils import DATASET_ID_MAP

config = load_config(keys=["LEXIANGLA_BASE_URL", "LEXIANGLA_TOKEN_KEY"])

HTML_COMPLETE_CONTENT = """<html><head></head><body>{content}</body></html>"""

filename = "乐享课堂文件.xlsx"
filepath = f"""/lexiang/{filename}"""


def download_file(url: str, filename: str):
    with requests.get(url, stream=True) as r:
        r.raise_for_status()
        with open(filename, "wb") as f:
            for chunk in r.iter_content(chunk_size=10240):
                if chunk:
                    f.write(chunk)
    print(f"文件已下载到 {filename}")


df = pd.read_excel(filepath).fillna("")
for _, row in df.iterrows():
    redis_client = Redis()
    existence_count = redis_client.exists(config.get("LEXIANGLA_TOKEN_KEY", ""))
    if existence_count == 0:
        authenticate()

    token = cast(
        bytes,
        redis_client.get(config.get("LEXIANGLA_TOKEN_KEY", "")),
    ).decode()

    response = requests.get(
        url=f"""{row["self_link"]}""",
        headers={
            "Accept": "application/json",
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
        },
    )
    course = response.json()
    attachment = course.get("data").get("relationships").get("attachment")
    if attachment is not None:
        attachment = attachment.get("data")
        attachment_id = attachment.get("id")
        att = requests.get(
            url=f"{config['LEXIANGLA_BASE_URL']}/attachments/{attachment_id}",
            headers={"Authorization": f"Bearer {token}"},
        ).json()
        attachment_id = att.get("data").get("id")
        attachment_name = att.get("data").get("attributes").get("name")
        downloadable = att.get("data").get("attributes").get("downloadable")
        if downloadable == 1:
            download_file_path = os.path.join(
                "/lexiang/download",
                att.get("attributes").get("name").replace("/", "_").replace("\\", "_"),
            )
            download_file(
                att.get("links").get("download"),
                download_file_path,
            )
            res = upload_file_to_dify(
                DATASET_ID_MAP[row["Dify库名"]], download_file_path
            )
            if res.get("document") is None:
                continue
            doc_id = res.get("document").get("id")
            res = requests.post(
                url=f"""{config["LEXIANGLA_BASE_URL"]}/datasets/{DATASET_ID_MAP[row["Dify库名"]]}/documents/metadata""",
                headers={
                    "Accept": "application/json",
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json",
                },
                json={
                    "operation_data": [
                        {
                            "document_id": doc_id,
                            "metadata_list": [
                                {
                                    "privilege": row["privilege"],
                                    "privilege_type": row["privilege_type"],
                                }
                            ],
                        }
                    ]
                },
            )
            print(res.json())
