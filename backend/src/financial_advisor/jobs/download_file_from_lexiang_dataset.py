import os
import pandas as pd
import requests
from typing_extensions import cast

from src.config.load_config import load_config
from src.db.redis import Redis
from src.financial_advisor.jobs.upload_file_to_dify import upload_file_to_dify
from src.financial_advisor.tools.lexiangla_tools.authenticate import authenticate
from src.financial_advisor.tools.dify_tools.retrieve_utils import DATASET_ID_MAP

config = load_config(keys=["LEXIANGLA_BASE_URL", "LEXIANGLA_TOKEN_KEY"])

HTML_COMPLETE_CONTENT = """<html><head></head><body>{content}</body></html>"""

filename = "乐享知识库文件.xlsx"
filepath = f"""/lexiang/{filename}"""

def download_file(url: str, filename: str):
    with requests.get(url, stream=True) as r:
        r.raise_for_status()
        with open(filename, "wb") as f:
            for chunk in r.iter_content(chunk_size=10240):
                if chunk:
                    f.write(chunk)
    print(f"文件已下载到 {filename}")


df = pd.read_excel(filepath).fillna("")
for _, row in df.iterrows():
    if len(row["doc_content"]) > 0 and len(row["file_id"]) == 0:
        html_name = row["乐享文档"].replace("/", "_").replace("\\", "_")
        html_path = os.path.join(
            "/lexiang/download",
            f"""{html_name}.html""",
        )
        with open(
            html_path,
            "w",
            encoding="utf-8",
        ) as html_file:
            redis_client = Redis()
            existence_count = redis_client.exists(config.get("LEXIANGLA_TOKEN_KEY", ""))
            if existence_count == 0:
                authenticate()

            token = cast(
                bytes,
                redis_client.get(config.get("LEXIANGLA_TOKEN_KEY", "")),
            ).decode()
            html_content = HTML_COMPLETE_CONTENT.format(content=row["doc_content"])
            html_file.write(html_content)
            html_file.close()
            res = upload_file_to_dify(DATASET_ID_MAP[row["Dify库名"]], html_path)
            doc_id = res.get("document").get("id")
            if res.get("document") is None:
                continue
            res = requests.post(
                url=f"""{config["LEXIANGLA_BASE_URL"]}/datasets/{DATASET_ID_MAP[row["Dify库名"]]}/documents/metadata""",
                headers={
                    "Accept": "application/json",
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json",
                },
                json={
                    "operation_data": [
                        {
                            "document_id": doc_id,
                            "metadata_list": [
                                {
                                    "privilege": row["privilege"],
                                    "privilege_type": row["privilege_type"],
                                }
                            ],
                        }
                    ]
                },
            )
            print(res.json())
    elif len(row["file_id"]) > 0:
        redis_client = Redis()
        existence_count = redis_client.exists(config.get("LEXIANGLA_TOKEN_KEY", ""))
        if existence_count == 0:
            authenticate()

        token = cast(
            bytes,
            redis_client.get(config.get("LEXIANGLA_TOKEN_KEY", "")),
        ).decode()

        response = requests.get(
            url=f"""{row["self_link"]}""",
            headers={
                "Accept": "application/json",
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json",
            },
        )
        document = response.json()
        included = document.get("included")
        if included:
            for item in included:
                if item.get("type") == "file":
                    download_file_path = os.path.join(
                        "/lexiang/download",
                        item.get("attributes").get("name").replace("/", "_").replace("\\", "_")
                        + "."
                        + item.get("attributes").get("file_type"),
                    )
                    download_file(
                        item.get("links").get("download"),
                        download_file_path,
                    )
                    res = upload_file_to_dify(
                        DATASET_ID_MAP[row["Dify库名"]], download_file_path
                    )
                    if res.get("document") is None:
                        continue
                    doc_id = res.get("document").get("id")
                    res = requests.post(
                        url=f"""{config["LEXIANGLA_BASE_URL"]}/datasets/{DATASET_ID_MAP[row["Dify库名"]]}/documents/metadata""",
                        headers={
                            "Accept": "application/json",
                            "Authorization": f"Bearer {token}",
                            "Content-Type": "application/json",
                        },
                        json={
                            "operation_data": [
                                {
                                    "document_id": doc_id,
                                    "metadata_list": [
                                        {
                                            "privilege": row["privilege"],
                                            "privilege_type": row["privilege_type"],
                                            "product_name": row["product_name"],
                                            "report_type": row["report_type"],
                                            "product_manager": row["product_manger"]
                                        }
                                    ],
                                }
                            ]
                        },
                    )
                    print(res.json())
