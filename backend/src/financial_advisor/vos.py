from typing import Optional
from pydantic import BaseModel, Field


class CustomerBasicInfoVO(BaseModel):
    customer_id: str = Field(description="客户号")
    customer_name: str = Field(description="客户姓名")
    mobile_phone: Optional[str] = Field(description="手机号码")
    gender: Optional[str] = Field(description="性别: 0-女 1-男")
    customer_age: int = Field(description="年龄")
    birthday: Optional[str] = Field(description="生日")
    email: Optional[str] = Field(description="邮箱")
    address: Optional[str] = Field(description="地址")
    id_status: Optional[str] = Field(description="证件状态: 0-未实名 1-已实名 2-待审核")
    id_expires_date: Optional[str] = Field(description="证件有效期截止日")
    bank_card_count: Optional[int] = Field(description="银行卡数量")
    prove_status: Optional[str] = Field(description="合格投资者: 0-否 1-是")
    prove_expires_date: Optional[str] = Field(description="合格投资者失效日")
    risk_level_status: Optional[str] = Field(description="风险测评: 0-否 1-是")
    risk_level: Optional[str] = Field(description="风测等级")
    risk_level_name: Optional[str] = Field(description="风测等级名称")
    risk_level_expires_date: Optional[str] = Field(description="风测失效日")
    tax_id_status: Optional[str] = Field(description="税收居民身份: 0-否 1-是")
    tax_id: Optional[str] = Field(description="税收居民身份状态: 0-未申报 1-已申报")
    tax_id_name: Optional[str] = Field(description="税收居民身份状态名称")
    tax_id_expires_date: Optional[str] = Field(description="税收居民身份失效日")
    customer_status: Optional[str] = Field(
        description="客户状态: 0-注册 1-实名 2-意向 3-流失 4-休眠 5-持仓"
    )
    marital_status: Optional[str] = Field(description="婚姻状况")
    nation: Optional[str] = Field(description="民族")
    highest_education: Optional[str] = Field(description="最高学历")
    last_login_time: Optional[str] = Field(description="上传登录时间")
    total_asset: Optional[float] = Field(description="总资产(万元)")
    total_income: Optional[float] = Field(description="累计收益(万元)")


class CustomerKycInfoVO(BaseModel):
    customer_name: str = Field(description="客户姓名")
    gender: Optional[str] = Field(description="性别")
    customer_age: int = Field(description="年龄")
    risk_level: Optional[str] = Field(description="风测等级")
    risk_level_name: Optional[str] = Field(description="风测等级名称")
    marital_status: Optional[str] = Field(description="婚姻状况")
    highest_education: Optional[str] = Field(description="最高学历")
    total_asset: Optional[int] = Field(description="总资产")


class CustomerHoldInfoVO(BaseModel):
    product_name: str = Field(description="产品名称")
    product_code: str = Field(description="产品代码")
    product_type: str = Field(description="产品类型")
    hold_shares: float = Field(description="持有份额")
    hold_assets: float = Field(description="持有资产")
    latest_net_value: float = Field(description="最新净值")
    latest_net_date: str = Field(description="最新净值日期")
    performance_benchmark: float = Field(description="业绩基准")

class CustomerTradeInfoVO(BaseModel):
    trade_date: str = Field(description="交易日期")
    biz_type: str = Field(description="业务类型")
    product_name: str = Field(description="产品名称")
    product_code: str = Field(description="产品代码")
    trade_amount: float = Field(description="交易金额")
    trade_status: str = Field(description="交易状态")

class PortfolioVO(BaseModel):
    portfolio_id: str = Field(description="组合ID")
    portfolio_name: str = Field(description="组合名称")
    portfolio_status: Optional[str] = Field(description="组合状态")
    portfolio_type: Optional[str] = Field(description="组合类型")
    risk_level: Optional[str] = Field(description="风险等级")
    risk_level_name: Optional[str] = Field(description="风险等级名称")
    first_adjust_date: Optional[str] = Field(description="首次调仓生效日期")
    last_adjust_date: Optional[str] = Field(description="最后调仓生效日期")
    acc_return: Optional[float] = Field(description="累计收益率")
    last_year_return: Optional[float] = Field(description="近一年收益率")
    max_drawdown: Optional[float] = Field(description="最大回撤")


class ProductVO(BaseModel):
    product_id: str = Field(description="产品ID")
    product_name: str = Field(description="产品名称")
    product_status: Optional[str] = Field(
        description="产品状态: 0-预售中 1-发行中 2-已售空"
    )
    risk_level: Optional[str] = Field(description="风险等级")
    latest_net_value: Optional[str] = Field(description="最新净值")
    latest_net_value_date: Optional[str] = Field(description="最新净值日期")
    latest_acc_net_value: Optional[str] = Field(description="最新累计净值")
    income_per_ten_thousand: Optional[str] = Field(description="万份收益")
    seven_day_annualized_return: Optional[str] = Field(description="七日年化收益率")
    set_up_date: Optional[str] = Field(description="成立日期")
    performance_benchmark: Optional[str] = Field(description="业绩基准")
    deadline: Optional[str] = Field(description="期限")
    deadline_type: Optional[str] = Field(description="期限类型")
    min_subscription_amount: Optional[str] = Field(description="个人认申购起点")
    launch_date: Optional[str] = Field(description="上架日期")
    removal_date: Optional[str] = Field(description="下架日期")
    open_purchase: Optional[str] = Field(description="开放申赎")
    open_date: Optional[str] = Field(description="开放日")
    next_open_date: Optional[str] = Field(description="下一开放日")
    last_month_return: Optional[str] = Field(description="近一月涨跌幅")
    last_year_return: Optional[str] = Field(description="近一年涨跌幅")
    product_tags: Optional[str] = Field(description="产品标签: 以｜分隔")
    project_short_name: Optional[str] = Field(description="项目简称")
    project_series: Optional[str] = Field(description="项目系列")
    project_type: Optional[str] = Field(description="项目类型")
    project_status: Optional[str] = Field(description="营销项目状态")
    project_hot: Optional[str] = Field(description="是否热门项目: 0-否 1-是")
    fee_description: Optional[str] = Field(description="费用说明")
