from pydantic import BaseModel, Field
from typing import Optional, Union


class RouteDecision(BaseModel):
    """Main graph routing decision for user query"""

    route: str = Field(
        description="One of: 'customer_info', 'product_info', 'dataset', 'general_discussion', 'portfolio_info', 'financial_planning'"
    )
    confidence: float = Field(description="Confidence in the routing decision (0-1)")
    reasoning: str = Field(
        description="Comprehensive explanation for the routing decision in Chinese"
    )


class CustomerRouteDecision(BaseModel):
    """Customer graph routing decision for user query"""

    route: str = Field(
        description="One of: 'customer_basic_info', 'customer_hold_info', 'customer_trade_info'"
    )
    confidence: float = Field(description="Confidence in the routing decision (0-1)")
    reasoning: str = Field(
        description="Comprehensive explanation for the routing decision"
    )


class ProductRouteDecision(BaseModel):
    """Product graph routing decision for user query"""

    route: str = Field(
        description="One of: 'product_basic_info', 'compare_products', 'similar_products', 'similar_products_for_customer'"
    )
    confidence: float = Field(description="Confidence in the routing decision (0-1)")
    reasoning: str = Field(
        description="Comprehensive explanation for the routing decision"
    )

class PorfolioRouteDecision(BaseModel):
    """Product graph routing decision for user query"""

    route: str = Field(
        description="One of: 'analyze_portfolio', 'endorse_portfolio_for_customer'"
    )
    confidence: float = Field(description="Confidence in the routing decision (0-1)")
    reasoning: str = Field(
        description="Comprehensive explanation for the routing decision"
    )

class PlanningRouteDecision(BaseModel):
    """Planning graph routing decision for user query"""

    route: str = Field(
        description="One of: 'planning_for_new_customer', 'planning_for_existing_customer', 'planning_for_existing_customer_with_position'"
    )
    confidence: float = Field(description="Confidence in the routing decision (0-1)")
    reasoning: str = Field(
        description="Comprehensive explanation for the routing decision"
    )


class ProductRecommendPitch(BaseModel):
    """Product recommend pitch for user query"""

    reasoning: str = Field(
        description="Comprehensive explanation for product recommendation"
    )
    pitch: str = Field(description="Product recommendation script for customer")
    # product: dict = Field(description="Original data of the chosen product")


class FinancialPlanningOptions(BaseModel):
    """Financial planning options for user query"""

    account: str = Field(
        description="规划账户，从'全账户', '直销财富', '信托'中选择一个"
    )
    account_reasoning: str = Field(
        description="Comprehensive explanation for the account decision"
    )
    risk_tolerance: str = Field(
        description="风险承受能力，从'保守型', '稳健型', '平衡型', '成长型', '积极型'中选择一个"
    )
    risk_tolerance_reasoning: str = Field(
        description="Comprehensive explanation for the risk tolerance decision"
    )
    investment_style: str = Field(
        description="投资风格，从'保守型', '稳健型', '平衡型', '成长型', '积极型'中选择一个"
    )
    investment_style_reasoning: str = Field(
        description="Comprehensive explanation for the investment style decision"
    )
    customer_age: str = Field(
        description="客户年龄，从'-25', '25-45', '45-65', '65-'中选择一个"
    )
    customer_age_reasoning: str = Field(
        description="Comprehensive explanation for the customer age decision"
    )
    customer_asset: str = Field(
        description="客户资产，从'-100万', '100万-300万', '300万-600万', '600万-1000万', '1000万-'中选择一个"
    )
    customer_asset_reasoning: str = Field(
        description="Comprehensive explanation for the customer asset decision"
    )
    annualizedReturn: str = Field(
        description="预期年化收益率， 从'-3%', '3%-5%', '5%-8%', '8%-'中选择一个"
    )
    annualizedReturn_reasoning: str = Field(
        description="Comprehensive explanation for the annualized return decision"
    )


class FinancialPlanningProduct(BaseModel):
    proCode: str = Field(description="产品ID")
    proName: str = Field(description="产品名称")
    proName: str = Field(description="产品名称")
    proShortName: str = Field(description="产品简称")
    assetcatCode: str = Field(description="产品大类代码: DCM-固收类 EQ-权益类 MM-混合类 OTH-商品类及衍生类")
    assetcatName: str = Field(description="产品大类名称")
    bankProCode: str = Field(description="银行间产品代码")
    pfcatCode: str = Field(description="产品分类代码: FUND-公募基金 TRUST-信托产品")
    pfcatName: str = Field(description="产品分类名称")
    proRiskCode: str = Field(description="产品风险等级代码")
    proRiskName: str = Field(description="产品风险等级名称")
    weight: float = Field(description="配置比例(最多保留两位的浮点数,且所有产品比例之和为100%)")
    weightAmount: int = Field(description="配置金额")
    sectorCode: str = Field(description="市场代码")
    sectorName: str = Field(description="市场名称")
    sectorType: str = Field(description="市场类型")
    holdingMoney: Optional[float] = Field(description="持仓金额")
    holdingYn: Optional[str] = Field(description="是否持仓产品: Y-是 N-否")
    productFeature: Optional[str] = Field(description="产品特色")
    profitName: str = Field(description="产品收益能力名称")
    profitValue: Optional[Union[str, int]] = Field(description="产品收益能力")


class FinancialPlanningProducts(BaseModel):
    """Financial planning products for user query"""

    product_allocation: list[FinancialPlanningProduct] = Field(
        description="List of financial planning products"
    )
    product_allocation_reasoning: str = Field(
        description="Comprehensive explanation for the product allocation decision"
    )
