# 侧边栏服务接口-产品列表查询
from src.financial_advisor.api.request import async_request, request


async def async_query_allocation_products(current_manager: dict, params: dict):
    response = await async_request(
        "assetConfig/qryAcProdList",
        {
            "userCode": current_manager["work_no"],
            **params,
        },
        {"Authorization": current_manager["access_token"]},
    )
    return response["data"]


def query_allocation_products(current_manager: dict, params: dict):
    response = request(
        "assetConfig/qryAcProdList",
        {
            **params,
            "userCode": current_manager["work_no"],
        },
        {"Authorization": current_manager["access_token"]},
    )
    return response["data"]
