# 侧边栏服务接口-产品列表查询
from src.financial_advisor.api.request import async_request, request


async def async_query_indexes(current_manager: dict, payload: dict):
    payload = {"brokerAccount": current_manager["broker_account"]}

    response = await async_request(
        "assetConfig/qryIndexBasic",
        payload,
        {"Authorization": current_manager["access_token"]},
    )
    index_list = response["data"]

    return index_list


def query_indexes(current_manager: dict, payload: dict):
    payload = {"brokerAccount": current_manager["broker_account"]}

    index_list = request(
        "assetConfig/qryIndexBasic",
        payload,
        {"Authorization": current_manager["access_token"]},
    )["data"]

    return index_list
