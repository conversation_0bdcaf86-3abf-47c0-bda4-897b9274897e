import json
import urllib.parse

from src.financial_advisor.api.request import request


def generate_portfolio_info_link_id(
    current_manager: dict,
    portfolio_data: dict,
):
    params = {
        "brokerAccount": current_manager["work_no"],
        "brokerAccount1": current_manager["broker_account"],
        "pageType": "1",
        "type": "query",
        "jsonData": urllib.parse.quote(
            json.dumps(
                {
                    "pfId": portfolio_data["portfolio_id"],
                    "firstActiveDt": portfolio_data["first_adjust_date"],
                    "lastActiveDt": portfolio_data["last_adjust_date"],
                },
                ensure_ascii=False,
            )
        ),
        "isFinancialManager": "1",
    }
    response = request(
        "assetConfig/generateLinkCache",
        {"pramId": json.dumps(params, ensure_ascii=False), "unid": ""},
        {"Authorization": current_manager["access_token"]},
    )
    data = response["data"]

    return data
