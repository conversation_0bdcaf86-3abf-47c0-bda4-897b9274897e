from src.financial_advisor.api.request import request


def query_portfolio_allocation(current_manager: dict, portfolio_no: str):
    response = request(
        path="assetConfig/qryCombinCfg",
        payload={
            "isFinancialManager": 1,
            "pfId": portfolio_no,
            "userCode": current_manager["work_no"],
        },
        ex_headers={"Authorization": current_manager["access_token"]},
    )
    data = response["data"]
    return data


def query_portfolio_allocation_products(
    current_manager: dict,
    portfolio_no: str,
):
    response = request(
        path="assetConfig/qryAllocationProducts",
        payload={
            "isFinancialManager": 1,
            "pfId": portfolio_no,
            "userCode": current_manager["work_no"],
        },
        ex_headers={"Authorization": current_manager["access_token"]},
    )
    data = response["data"]
    return data
