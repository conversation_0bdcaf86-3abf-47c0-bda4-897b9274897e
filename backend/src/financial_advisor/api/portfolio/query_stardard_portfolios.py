from typing_extensions import Optional

from src.config.load_config import load_config
from src.financial_advisor.api.mock import portfolios
from src.financial_advisor.api.request import request
from src.financial_advisor.vos import PortfolioVO

def query_stardard_portfolios(
    current_manager: dict,
    payload: Optional[dict] = None
):
    if load_config("MOCK_MODE") == "Y":
        return portfolios

    page_no = 1
    page_size = 10
    total_cnt = page_no * (page_size + 1)
    portfolio_list = []

    while page_no * page_size < total_cnt:
        response = request(
            "assetConfig/qryStandCombinInfo",
            {
                "userCode": current_manager["work_no"],
                "pageNum": page_no,
                "pageSize": page_size,
                "pfTypeList": ["12"],
                **(payload or {}),
            },
            {"Authorization": current_manager["access_token"]},
        )
        portfolio_list.extend(response["data"]["scList"] or [])
        total_cnt = int(response["data"]["totalCount"] or 0)
        page_no += 1

    return [
        PortfolioVO(
            portfolio_id=portfolio["pfId"],
            portfolio_name=portfolio["pfName"],
            portfolio_type=portfolio["pfType"],
            portfolio_status=portfolio["stat"],
            risk_level=portfolio["riskCode"],
            risk_level_name=portfolio["parseRiskCode"],
            first_adjust_date=portfolio["firstActiveDt"],
            last_adjust_date=portfolio["lastActiveDt"],
            acc_return=portfolio["PCTValue"],
            last_year_return=portfolio["PCT1yValue"],
            max_drawdown=portfolio["DDRValue"],
        )
        for portfolio in portfolio_list
    ]
