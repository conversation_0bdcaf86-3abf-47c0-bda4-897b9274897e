from Crypto.Cipher import AES

import base64
import os


def iso10126_pad(data: bytes, block_size=16) -> bytes:
    pad_len = block_size - len(data) % block_size
    if pad_len == 0:
        pad_len = block_size
    padding = os.urandom(pad_len - 1) + bytes([pad_len])
    return data + padding


def iso10126_unpad(data: bytes) -> bytes:
    pad_len = data[-1]
    if pad_len < 1 or pad_len > 16:
        raise ValueError("Invalid padding length.")
    return data[:-pad_len]


def encrypt_aes(plaintext: str, secret: str) -> str:
    key = secret.encode("utf-8")
    cipher = AES.new(key, AES.MODE_ECB)

    padded_data = iso10126_pad(plaintext.encode("utf-8"), 16)
    encrypted = cipher.encrypt(padded_data)

    return base64.b64encode(encrypted).decode("utf-8")


def decrypt_aes(ciphertext: str, secret: str) -> str:
    key = secret.encode("utf-8")
    cipher = AES.new(key, AES.MODE_ECB)

    encrypted_data = base64.b64decode(ciphertext)
    decrypted_padded = cipher.decrypt(encrypted_data)
    decrypted = iso10126_unpad(decrypted_padded)

    return decrypted.decode("utf-8")


__all__ = ["encrypt_aes", "decrypt_aes"]
