from datetime import datetime, timedelta

from src.financial_advisor.vos import (
    CustomerBasicInfoVO,
    CustomerHoldInfoVO,
    PortfolioVO,
    ProductVO,
)


# 1. 客户基本信息
customer_info = CustomerBasicInfoVO(
    **{
        "customer_id": "************",
        "customer_name": "李华",
        "mobile_phone": "***********",  # 根据需求文档中的示例
        "gender": "0",  # 0-女,1-男
        "customer_age": 30,
        "birthday": "********",
        "email": "<EMAIL>",
        "address": "北京市朝阳区",
        "id_status": "1",  # 已实名
        "id_expires_date": "2030-12-31",
        "bank_card_count": 2,  # 银行卡数量
        "prove_status": "1",  # 是合格投资者 0否1是
        "prove_expires_date": "********",  # 有效期至2026-06-30
        "risk_level_status": "1",  # 已完成风测 0否1是
        "risk_level": "C5",  # 积极型
        "risk_level_name": "积极型",
        "risk_level_expires_date": "********",  # 风测有效期至2025-12-31
        "tax_id_status": "1",  # 是税收居民 0否1是
        "tax_id": "1",  # 已申报 税收居民身份状态: 0-未申报 1-已申报
        "tax_id_name": "已申报",  # 税收居民身份状态: 0-未申报 1-已申报
        "tax_id_expires_date": "********",  # 税收居民身份有效期
        "customer_status": "5",  # 持仓客户 客户状态: 0-注册 1-实名 2-意向 3-流失 4-休眠 5-持仓
        "marital_status": "未婚",  # 婚姻状态
        "nation": "汉族",
        "highest_education": "硕士",
        "last_login_time": "2018-08-01 12:00",
        "total_asset": 2200000.22,  # 总资产 2,200,000元 (220万)
        "total_income": 110300.11,  # 累计收益 (单位:元) 110,300
    }
)


# 2. 客户持仓信息 (4条)
customer_hold_info = [
    CustomerHoldInfoVO(
        **{
            "product_name": "招福宝玖晟1号",
            "product_code": "ZX95001",
            "hold_shares": 500000.0,  # 持有份额 (50万份)
            "hold_assets": 525000.0,  # 持有资产 (52.5万)
            "latest_net_value": 1.05,  # 最新净值
            "latest_net_date": datetime.now().strftime("%Y%m%d"),
            "performance_benchmark": 1.05,
            "product_type": "固收类",
        }
    ),
    CustomerHoldInfoVO(
        **{
            "product_name": "泰康颐养信托",
            "product_code": "TKYY001",
            "hold_shares": 300000.0,
            "hold_assets": 750000.0,
            "latest_net_value": 1.25,
            "latest_net_date": datetime.now().strftime("%Y%m%d"),
            "performance_benchmark": 1.25,
            "product_type": "权益类",
        }
    ),
    CustomerHoldInfoVO(
        **{
            "product_name": "涌信宝稳健收益3期",
            "product_code": "YXB003",
            "hold_shares": 400000.0,
            "hold_assets": 440000.0,
            "latest_net_value": 1.10,
            "latest_net_date": datetime.now().strftime("%Y%m%d"),
            "performance_benchmark": 1.10,
            "product_type": "混合类",
        }
    ),
    CustomerHoldInfoVO(
        **{
            "product_name": "名匠展弘1号",
            "product_code": "MJZH001",
            "hold_shares": 100000.0,
            "hold_assets": 180000.0,
            "latest_net_value": 1.80,
            "latest_net_date": datetime.now().strftime("%Y%m%d"),
            "performance_benchmark": 1.80,
            "product_type": "权益类",
        }
    ),
]


# 3. 客户交易记录 (3条)
customer_trade_info = [
    {
        "trade_date": (datetime.now() - timedelta(days=15)).strftime("%Y%m%d"),
        "biz_type": "申购",
        "product_name": "泰康颐养信托",
        "product_code": "TKYY001",
        "trade_amount": 100000,  # 10万元
        "trade_status": "5",  # 已签约
    },
    {
        "trade_date": (datetime.now() - timedelta(days=30)).strftime("%Y%m%d"),
        "biz_type": "赎回",
        "product_name": "招福宝玖晟3号",
        "product_code": "ZX95003",
        "trade_amount": 50000,  # 5万元
        "trade_status": "5",  # 已签约
    },
    {
        "trade_date": (datetime.now() - timedelta(days=45)).strftime("%Y%m%d"),
        "biz_type": "申购",
        "product_name": "名匠展弘1号",
        "product_code": "MJZH001",
        "trade_amount": 180000,  # 18万元
        "trade_status": "5",  # 已签约
    },
]


# 4. 信托产品信息 (用于测试产品查询和对比)
products = [
    ProductVO(
        **{
            "product_id": "ZX95001",
            "product_name": "招福宝玖晟1号",
            "product_status": "2",
            "risk_level": "中高风险",
            "latest_net_value": "1.2355",
            "latest_net_value_date": "2025-09-02",
            "latest_acc_net_value": "2.3455",
            "income_per_ten_thousand": "1.2323",
            "seven_day_annualized_return": "4.56%",
            "set_up_date": "2022-01-10",
            "performance_benchmark": "沪深300指数",
            "deadline": "3年",
            "deadline_type": "固定期限",
            "min_subscription_amount": "1000",
            "launch_date": "2020-02-01",
            "removal_date": None,
            "open_purchase": "每周一",
            "open_date": "每周一",
            "next_open_date": "2024-06-24",
            "last_month_return": "2.5%",
            "last_year_return": "12.3%",
            "product_tags": "积极｜高收益｜中高风险",
            "project_short_name": "稳成长",
            "project_series": "系列A",
            "project_type": "混合类",
            "project_status": "发行中",
            "project_hot": "1",
            "fee_description": "‌固定管理费‌：按年收取，费率一般为0.3%～0.9%，终身寿险型保险金信托在保险金进入账户前不收取。 ‌‌浮动管理费‌：证券投资类信托可能按盈利部分的20%收取。 ‌税费‌：包括营业税金及附加（按税法规定计提）。 ‌变更手续费‌：如增加受益人、修改合同条款等，每次变更可能收取手续费。",
        }
    ),
    ProductVO(
        **{
            "product_id": "ZX95001",
            "product_name": "招福宝玖晟5号",
            "product_status": "2",
            "risk_level": "中高风险",
            "latest_net_value": "1.5355",
            "latest_net_value_date": "2025-09-02",
            "latest_acc_net_value": "2.3455",
            "income_per_ten_thousand": "1.2323",
            "seven_day_annualized_return": "4.56%",
            "set_up_date": "2023-01-10",
            "performance_benchmark": "沪深300指数",
            "deadline": "3年",
            "deadline_type": "固定期限",
            "min_subscription_amount": "5000",
            "launch_date": "2021-06-01",
            "removal_date": None,
            "open_purchase": "每半年",  # 开放申赎
            "open_date": None,
            "next_open_date": "2025-09-25",
            "last_month_return": "-1.2%",
            "last_year_return": "8.9%",
            "product_tags": "积极｜高收益｜中高风险",
            "project_short_name": "高债",
            "project_series": "系列B",
            "project_type": "混合类",
            "project_status": "预售中",
            "project_hot": "0",
            "fee_description": "申购费0.5%，管理费1.2%",
            "shareLimitDay": "1",  # 锁定期
            "shareLimitDayUnit": "年", 	# 锁定期单位
            "fee_description": "‌固定管理费‌：按年收取，费率一般为0.3%～0.9%，终身寿险型保险金信托在保险金进入账户前不收取。 ‌‌浮动管理费‌：证券投资类信托可能按盈利部分的20%收取。 ‌税费‌：包括营业税金及附加（按税法规定计提）。 ‌变更手续费‌：如增加受益人、修改合同条款等，每次变更可能收取手续费。",
        }
    ),
    ProductVO(
        **{
            "product_id": "HX007",
            "product_name": "国投泰康信托鸿鹄7号",
            "product_status": "1",
            "risk_level": "中低风险",
            "latest_net_value": "0.987",
            "latest_net_value_date": "2024-06-19",
            "latest_acc_net_value": "1.876",
            "income_per_ten_thousand": "2.34",
            "seven_day_annualized_return": "6.78%",
            "set_up_date": "2021-12-30",
            "performance_benchmark": "国债指数*100%",
            "deadline": "5年",
            "deadline_type": "固定期限",
            "min_subscription_amount": "5000",
            "launch_date": "2021-06-01",
            "removal_date": None,
            "open_purchase": "每半年",  # 开放申赎
            "open_date": None,
            "next_open_date": "2025-09-25",
            "last_month_return": "-1.2%",
            "last_year_return": "8.9%",
            "product_tags": "债券｜成长｜中低风险",
            "project_short_name": "高债",
            "project_series": "系列B",
            "project_type": "固收类",
            "project_status": "预售中",
            "project_hot": "0",
            "fee_description": "申购费0.5%，管理费1.2%",
            "shareLimitDay": "1",  # 锁定期
            "shareLimitDayUnit": "年" 	# 锁定期单位

        }
    ),
    ProductVO(
        **{
            "product_id": "YXB003",
            "product_name": "涌信宝稳健收益3期",
            "product_status": "2",
            "risk_level": "高风险",
            "latest_net_value": "1.543",
            "latest_net_value_date": "2024-06-18",
            "latest_acc_net_value": "3.210",
            "income_per_ten_thousand": "3.45",
            "seven_day_annualized_return": "10.12%",
            "set_up_date": "2019-09-20",
            "performance_benchmark": "创业板指数",
            "deadline": "无固定期限",
            "deadline_type": "开放式",
            "min_subscription_amount": "2000",
            "launch_date": "2019-10-01",
            "removal_date": "2024-06-15",
            "open_purchase": "否",
            "open_date": None,
            "next_open_date": None,
            "last_month_return": "5.6%",
            "last_year_return": "25.4%",
            "product_tags": "科技｜创新｜高风险",
            "project_short_name": "科创",
            "project_series": "系列C",
            "project_type": "权益类",
            "project_status": "已售空",
            "project_hot": "1",
            "fee_description": "申购费1.5%，管理费1.5%",
        }
    ),
    ProductVO(
        **{
            "product_id": "MJZH001",
            "product_name": "名匠展弘1号",
            "product_status": "1",
            "risk_level": "低风险",
            "latest_net_value": "1.102",
            "latest_net_value_date": "2024-06-20",
            "latest_acc_net_value": "1.980",
            "income_per_ten_thousand": "0.98",
            "seven_day_annualized_return": "3.45%",
            "set_up_date": "2018-03-05",
            "performance_benchmark": "国债指数",
            "deadline": "2年",
            "deadline_type": "固定期限",
            "min_subscription_amount": "1000",
            "launch_date": "2018-04-01",
            "removal_date": None,
            "open_purchase": "每月1日",
            "open_date": "每月1日",
            "next_open_date": "2024-07-01",
            "last_month_return": "1.1%",
            "last_year_return": "6.7%",
            "product_tags": "债券｜稳健｜低风险",
            "project_short_name": "稳债",
            "project_series": "系列D",
            "project_type": "固收类",
            "project_status": "发行中",
            "project_hot": "0",
            "fee_description": "申购费0.3%，管理费0.6%",
            "shareLimitDay": "183",  # 锁定期
            "shareLimitDayUnit": "天"  # 锁定期单位
        }
    ),
]


# 5. 标准化组合信息 (用于测试投顾功能)

# 标准组合详细数据结构
portfolio_details = {
    "P001": {
        "基本信息": {
            "portfolio_id": "P001",
            "portfolio_name": "建议配比",
            "portfolio_type": "混合型",
            "portfolio_status": "发行中",
            "risk_level": "3",
            "risk_level_name": "中等风险",
        },
        "大类配置": {
            "固收": {"权重比例": 0.20, "资产类别": "固收"},
            "权益": {"权重比例": 0.15, "资产类别": "权益"},
            "混合": {"权重比例": 0.70, "资产类别": "混合"},
            "商品及衍生品": {"权重比例": 0.00, "资产类别": "商品及衍生品"},
        },
        "底层产品配置": [
            {
                "产品名称": "招福宝B款理财77号",
                "产品类型": "固收类",
                "风险等级": "R2中低风险",
                "产品标签": ["招福宝", "纯债类"],
                "配置比例": 0.20,
            },
            {
                "产品名称": "名匠艾方可转债1号",
                "产品类型": "混合类",
                "风险等级": "R5高风险",
                "产品标签": ["可转债轮动策略", "百亿私募", "历史业绩优异"],
                "配置比例": 0.20,
            },
            {
                "产品名称": "名匠宽德1号",
                "产品类型": "混合类",
                "风险等级": "R5高风险",
                "产品标签": ["相对策略+量化期货", "历史超额优异", "产品风控约束"],
                "配置比例": 0.50,
            },
            {
                "产品名称": "景明**************A",
                "产品类型": "权益类",
                "风险等级": "R3中风险",
                "产品标签": [],
                "配置比例": 0.10,
            }
        ],
        "组合收益指标": {
            "区间指标回测": {
                "整体": {
                    "年化收益率": 0.0830,
                    "最大回撤": -0.1362,
                    "年化波动率": 0.1293,
                    "夏普": 0.64,
                    "卡玛": 0.61
                },
                "近一年": {
                    "年化收益率": 0.0356,
                    "最大回撤": -0.1362,
                    "年化波动率": 0.1378,
                    "夏普": 0.26,
                    "卡玛": 0.26
                },
                "2025": {
                    "年化收益率": 0.1770,
                    "最大回撤": -0.0939,
                    "年化波动率": 0.2305,
                    "夏普": 0.77,
                    "卡玛": 1.88
                },
                "2024": {
                    "年化收益率": 0.0626,
                    "最大回撤": -0.0939,
                    "年化波动率": 0.0890,
                    "夏普": 0.70,
                    "卡玛": 1.03
                }
            },
            "区间胜率统计": {
                "区间总周数": 61,
                "收益上涨周数": 35,
                "收益下降周数": 26,
                "收益上涨周数比率": 0.5806,
                "收益下降周数比率": 0.4194
            },
            "区间指标超额回测": {
                "整体": {
                    "超额年化收益率": 0.1787,
                    "超额最大回撤": -0.0939,
                    "超额年化波动率": 0.1176,
                    "超额夏普": 1.52,
                    "超额卡玛": 1.90
                },
                "近一年": {
                    "超额年化收益率": 0.0356,
                    "超额最大回撤": -0.1362,
                    "超额年化波动率": 0.1378,
                    "超额夏普": 0.26,
                    "超额卡玛": 0.26
                },
                "2025": {
                    "超额年化收益率": 0.1495,
                    "超额最大回撤": -0.0939,
                    "超额年化波动率": 0.2453,
                    "超额夏普": 0.61,
                    "超额卡玛": 1.59
                },
                "2024": {
                    "超额年化收益率": 0.1904,
                    "超额最大回撤": -0.0177,
                    "超额年化波动率": 0.0503,
                    "超额夏普": 3.78,
                    "超额卡玛": 10.75
                }
            },
            "区间超额胜率统计": {
                "区间总周数": 61,
                "超额收益上涨周数": 35,
                "超额收益下降周数": 26,
                "超额收益上涨周数比率": 0.5806,
                "超额收益下降周数比率": 0.4194
            }
        }
    },
    "P002": {
        "基本信息": {
            "portfolio_id": "P002",
            "portfolio_name": "激进进取组合",
            "portfolio_type": "股票型",
            "portfolio_status": "发行中",
            "risk_level": "5",
            "risk_level_name": "高风险",
        },
        "大类配置": {
            "固收": {"权重比例": 0.10, "资产类别": "固收"},
            "权益": {"权重比例": 0.60, "资产类别": "权益"},
            "混合": {"权重比例": 0.30, "资产类别": "混合"},
            "商品及衍生品": {"权重比例": 0.00, "资产类别": "商品及衍生品"},
        },
        "底层产品配置": [
            {
                "产品名称": "招福宝B款理财88号",
                "产品类型": "固收类",
                "风险等级": "R2中低风险",
                "产品标签": ["招福宝", "纯债类"],
                "配置比例": 0.10,
            },
            {
                "产品名称": "名匠展弘1号",
                "产品类型": "权益类",
                "风险等级": "R5高风险",
                "产品标签": ["股票多头", "价值投资", "长期持有"],
                "配置比例": 0.60,
            },
            {
                "产品名称": "名匠艾方可转债2号",
                "产品类型": "混合类",
                "风险等级": "R5高风险",
                "产品标签": ["可转债轮动策略", "百亿私募"],
                "配置比例": 0.30,
            }
        ],
        "组合收益指标": {
            "区间指标回测": {
                "整体": {
                    "年化收益率": 0.1520,
                    "最大回撤": -0.2480,
                    "年化波动率": 0.2850,
                    "夏普": 0.53,
                    "卡玛": 0.61
                },
                "近一年": {
                    "年化收益率": 0.1856,
                    "最大回撤": -0.1862,
                    "年化波动率": 0.2678,
                    "夏普": 0.69,
                    "卡玛": 1.00
                }
            }
        }
    },
    "P003": {
        "基本信息": {
            "portfolio_id": "P003",
            "portfolio_name": "保守稳健组合",
            "portfolio_type": "债券型",
            "portfolio_status": "发行中",
            "risk_level": "1",
            "risk_level_name": "低风险",
        },
        "大类配置": {
            "固收": {"权重比例": 0.80, "资产类别": "固收"},
            "权益": {"权重比例": 0.00, "资产类别": "权益"},
            "混合": {"权重比例": 0.20, "资产类别": "混合"},
            "商品及衍生品": {"权重比例": 0.00, "资产类别": "商品及衍生品"},
        },
        "底层产品配置": [
            {
                "产品名称": "招福宝B款理财99号",
                "产品类型": "固收类",
                "风险等级": "R1低风险",
                "产品标签": ["招福宝", "纯债类", "稳健收益"],
                "配置比例": 0.80,
            },
            {
                "产品名称": "名匠宽德稳健1号",
                "产品类型": "混合类",
                "风险等级": "R3中风险",
                "产品标签": ["稳健策略", "低波动"],
                "配置比例": 0.20,
            }
        ],
        "组合收益指标": {
            "区间指标回测": {
                "整体": {
                    "年化收益率": 0.0425,
                    "最大回撤": -0.0512,
                    "年化波动率": 0.0623,
                    "夏普": 0.68,
                    "卡玛": 0.83
                },
                "近一年": {
                    "年化收益率": 0.0386,
                    "最大回撤": -0.0425,
                    "年化波动率": 0.0578,
                    "夏普": 0.67,
                    "卡玛": 0.91
                }
            }
        }
    }
}

# 保持原有的简单列表格式以保证兼容性
portfolios = [
    PortfolioVO(
        portfolio_id="P001",
        portfolio_name="稳健成长组合",
        portfolio_type="混合型",
        portfolio_status="发行中",
        risk_level="3",
        risk_level_name="中等风险",
        first_adjust_date="2022-01-15",
        last_adjust_date="2024-04-10",
        acc_return=0.0830,
        last_year_return=0.0356,
        max_drawdown=-0.1362,
    ),
    PortfolioVO(
        portfolio_id="P002",
        portfolio_name="宏观对冲策略",
        portfolio_type="股票型",
        portfolio_status="发行中",
        risk_level="5",
        risk_level_name="高风险",
        first_adjust_date="2021-06-01",
        last_adjust_date="2024-03-20",
        acc_return=0.1520,
        last_year_return=0.1856,
        max_drawdown=-0.2480,
    ),
    PortfolioVO(
        portfolio_id="P003",
        portfolio_name="保守稳健组合",
        portfolio_type="债券型",
        portfolio_status="发行中",
        risk_level="1",
        risk_level_name="低风险",
        first_adjust_date="2023-02-10",
        last_adjust_date="2024-05-01",
        acc_return=0.0425,
        last_year_return=0.0386,
        max_drawdown=-0.0512,
    ),
    PortfolioVO(
        portfolio_id="P004",
        portfolio_name="均衡配置组合",
        portfolio_type="混合型",
        portfolio_status="发行中",
        risk_level="2",
        risk_level_name="偏低风险",
        first_adjust_date="2022-09-05",
        last_adjust_date="2024-04-25",
        acc_return=0.0680,
        last_year_return=0.0520,
        max_drawdown=-0.0890,
    ),
]

__all__ = ["customer_info", "customer_hold_info", "customer_trade_info", "products", "portfolios", "portfolio_details"]
