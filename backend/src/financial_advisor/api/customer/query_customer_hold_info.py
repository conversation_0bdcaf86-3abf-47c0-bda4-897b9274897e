# 侧边栏服务接口-客户持仓产品信息查询
# 侧边栏服务接口-产品列表查询
# 侧边栏服务接口-信托服务总资产查询

from src.config.load_config import load_config
from src.financial_advisor.api.mock import customer_hold_info as ch_info
from src.financial_advisor.api.request import async_request, request
from src.financial_advisor.vos import CustomerHoldInfoVO


async def async_query_customer_hold_info(current_manager: dict):
    page_no = 1
    page_size = 10
    total_cnt = page_no * (page_size + 1)
    customer_hold_list = []

    while page_no * page_size < total_cnt:
        response = await async_request(
            "sidebar/qryCustPositionList",
            {
                "externalUserid": current_manager["external_user_id"],
                "brokerAccount": current_manager["work_no"],
                "pageNo": page_no,
                "pageSize": page_size,
            },
            {"Authorization": current_manager["access_token"]},
        )
        customer_hold_list.extend(response["data"]["custPositionResultList"] or [])
        total_cnt = int(response["data"]["totalCount"] or 0)
        page_no += 1

    if len(customer_hold_list) == 0:
        return {"position_info": customer_hold_list}

    position_info = [
        CustomerHoldInfoVO(
            product_name=hold["fundName"],
            product_code=hold["fundCode"],
            hold_shares=hold["readShares"],
            latest_net_value=hold["lastNetValue"],
            latest_net_date=hold["lastDdDate"],
            hold_assets=hold["holdAssets"],
            performance_benchmark=hold["profit"],
            serial_no=hold["serialNo"],
        )
        for hold in customer_hold_list
    ]

    return {"position_info": position_info}


def query_customer_hold_info(current_manager: dict):
    if load_config("MOCK_MODE") == "Y":
        return {"position_info": ch_info}

    page_no = 1
    page_size = 10
    total_cnt = page_no * (page_size + 1)
    customer_hold_list = []

    while page_no * page_size < total_cnt:
        response = request(
            "sidebar/qryCustPositionList",
            {
                "externalUserid": current_manager["external_user_id"],
                "brokerAccount": current_manager["work_no"],
                "pageNo": page_no,
                "pageSize": page_size,
            },
            {"Authorization": current_manager["access_token"]},
        )
        customer_hold_list.extend(response["data"]["custPositionResultList"] or [])
        total_cnt = int(response["data"]["totalCount"] or 0)
        page_no += 1

    if len(customer_hold_list) == 0:
        return {"position_info": customer_hold_list}

    position_info = [
        CustomerHoldInfoVO(
            product_name=hold["fundName"],
            product_code=hold["fundCode"],
            hold_shares=hold["readShares"],
            latest_net_value=hold["lastNetValue"],
            latest_net_date=hold["lastDdDate"],
            hold_assets=hold["holdAssets"],
            performance_benchmark=hold["profit"],
            serial_no=hold["serialNo"],
        )
        for hold in customer_hold_list
    ]

    return {"position_info": position_info}
