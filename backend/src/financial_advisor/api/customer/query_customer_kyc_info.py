from src.financial_advisor.api.request import async_request


async def async_query_customer_kyc_info(current_manager: dict):
    response = await async_request(
        "assetAllocation/queryKycInfo",
        {"brokerAccount": current_manager["work_no"]},
        {"Authorization": current_manager["access_token"]},
    )
    kyc_list = response["data"]["kycList"]

    if kyc_list is None:
        return []

    return kyc_list
