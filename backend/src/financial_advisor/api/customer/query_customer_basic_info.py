# 侧边栏服务接口-获取客户基本信息
# 客户统计服务接口-客户列表
from typing import Optional

from src.config.load_config import load_config
from src.financial_advisor.api.mock import customer_info as c_info
from src.financial_advisor.api.request import async_request, request
from src.financial_advisor.vos import CustomerBasicInfoVO


async def async_query_customer_basic_info(
    current_manager: dict,
    customer_name: Optional[str] = None,
    customer_no: Optional[str] = None,
):
    if load_config("MOCK_MODE") == "Y":
        return [c_info]
    page_no = 1
    page_size = 10
    total_cnt = page_no * (page_size + 1)
    customer_info_list = []

    while page_no * page_size < total_cnt:
        response = await async_request(
            "sidebar/qryCustBaseInfo",
            {
                "managerNo": current_manager["work_no"],
                "externalUserid": current_manager["external_user_id"],
                "pageNo": page_no,
                "pageSize": page_size,
            },
            {"Authorization": current_manager["access_token"]},
        )
        customer_info_list.extend(response["data"]["customerInfoList"] or [])
        total_cnt = int(response["data"]["totalCount"] or 0)
        page_no += 1

    customer_info_list = [
        customer_info
        for customer_info in customer_info_list
        if (
            customer_name is not None
            and customer_no is None
            and customer_info["custName"] == customer_name
        )
        or (
            customer_no is not None
            and customer_name is None
            and customer_info["ecifCustNo"] == customer_no
        )
        or (
            customer_name is not None
            and customer_no is not None
            and customer_info["custName"] == customer_name
            and customer_info["ecifCustNo"] == customer_no
        )
        or (customer_name is None and customer_no is None)
    ]

    if len(customer_info_list) == 0 or len(customer_info_list) > 1:
        return customer_info_list

    customer_info = customer_info_list[0]
    extra_info_list = []
    # extra_info_list = request(
    #     "manager/customerList",
    #     {
    #         "brokerAccount": current_manager["work_no"],
    #         "search": customer_info["mobilePhone"] or customer_info["custName"],
    #     },
    # )["data"]["customerList"]
    # extra_info_list = [
    #     extra_info
    #     for extra_info in extra_info_list
    #     if extra_info["ecifCustNo"] == customer_info["ecifCustNo"]
    # ]

    customer_basic_info_vo = {
        "customer_id": customer_info["ecifCustNo"],
        "customer_name": customer_info["custName"],
        "mobile_phone": customer_info["mobilePhone"],
        "gender": customer_info["sex"],
        "customer_age": customer_info["age"],
        "birthday": customer_info["birth"],
        "email": customer_info["email"],
        "address": customer_info["address"],
        "id_status": customer_info["idStatus"],
        "bank_card_count": customer_info["bankCardCount"],
        "prove_status": customer_info["proveValidFlag"],
        "prove_expires_date": customer_info["proveValidDate"],
        "risk_level_status": customer_info["corpFlag"],
        "risk_level": customer_info["endure"],
        "risk_level_name": customer_info["endureName"],
        "risk_level_expires_date": customer_info["corpEndDate"],
        "tax_id_status": customer_info["crsDeclareValidFlag"],
        "tax_id": customer_info["oriCustFlag"],
        "tax_id_name": customer_info["oriCustFlagName"],
        "tax_id_expires_date": customer_info["crsDeclareValidDate"],
        "customer_status": customer_info["customerTag"],
        "marital_status": customer_info["maritalStatus"],
        "nation": customer_info["nation"],
        "highest_education": customer_info["highestEducation"],
        "last_login_time": (
            extra_info_list[0]["lastLoginTime"] if len(extra_info_list) == 1 else None
        ),
        "total_asset": (
            extra_info_list[0]["customerAssets"]["assets"]
            if len(extra_info_list) == 1
            else None
        ),
        "total_income": (
            extra_info_list[0]["customerAssets"]["totalIncome"]
            if len(extra_info_list) == 1
            else None
        ),
    }

    return [CustomerBasicInfoVO(**customer_basic_info_vo)]


def query_customer_basic_info(
    current_manager: dict,
    customer_name: Optional[str] = None,
    customer_no: Optional[str] = None,
):
    if load_config("MOCK_MODE") == "Y":
        return [c_info]

    page_no = 1
    page_size = 10
    total_cnt = page_no * (page_size + 1)
    customer_info_list = []

    while page_no * page_size < total_cnt:
        response = request(
            "sidebar/qryCustBaseInfo",
            {
                "managerNo": current_manager["work_no"],
                "externalUserid": current_manager["external_user_id"],
                "pageNo": page_no,
                "pageSize": page_size,
            },
            {"Authorization": current_manager["access_token"]},
        )
        customer_info_list.extend(response["data"]["customerInfoList"] or [])
        total_cnt = int(response["data"]["totalCount"] or 0)
        page_no += 1

    customer_info_list = [
        customer_info
        for customer_info in customer_info_list
        if (
            customer_name is not None
            and customer_no is None
            and customer_info["custName"] == customer_name
        )
        or (
            customer_no is not None
            and customer_name is None
            and customer_info["ecifCustNo"] == customer_no
        )
        or (
            customer_name is not None
            and customer_no is not None
            and customer_info["custName"] == customer_name
            and customer_info["ecifCustNo"] == customer_no
        )
        or (customer_no is None and customer_name is None)
    ]

    if len(customer_info_list) == 0 or len(customer_info_list) > 1:
        return customer_info_list

    customer_info = customer_info_list[0]
    extra_info_list = []
    # extra_info_list = request(
    #     "manager/customerList",
    #     {
    #         "brokerAccount": current_manager["work_no"],
    #         "search": customer_info["mobilePhone"] or customer_info["custName"],
    #     },
    # )["data"]["customerList"]
    # extra_info_list = [
    #     extra_info
    #     for extra_info in extra_info_list
    #     if extra_info["ecifCustNo"] == customer_info["ecifCustNo"]
    # ]

    customer_basic_info_vo = {
        "customer_id": customer_info["ecifCustNo"],
        "customer_name": customer_info["custName"],
        "mobile_phone": customer_info["mobilePhone"],
        "gender": customer_info["sex"],
        "customer_age": customer_info["age"],
        "birthday": customer_info["birth"],
        "email": customer_info["email"],
        "address": customer_info["address"],
        "id_status": customer_info["idStatus"],
        "bank_card_count": customer_info["bankCardCount"],
        "prove_status": customer_info["proveValidFlag"],
        "prove_expires_date": customer_info["proveValidDate"],
        "risk_level_status": customer_info["corpFlag"],
        "risk_level": customer_info["endure"],
        "risk_level_name": customer_info["endureName"],
        "risk_level_expires_date": customer_info["corpEndDate"],
        "tax_id_status": customer_info["crsDeclareValidFlag"],
        "tax_id": customer_info["oriCustFlag"],
        "tax_id_name": customer_info["oriCustFlagName"],
        "tax_id_expires_date": customer_info["crsDeclareValidDate"],
        "customer_status": customer_info["customerTag"],
        "marital_status": customer_info["maritalStatus"],
        "nation": customer_info["nation"],
        "highest_education": customer_info["highestEducation"],
        "last_login_time": (
            extra_info_list[0]["lastLoginTime"] if len(extra_info_list) == 1 else None
        ),
        "holding_amount": (
            extra_info_list[0]["customerAssets"] if len(extra_info_list) == 1 else None
        ),
        "total_asset": (
            extra_info_list[0]["customerAssets"]["assets"]
            if len(extra_info_list) == 1
            else None
        ),
        "total_income": (
            extra_info_list[0]["customerAssets"]["totalIncome"]
            if len(extra_info_list) == 1
            else None
        ),
    }

    return [CustomerBasicInfoVO(**customer_basic_info_vo)]
