from langgraph_sdk import Auth

from src.utils.auth_utils import verify_token


auth = Auth()


@auth.authenticate
async def authenticate(authorization: str):
    try:
        token = authorization.split(" ")[1]
        result = await verify_token(token)
    except Exception as e:
        raise Auth.exceptions.HTTPException(status_code=401, detail="Unauthorized")
    return {**result, "identity": result["user_id"]}
