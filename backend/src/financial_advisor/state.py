from langchain_core.messages import BaseMessage
from langgraph.graph import add_messages
from typing import Any
from typing_extensions import Annotated, TypedDict


def replace_value(oldVal: str, newVal: str) -> str:
    """Reducer that replaces the old value with the new value."""
    return newVal


class State(TypedDict):
    """理财经理智能体的整体状态

    Attributes:
        conversation_history: 会话历史记录
        current_step: 当前执行的步骤
        current_step_detail: 当前执行的步骤详细信息
        messages: 会话中交换的消息列表
        next_node: 下一个节点
        next_state: 下一个状态
    """

    conversation_history: Annotated[list[BaseMessage], add_messages]
    current_step: Annotated[str, replace_value]
    current_step_detail: Annotated[str, replace_value]
    messages: Annotated[list[str], add_messages]
    next_node: str
    next_state: dict[str, Any]


class SearchState(State):
    """搜索状态

    Attributes:
        search_query: 搜索字符串
        search_result: 搜索结果
        search_sources: 搜索源
    """

    search_query: str
    search_result: list[str]
    search_sources: list[dict]


class PortfolioState(State):
    """标准组合状态

    Attributes:
        additional_info: 附加信息
        customer_basic_info: 客户基本信息
        customer_hold_info: 客户持仓组合
    """

    additional_info: str
    customer_basic_info: dict[str, Any]
    customer_hold_info: list[dict[str, Any]]


class PlanningState(PortfolioState):
    """财富规划状态

    Attributes:
        asset_allocation_message: 资产配置建议信息
        customer_type: 客户类型 1-老客户 2-新客户
        ending_action: 结束动作
        financial_planning: 财富规划字典
        product_allocation: 产品配置列表
        profile_message: 客户画像信息

    """

    asset_allocation_message: str
    customer_type: str
    ending_action: str
    financial_planning: dict[str, Any]
    product_allocation: list[dict[str, Any]]
    profile_message: str
