from fastapi import Fast<PERSON><PERSON>, APIRouter
from fastapi.middleware.cors import CORSMiddleware

from src.financial_advisor.routers.asset_allocation import asset_allocation_router
from src.financial_advisor.routers.auth import financial_advisor_auth_router
from src.financial_advisor.routers.polling import polling_router
from src.routers.guess_you_want_to_ask import guess_you_want_to_ask_router
from src.routers.thread_run_steps import thread_run_steps_router

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

router = APIRouter(prefix="/api/v1")

router.include_router(asset_allocation_router)
router.include_router(financial_advisor_auth_router)
router.include_router(polling_router)

router.include_router(guess_you_want_to_ask_router)
router.include_router(thread_run_steps_router)

app.include_router(router)
