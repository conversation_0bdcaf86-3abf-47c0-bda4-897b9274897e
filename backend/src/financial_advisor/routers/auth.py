from fastapi import APIRouter

from src.financial_advisor.dtos import AuthSignInDto
from src.utils.auth_utils import sign_token


financial_advisor_auth_router = APIRouter(prefix="/financial_advisor_auth")


@financial_advisor_auth_router.post("/sign_in")
async def sign_in(authSignInDto: AuthSignInDto):
    try:
        token = await sign_token(authSignInDto)
        return {"message": "User signed in successfully", "data": token}
    except Exception as e:
        return {"message": "Error signing in user", "error": str(e)}


__all__ = ["financial_advisor_auth_router"]
