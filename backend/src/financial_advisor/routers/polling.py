import json

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends
from fastapi.exceptions import HTTPException
from fastapi.security import HTTPAuthorizationCredentials, HTTP<PERSON>earer
from typing import Annotated

from src.db.redis import AsyncRedis
from src.financial_advisor.dtos import C<PERSON><PERSON>ollingDto
from src.utils.auth_utils import verify_token


polling_router = APIRouter(prefix="/polling")
redis = AsyncRedis()
security = HTTPBearer()


@polling_router.get("")
async def get_polling_status(
    thread_id: str,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        user = await verify_token(token.credentials)
    except Exception as e:
        raise HTTPException(status_code=401, detail="Unauthorized")

    data = await redis.get(f"polling:{user['user_id']}:{thread_id}")
    return {"data": data}


@polling_router.post("")
async def post_polling_status(
    create_polling_dto: CreatePollingDto,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        user = await verify_token(token.credentials)
    except Exception as e:
        raise HTTPException(status_code=401, detail="Unauthorized")

    await redis.set(
        f"""polling:{user['user_id']}:{create_polling_dto["thread_id"]}""",
        json.dumps(
            create_polling_dto["data"],
            ensure_ascii=False,
        ),
    )
    return {"message": "Customer basic info saved successfully"}


__all__ = ["polling_router"]
