import json

from fastapi import APIRouter, Depends
from fastapi.exceptions import HTTPException
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from openai import AsyncOpenAI
from typing import Annotated

from src.config.load_config import load_config
from src.db.redis import AsyncRedis
from src.financial_advisor.api.customer import async_query_customer_kyc_info
from src.financial_advisor.api.product import (
    async_query_allocation_products,
    async_query_indexes,
)
from src.financial_advisor.dtos import (
    AssetAllocationProductDto,
    CreateAssetAllocationDto,
    PaginationDto,
)
from src.financial_advisor.models import FinancialPlanningProduct, ProductRecommendPitch
from src.financial_advisor.prompts import PRODUCT_RECOMMEND_PITCH_PROMPT
from src.utils.auth_utils import verify_token

asset_allocation_router = APIRouter(prefix="/asset_allocation")
config = load_config(keys=["OPENAI_API_KEY", "OPENAI_BASE_URL", "OPENAI_MODEL"])
security = HTTPBearer()


@asset_allocation_router.post("/kyc_dict")
async def fetch_kyc_dict(
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        user = await verify_token(token.credentials)
    except Exception as e:
        raise HTTPException(status_code=401, detail="Unauthorized")

    kyc_info = await async_query_customer_kyc_info(user)

    data = [
        {
            "id": ki.get("kycId"),
            "type": "radio" if ki.get("formType") == "dict" else "text",
            "lable": ki.get("formTitleDict"),
            "name": ki.get("kycDict"),
            "options": (
                [
                    {"label": ci.get("prompt"), "value": ci.get("val")}
                    for ci in ki.get("childDict")
                ]
                if ki.get("formType") == "dict"
                else None
            ),
            "required": True,
        }
        for ki in kyc_info
    ]

    return {
        "message": "Asset allocation endpoint",
        "data": data,
    }


@asset_allocation_router.post("")
async def create_asset_allocation(
    create_asset_allocation_dto: CreateAssetAllocationDto,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    return {
        "message": "Asset allocation endpoint",
        "data": [
            {
                "proCode": "product_id_0041",
                "proName": "product_name_0041",
                "assetcatCode": "MM",
                "assetcatName": "混合类",
                "pfcatCode": "FUND",
                "pfcatName": "公募基金",
                "proRiskName": "平衡型",
                "proRiskCode": "C3",
                "weight": 20,
                "weightAmount": 200000,
            },
            {
                "proCode": "product_id_0042",
                "proName": "product_name_0042",
                "assetcatCode": "MM",
                "assetcatName": "混合类",
                "pfcatCode": "FUND",
                "pfcatCode": "TRUST",
                "pfcatName": "信托产品",
                "proRiskCode": "C3",
                "weight": 20,
                "weightAmount": 200000,
            },
            {
                "proCode": "product_id_0051",
                "proName": "product_name_0051",
                "assetcatCode": "EQ",
                "assetcatName": "权益类",
                "pfcatCode": "FUND",
                "pfcatName": "公募基金",
                "proRiskName": "成长型",
                "proRiskCode": "C4",
                "weight": 20,
                "weightAmount": 200000,
            },
            {
                "proCode": "product_id_0052",
                "proName": "product_name_0052",
                "assetcatCode": "EQ",
                "assetcatName": "权益类",
                "pfcatCode": "TRUST",
                "pfcatName": "信托产品",
                "proRiskName": "成长型",
                "proRiskCode": "C4",
                "weight": 20,
                "weightAmount": 200000,
            },
            {
                "proCode": "product_id_006",
                "proName": "product_name_006",
                "assetcatCode": "DCM",
                "assetcatName": "固收类",
                "pfcatCode": "TRUST",
                "pfcatName": "信托产品",
                "proRiskName": "稳健型",
                "proRiskCode": "C2",
                "weight": 10,
                "weightAmount": 100000,
            },
            {
                "proCode": "product_id_007",
                "proName": "product_name_007",
                "assetcatCode": "OTH",
                "assetcatName": "商品类及衍生品类",
                "pfcatCode": "TRUST",
                "pfcatName": "信托产品",
                "proRiskName": "保守型",
                "proRiskCode": "C1",
                "weight": 10,
                "weightAmount": 100000,
            },
        ],
    }


@asset_allocation_router.post("/indexes")
async def fetch_asset_allocation_indexes(
    asset_allocation_index_dto: PaginationDto,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        user = await verify_token(token.credentials)
    except Exception as e:
        raise HTTPException(status_code=401, detail="Unauthorized")

    data = await async_query_indexes(user, asset_allocation_index_dto)

    return {
        "message": "Asset allocation endpoint",
        "data": data,
    }


@asset_allocation_router.post("/thread/{thread_id}/products/replace")
async def fetch_asset_allocation_similar_product(
    thread_id: str,
    similar_product_replacement_dto: FinancialPlanningProduct,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        user = await verify_token(token.credentials)
    except Exception as e:
        raise HTTPException(status_code=401, detail="Unauthorized")

    similar_products = await async_query_allocation_products(
        user,
        {
            "pageNum": 1,
            "pageSize": 10,
            "assetcatCode": similar_product_replacement_dto.assetcatCode,
            "pfcatCode": similar_product_replacement_dto.pfcatCode,
            "riskCode": similar_product_replacement_dto.proRiskCode,
        },
    )

    openai_client = AsyncOpenAI(
        api_key=config["OPENAI_API_KEY"], base_url=config["OPENAI_BASE_URL"]
    )
    formatted_prompt = PRODUCT_RECOMMEND_PITCH_PROMPT.format(
        user_question="请给我推荐类似的产品",
        current_product=similar_product_replacement_dto,
        similar_products=similar_products,
    )
    resp = await openai_client.chat.completions.parse(
        model=config["OPENAI_MODEL"],
        messages=[{"role": "system", "content": formatted_prompt}],
        temperature=0.5,
        response_format=ProductRecommendPitch,
    )

    data = resp.choices[0].message.parsed
    if data is None:
        return {
            "message": "No product recommended",
            "data": similar_product_replacement_dto,
        }

    await AsyncRedis().set(
        f"polling:{user['user_id']}:{thread_id}",
        json.dumps(
            {
                "route": "/aihome/statusDesc",
                "state": {"content": data.reasoning},
            },
            ensure_ascii=False,
        ),
    )

    return {"message": "Asset allocation endpoint", "data": data.product}


@asset_allocation_router.post("/products")
async def fetch_asset_allocation_product(
    asset_allocation_product_dto: AssetAllocationProductDto,
    token: Annotated[HTTPAuthorizationCredentials, Depends(security)],
):
    try:
        user = await verify_token(token.credentials)
    except Exception as e:
        raise HTTPException(status_code=401, detail="Unauthorized")

    data = await async_query_allocation_products(
        user,
        {
            "pageNum": asset_allocation_product_dto.get("page_num"),
            "pageSize": asset_allocation_product_dto.get("page_size"),
            "assetcatCode": asset_allocation_product_dto.get("asset_cat_code"),
            "pfcatCode": asset_allocation_product_dto.get("pf_cat_code"),
            "riskCode": asset_allocation_product_dto.get("risk_code"),
        },
    )

    return {
        "message": "Asset allocation product fetched successfully",
        "data": data,
    }


__all__ = ["asset_allocation_router"]
