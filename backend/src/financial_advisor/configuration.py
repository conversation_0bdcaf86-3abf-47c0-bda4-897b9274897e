import os
from langchain_core.runnables import RunnableConfig
from pydantic import BaseModel, Field
from typing import Any, Optional

from src.config.load_config import load_config

OPENAI_MODEL = load_config("OPENAI_MODEL")


class Configuration(BaseModel):
    """The configuration for the agent."""

    query_generator_model: str = Field(
        default=OPENAI_MODEL,
        description="The name of the language model to use for the agent's query generation.",
    )

    reflection_model: str = Field(
        default=OPENAI_MODEL,
        description="The name of the language model to use for the agent's reflection.",
    )

    reasoning_model: str = Field(
        default=OPENAI_MODEL,
        description="The name of the language model to use for the agent's reasoning.",
    )

    answer_model: str = Field(
        default=OPENAI_MODEL,
        description="The name of the language model to use for the agent's answer.",
    )

    general_chat: str = Field(
        default=OPENAI_MODEL,
        description="The name of the language model to use for the general chat model.",
    )

    analyst_model: str = Field(
        default=OPENAI_MODEL,
        description="The name of the language model to use for the analyst model.",
    )

    router_model: str = Field(
        default=OPENAI_MODEL,
        description="The name of the language model to use for the routing model.",
    )

    number_of_initial_queries: int = Field(
        default=3,
        description="The number of initial search queries to generate.",
    )

    max_research_loops: int = Field(
        default=2,
        description="The maximum number of research loops to perform.",
    )

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "Configuration":
        """Create a Configuration instance from a RunnableConfig."""
        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )

        # Get raw values from environment or config
        raw_values: dict[str, Any] = {
            name: os.environ.get(name.upper(), configurable.get(name))
            for name in cls.model_fields.keys()
        }

        # Filter out None values
        values = {k: v for k, v in raw_values.items() if v is not None}

        return cls(**values)
