from typing_extensions import TypedDict


class AuthSignInDto(TypedDict):
    access_token: str
    broker_account: str
    dept_id_list: list[str]
    user_id: str
    work_no: str
    external_user_id: str


class CreateAssetAllocationDto(TypedDict):
    broker_account: str


class PaginationDto(TypedDict):
    page_num: int
    page_size: int


class AssetAllocationProductDto(PaginationDto):
    asset_cat_code: str
    pf_cat_code: str
    risk_code: str


class CreatePollingDto(TypedDict):
    thread_id: str
    data: dict
