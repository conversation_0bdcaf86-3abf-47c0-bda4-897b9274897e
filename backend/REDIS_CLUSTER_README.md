# Redis Cluster 使用指南

本目录包含了一个完整的 Redis Cluster 配置，包括 6 个节点（3 主 3 从）的标准集群。

## 快速开始

### 1. 初始化配置
```bash
# 生成所有节点的配置文件
./redis-cluster-setup.sh
```

### 2. 启动集群
```bash
# 启动所有容器
docker-compose -f docker-compose-redis-cluster.yml up -d

# 查看容器状态
docker-compose -f docker-compose-redis-cluster.yml ps
```

### 3. 验证集群
```bash
# 查看集群信息
docker exec -it redis-node-1 redis-cli -p 7001 cluster info

# 查看节点状态
docker exec -it redis-node-1 redis-cli -p 7001 cluster nodes

# 运行测试脚本
python redis-cluster-test.py
```

## 集群架构

- **节点分布**：
  - redis-node-1 (7001) - 主节点
  - redis-node-2 (7002) - 主节点
  - redis-node-3 (7003) - 主节点
  - redis-node-4 (7004) - 从节点
  - redis-node-5 (7005) - 从节点
  - redis-node-6 (7006) - 从节点

- **网络配置**：
  - 子网: **********/16
  - 节点 IP: *********** - ***********

## 连接方式

### Python (redis-py-cluster)
```python
from redis import RedisCluster

startup_nodes = [
    {"host": "localhost", "port": "7001"},
    {"host": "localhost", "port": "7002"},
    {"host": "localhost", "port": "7003"}
]

rc = RedisCluster(startup_nodes=startup_nodes, decode_responses=True)
rc.set("key", "value")
print(rc.get("key"))
```

### Redis CLI
```bash
# 连接到集群（-c 启用集群模式）
redis-cli -c -h localhost -p 7001

# 集群命令
CLUSTER INFO
CLUSTER NODES
```

### 应用配置示例
```python
# 在 .env 文件中
REDIS_CLUSTER_NODES=localhost:7001,localhost:7002,localhost:7003

# 在应用中
import os
from redis import RedisCluster

nodes = os.getenv("REDIS_CLUSTER_NODES", "localhost:7001").split(",")
startup_nodes = []
for node in nodes:
    host, port = node.split(":")
    startup_nodes.append({"host": host, "port": int(port)})

rc = RedisCluster(startup_nodes=startup_nodes, decode_responses=True)
```

## 管理命令

### 查看集群状态
```bash
# 集群信息
docker exec -it redis-node-1 redis-cli -p 7001 cluster info

# 节点列表
docker exec -it redis-node-1 redis-cli -p 7001 cluster nodes

# 槽位分配
docker exec -it redis-node-1 redis-cli -p 7001 cluster slots
```

### 添加节点
```bash
# 添加主节点
docker exec -it redis-node-1 redis-cli -p 7001 cluster meet <ip> <port>

# 添加从节点
docker exec -it redis-node-7 redis-cli -p 7007 cluster replicate <master-node-id>
```

### 重新分片
```bash
docker exec -it redis-node-1 redis-cli --cluster reshard ***********:7001
```

### 故障转移
```bash
# 手动故障转移
docker exec -it redis-node-4 redis-cli -p 7004 cluster failover

# 强制故障转移
docker exec -it redis-node-4 redis-cli -p 7004 cluster failover force
```

## 监控工具

### RedisInsight
访问 http://localhost:8001 使用 RedisInsight 图形化管理工具。

添加集群连接：
- Host: host.docker.internal 或 ***********
- Port: 7001
- Name: Redis Cluster

### 命令行监控
```bash
# 实时监控
docker exec -it redis-node-1 redis-cli -p 7001 monitor

# 慢查询日志
docker exec -it redis-node-1 redis-cli -p 7001 slowlog get 10

# 内存使用
docker exec -it redis-node-1 redis-cli -p 7001 info memory
```

## 维护操作

### 备份数据
```bash
# 备份所有节点数据
for port in 7001 7002 7003 7004 7005 7006; do
    docker exec redis-node-${port: -1} redis-cli -p $port BGSAVE
done

# 复制数据文件
tar -czf redis-cluster-backup-$(date +%Y%m%d).tar.gz redis-cluster/*/data/
```

### 停止集群
```bash
# 优雅停止
docker-compose -f docker-compose-redis-cluster.yml stop

# 停止并删除容器
docker-compose -f docker-compose-redis-cluster.yml down

# 删除所有数据
docker-compose -f docker-compose-redis-cluster.yml down -v
rm -rf redis-cluster/
```

### 重启集群
```bash
# 重启所有节点
docker-compose -f docker-compose-redis-cluster.yml restart

# 重启单个节点
docker-compose -f docker-compose-redis-cluster.yml restart redis-node-1
```

## 性能优化

### 配置调优
编辑 `redis-cluster-setup.sh` 中的配置参数：

```conf
# 最大内存设置
maxmemory 2gb
maxmemory-policy allkeys-lru

# 持久化策略
save 900 1
save 300 10
save 60 10000

# 客户端连接
maxclients 10000
```

### 网络优化
```conf
# TCP 优化
tcp-backlog 511
tcp-keepalive 300
```

## 故障排除

### 集群状态异常
```bash
# 检查集群状态
docker exec -it redis-node-1 redis-cli -p 7001 cluster info

# 修复集群
docker exec -it redis-node-1 redis-cli --cluster fix ***********:7001
```

### 节点无法连接
```bash
# 检查网络
docker network inspect backend_redis-cluster-net

# 检查防火墙
# 确保端口 7001-7006 和 17001-17006 开放
```

### 数据不一致
```bash
# 检查主从同步
docker exec -it redis-node-1 redis-cli -p 7001 info replication

# 强制全量同步
docker exec -it redis-node-4 redis-cli -p 7004 psync
```

## 安全建议

1. **生产环境配置**：
   - 启用密码认证：在 redis.conf 中添加 `requirepass yourpassword`
   - 启用 SSL/TLS
   - 限制绑定地址：`bind 127.0.0.1 ::1`

2. **网络隔离**：
   - 使用专用网络
   - 配置防火墙规则
   - 限制容器间通信

3. **数据安全**：
   - 定期备份
   - 启用 AOF 持久化
   - 监控异常访问