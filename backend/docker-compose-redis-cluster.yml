version: '3.8'

services:
  redis-node-1:
    image: redis:7-alpine
    container_name: redis-node-1
    command: redis-server /usr/local/etc/redis/redis.conf
    ports:
      - "7001:7001"
      - "17001:17001"
    volumes:
      - ./redis-cluster/7001/conf:/usr/local/etc/redis
      - ./redis-cluster/7001/data:/data
    environment:
      - REDIS_PORT=7001
    networks:
      redis-cluster-net:
        ipv4_address: ***********

  redis-node-2:
    image: redis:7-alpine
    container_name: redis-node-2
    command: redis-server /usr/local/etc/redis/redis.conf
    ports:
      - "7002:7002"
      - "17002:17002"
    volumes:
      - ./redis-cluster/7002/conf:/usr/local/etc/redis
      - ./redis-cluster/7002/data:/data
    environment:
      - REDIS_PORT=7002
    networks:
      redis-cluster-net:
        ipv4_address: ***********

  redis-node-3:
    image: redis:7-alpine
    container_name: redis-node-3
    command: redis-server /usr/local/etc/redis/redis.conf
    ports:
      - "7003:7003"
      - "17003:17003"
    volumes:
      - ./redis-cluster/7003/conf:/usr/local/etc/redis
      - ./redis-cluster/7003/data:/data
    environment:
      - REDIS_PORT=7003
    networks:
      redis-cluster-net:
        ipv4_address: ***********

  redis-node-4:
    image: redis:7-alpine
    container_name: redis-node-4
    command: redis-server /usr/local/etc/redis/redis.conf
    ports:
      - "7004:7004"
      - "17004:17004"
    volumes:
      - ./redis-cluster/7004/conf:/usr/local/etc/redis
      - ./redis-cluster/7004/data:/data
    environment:
      - REDIS_PORT=7004
    networks:
      redis-cluster-net:
        ipv4_address: ***********

  redis-node-5:
    image: redis:7-alpine
    container_name: redis-node-5
    command: redis-server /usr/local/etc/redis/redis.conf
    ports:
      - "7005:7005"
      - "17005:17005"
    volumes:
      - ./redis-cluster/7005/conf:/usr/local/etc/redis
      - ./redis-cluster/7005/data:/data
    environment:
      - REDIS_PORT=7005
    networks:
      redis-cluster-net:
        ipv4_address: ***********

  redis-node-6:
    image: redis:7-alpine
    container_name: redis-node-6
    command: redis-server /usr/local/etc/redis/redis.conf
    ports:
      - "7006:7006"
      - "17006:17006"
    volumes:
      - ./redis-cluster/7006/conf:/usr/local/etc/redis
      - ./redis-cluster/7006/data:/data
    environment:
      - REDIS_PORT=7006
    networks:
      redis-cluster-net:
        ipv4_address: ***********

  # Redis Cluster 初始化容器
  redis-cluster-init:
    image: redis:7-alpine
    container_name: redis-cluster-init
    depends_on:
      - redis-node-1
      - redis-node-2
      - redis-node-3
      - redis-node-4
      - redis-node-5
      - redis-node-6
    command: |
      sh -c '
        sleep 10
        redis-cli --cluster create \
          ***********:7001 \
          ***********:7002 \
          ***********:7003 \
          ***********:7004 \
          ***********:7005 \
          ***********:7006 \
          --cluster-replicas 1 --cluster-yes
      '
    networks:
      - redis-cluster-net

  # Redis 监控工具 - RedisInsight（可选）
  redis-insight:
    image: redislabs/redisinsight:latest
    container_name: redis-insight
    ports:
      - "8001:8001"
    volumes:
      - ./redis-insight:/db
    networks:
      - redis-cluster-net

networks:
  redis-cluster-net:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16