# Dify同步功能快速开始指南

## 1. 使用Mock服务器测试（推荐）

### 步骤1: 启动Mock服务器
```bash
# 使用默认端口8889
./mock_server/start_mock.sh

# 或指定其他端口
./mock_server/start_mock.sh 9999
```

### 步骤2: 配置环境变量
在 `.env` 文件中设置：
```env
WEALTH_APP_BASE_URL=http://localhost:8889
WEALTH_APP_SECRET_KEY=1234567890123456
```

### 步骤3: 测试同步功能
```bash
# 测试本地文件同步
python -m src.dify_sync.sync_manager --mode local --folder mock_server/sample_files --dry-run

# 测试财富APP同步（需要Mock服务器运行）
python -m src.dify_sync.sync_manager --mode wealth --dry-run

# 测试全部同步
python -m src.dify_sync.sync_manager --mode all --dry-run
```

## 2. 连接真实财富APP服务器

### 配置真实环境
在 `.env` 文件中设置：
```env
# SIT环境
WEALTH_APP_BASE_URL=http://***********:8888
WEALTH_APP_SECRET_KEY=your_actual_secret_key

# TEST环境
WEALTH_APP_BASE_URL=http://***********:8888
WEALTH_APP_SECRET_KEY=your_actual_secret_key

# PROD环境
WEALTH_APP_BASE_URL=http://************:8888
WEALTH_APP_SECRET_KEY=your_actual_secret_key
```

## 3. 故障排除

### 检查配置
```bash
python mock_server/check_config.py
```

### 测试连接
```bash
python mock_server/test_connection.py
```

### 常见问题

1. **"Expecting value: line 1 column 1" 错误**
   - 原因：服务器返回的不是JSON格式
   - 解决：检查URL是否正确，使用Mock服务器测试

2. **连接被拒绝**
   - 原因：服务器未启动或端口错误
   - 解决：确认服务器已启动，检查端口号

3. **密钥错误**
   - 原因：加密密钥不匹配
   - 解决：确认密钥长度为16或32位

## 4. 完整示例

```bash
# 1. 启动Mock服务器（新终端）
./mock_server/start_mock.sh 8889

# 2. 设置环境变量
export WEALTH_APP_BASE_URL=http://localhost:8889
export WEALTH_APP_SECRET_KEY=1234567890123456

# 3. 运行同步（模拟模式）
python -m src.dify_sync.sync_manager --mode wealth --dry-run

# 4. 查看日志
tail -f dify_sync_*.log
```