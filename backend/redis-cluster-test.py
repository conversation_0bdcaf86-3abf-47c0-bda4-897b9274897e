"""
Redis Cluster 连接测试脚本
"""
import sys
import time
from redis import RedisCluster, RedisError


def test_redis_cluster():
    """测试 Redis Cluster 连接和基本操作"""
    print("Redis Cluster 测试")
    print("=" * 50)
    
    # 定义启动节点
    startup_nodes = [
        {"host": "localhost", "port": "7001"},
        {"host": "localhost", "port": "7002"},
        {"host": "localhost", "port": "7003"},
        {"host": "localhost", "port": "7004"},
        {"host": "localhost", "port": "7005"},
        {"host": "localhost", "port": "7006"},
    ]
    
    try:
        # 连接到集群
        print("\n1. 连接到 Redis Cluster...")
        rc = RedisCluster(startup_nodes=startup_nodes, decode_responses=True)
        print("✓ 连接成功！")
        
        # 获取集群信息
        print("\n2. 集群信息:")
        info = rc.cluster_info()
        print(f"   集群状态: {info.get('cluster_state', 'unknown')}")
        print(f"   已知节点数: {info.get('cluster_known_nodes', 0)}")
        print(f"   集群大小: {info.get('cluster_size', 0)}")
        
        # 测试基本操作
        print("\n3. 测试基本操作:")
        
        # SET 操作
        test_keys = []
        for i in range(10):
            key = f"test:key:{i}"
            value = f"value_{i}_{int(time.time())}"
            rc.set(key, value)
            test_keys.append(key)
            print(f"   SET {key} = {value}")
        
        # GET 操作
        print("\n4. 验证数据:")
        for key in test_keys[:3]:  # 只显示前3个
            value = rc.get(key)
            print(f"   GET {key} = {value}")
        
        # 查看键的分布
        print("\n5. 键的分布情况:")
        nodes = rc.cluster_nodes()
        master_nodes = [n for n in nodes if 'master' in n.get('flags', [])]
        
        for node in master_nodes[:3]:  # 只显示前3个主节点
            node_id = node['node_id']
            host = node['host']
            port = node['port']
            slots = node.get('slots', [])
            
            if slots:
                slot_ranges = []
                start = slots[0][0]
                end = slots[0][1]
                
                for slot_range in slots[1:]:
                    if slot_range[0] == end + 1:
                        end = slot_range[1]
                    else:
                        slot_ranges.append(f"{start}-{end}")
                        start = slot_range[0]
                        end = slot_range[1]
                slot_ranges.append(f"{start}-{end}")
                
                print(f"   节点 {host}:{port} 负责槽位: {', '.join(slot_ranges)}")
        
        # 测试管道操作
        print("\n6. 测试管道操作:")
        pipe = rc.pipeline()
        for i in range(5):
            pipe.incr(f"counter:{i}")
        results = pipe.execute()
        print(f"   执行了 {len(results)} 个 INCR 操作")
        
        # 清理测试数据
        print("\n7. 清理测试数据...")
        for key in test_keys:
            rc.delete(key)
        for i in range(5):
            rc.delete(f"counter:{i}")
        print("✓ 清理完成！")
        
        print("\n✅ 所有测试通过！Redis Cluster 运行正常。")
        
    except RedisError as e:
        print(f"\n❌ Redis 错误: {e}")
        print("\n请确保:")
        print("1. Redis Cluster 已启动")
        print("2. 所有节点都可访问")
        print("3. 集群已正确初始化")
        return False
    except Exception as e:
        print(f"\n❌ 错误: {e}")
        return False
    
    return True


def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--wait":
        # 等待集群启动
        print("等待 Redis Cluster 启动...")
        for i in range(30):
            try:
                rc = RedisCluster(
                    startup_nodes=[{"host": "localhost", "port": "7001"}],
                    decode_responses=True
                )
                rc.ping()
                print("✓ Redis Cluster 已就绪！")
                break
            except:
                print(f"等待中... ({i+1}/30)")
                time.sleep(1)
        else:
            print("❌ Redis Cluster 启动超时")
            return
    
    test_redis_cluster()


if __name__ == "__main__":
    main()