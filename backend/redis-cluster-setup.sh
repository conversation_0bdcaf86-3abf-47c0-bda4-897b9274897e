#!/bin/bash

# Redis Cluster 配置文件生成脚本

echo "创建 Redis Cluster 配置目录..."

# 创建配置目录
for port in 7001 7002 7003 7004 7005 7006; do
    mkdir -p redis-cluster/${port}/conf
    mkdir -p redis-cluster/${port}/data
done

# 生成每个节点的配置文件
for port in 7001 7002 7003 7004 7005 7006; do
    cat > redis-cluster/${port}/conf/redis.conf << EOF
# Redis 配置文件
port ${port}
bind 0.0.0.0
protected-mode no

# 集群配置
cluster-enabled yes
cluster-config-file nodes.conf
cluster-node-timeout 5000
cluster-announce-ip 172.20.0.$((port - 6990))
cluster-announce-port ${port}
cluster-announce-bus-port 1${port}

# 持久化配置
appendonly yes
appendfsync everysec
dir /data

# 日志
loglevel notice
logfile ""

# 性能优化
tcp-backlog 511
timeout 0
tcp-keepalive 300

# 内存管理
maxmemory-policy allkeys-lru

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 客户端输出缓冲限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60
EOF
    echo "生成配置文件: redis-cluster/${port}/conf/redis.conf"
done

echo ""
echo "配置文件生成完成！"
echo ""
echo "使用方法："
echo "1. 启动集群: docker-compose -f docker-compose-redis-cluster.yml up -d"
echo "2. 查看集群状态: docker exec -it redis-node-1 redis-cli -p 7001 cluster info"
echo "3. 查看节点信息: docker exec -it redis-node-1 redis-cli -p 7001 cluster nodes"
echo "4. 停止集群: docker-compose -f docker-compose-redis-cluster.yml down"
echo "5. 清理数据: rm -rf redis-cluster/"
echo ""
echo "访问 RedisInsight: http://localhost:8001"
echo ""
echo "连接集群示例："
echo "- Python: redis.RedisCluster(host='localhost', port=7001)"
echo "- CLI: redis-cli -c -h localhost -p 7001"