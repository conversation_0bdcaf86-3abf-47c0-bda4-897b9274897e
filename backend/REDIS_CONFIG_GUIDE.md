# Redis 配置指南

本项目支持 Redis 单机模式和集群模式，可以通过环境变量灵活切换。

## 配置选项

### 基础配置
```env
# Redis 基础配置
REDIS_HOST=localhost          # Redis 主机地址（单机模式使用）
REDIS_PORT=6379              # Redis 端口（单机模式使用）
REDIS_PASSWORD=              # Redis 密码（可选）
REDIS_PREFIX=financial_advisor  # 键前缀，用于隔离不同应用
REDIS_MAX_CONNECTIONS=50     # 最大连接数

# 模式选择
REDIS_USE_CLUSTER=false      # false=单机模式，true=集群模式
```

### 集群配置
```env
# 启用集群模式
REDIS_USE_CLUSTER=true

# 集群节点列表（逗号分隔）
REDIS_CLUSTER_NODES=localhost:7001,localhost:7002,localhost:7003
```

## 使用示例

### 1. 单机模式（默认）

最简单的配置，适合开发和测试：

```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_USE_CLUSTER=false
```

### 2. 集群模式

适合生产环境，提供高可用性：

```env
REDIS_USE_CLUSTER=true
REDIS_CLUSTER_NODES=localhost:7001,localhost:7002,localhost:7003,localhost:7004,localhost:7005,localhost:7006
```

### 3. 云服务 Redis

#### AWS ElastiCache
```env
# 单机模式
REDIS_HOST=your-redis-instance.abc123.ng.0001.use1.cache.amazonaws.com
REDIS_PORT=6379
REDIS_PASSWORD=your-auth-token
REDIS_USE_CLUSTER=false

# 集群模式
REDIS_USE_CLUSTER=true
REDIS_CLUSTER_NODES=cluster-endpoint.abc123.clustercfg.use1.cache.amazonaws.com:6379
```

#### 阿里云 Redis
```env
# 单机/主从
REDIS_HOST=r-bp1xxxxx.redis.rds.aliyuncs.com
REDIS_PORT=6379
REDIS_PASSWORD=your-password
REDIS_USE_CLUSTER=false

# 集群版
REDIS_USE_CLUSTER=true
REDIS_CLUSTER_NODES=r-bp1xxxxx.redis.rds.aliyuncs.com:6379
```

## 代码使用

### 同步客户端
```python
from src.db.redis import get_redis

# 获取 Redis 客户端（自动识别单机/集群）
redis_client = get_redis()

# 使用示例
redis_client.set("key", "value")
value = redis_client.get("key")
```

### 异步客户端
```python
from src.db.redis import get_async_redis

# 获取异步 Redis 客户端
async_redis = get_async_redis()

# 使用示例
await async_redis.set("key", "value")
value = await async_redis.get("key")
```

### 直接创建实例
```python
from src.db.redis import Redis, AsyncRedis

# 创建新实例（不推荐，建议使用单例）
redis = Redis()
async_redis = AsyncRedis()
```

## 测试配置

运行测试脚本验证配置：

```bash
# 测试当前配置
python test_redis_config.py

# 测试单机模式
REDIS_USE_CLUSTER=false python test_redis_config.py

# 测试集群模式
REDIS_USE_CLUSTER=true REDIS_CLUSTER_NODES=localhost:7001,localhost:7002,localhost:7003 python test_redis_config.py
```

## 本地开发

### 启动单机 Redis
```bash
# Docker
docker run -d -p 6379:6379 redis:7-alpine

# 或使用 docker-compose
docker-compose up -d redis
```

### 启动 Redis 集群
```bash
# 使用提供的集群配置
./redis-cluster-setup.sh
docker-compose -f docker-compose-redis-cluster.yml up -d
```

## 切换模式

### 从单机切换到集群
```bash
# 1. 启动集群
docker-compose -f docker-compose-redis-cluster.yml up -d

# 2. 更新 .env
REDIS_USE_CLUSTER=true
REDIS_CLUSTER_NODES=localhost:7001,localhost:7002,localhost:7003

# 3. 重启应用
```

### 从集群切换到单机
```bash
# 1. 更新 .env
REDIS_USE_CLUSTER=false
REDIS_HOST=localhost
REDIS_PORT=6379

# 2. 重启应用
```

## 性能优化

### 连接池配置
```env
# 根据并发量调整
REDIS_MAX_CONNECTIONS=100  # 高并发场景
REDIS_MAX_CONNECTIONS=20   # 低并发场景
```

### 键前缀策略
```env
# 开发环境
REDIS_PREFIX=dev_financial_advisor

# 测试环境
REDIS_PREFIX=test_financial_advisor

# 生产环境
REDIS_PREFIX=prod_financial_advisor
```

## 故障排除

### 连接失败
1. 检查 Redis 服务是否启动
2. 验证网络连接：`telnet localhost 6379`
3. 检查防火墙设置
4. 验证密码是否正确

### 集群模式问题
1. 确保所有节点都可访问
2. 检查集群状态：`redis-cli -c -h localhost -p 7001 cluster info`
3. 验证节点配置是否正确

### 性能问题
1. 检查连接池大小
2. 监控 Redis 内存使用
3. 检查网络延迟
4. 考虑使用管道（pipeline）批量操作

## 监控建议

### 关键指标
- 连接数：`INFO clients`
- 内存使用：`INFO memory`
- 命令统计：`INFO commandstats`
- 慢查询：`SLOWLOG get 10`

### 日志配置
应用会自动记录 Redis 连接信息：
- 单机模式：`使用 Redis 单机模式: host:port`
- 集群模式：`使用 Redis 集群模式，节点: [...]`