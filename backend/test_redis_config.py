"""
测试 Redis 配置（单机/集群模式）
"""
import asyncio
import os
import sys
from datetime import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.db.redis import AsyncRedis, Redis, get_async_redis, get_redis
from loguru import logger


async def test_async_redis():
    """测试异步 Redis 连接"""
    print("\n=== 测试异步 Redis 连接 ===")
    
    try:
        # 使用单例
        async_redis = get_async_redis()
        
        # 测试基本操作
        test_key = f"test:async:{datetime.now().timestamp()}"
        test_value = "Hello from Async Redis!"
        
        # SET
        await async_redis.set(test_key, test_value, ex=60)
        print(f"✓ SET {test_key}")
        
        # GET
        value = await async_redis.get(test_key)
        print(f"✓ GET {test_key} = {value}")
        
        # EXISTS
        exists = await async_redis.exists(test_key)
        print(f"✓ EXISTS {test_key} = {exists}")
        
        # 队列操作
        queue_key = f"test:queue:async"
        await async_redis.enqueue(queue_key, "item1")
        await async_redis.enqueue(queue_key, "item2")
        item = await async_redis.dequeue(queue_key)
        print(f"✓ QUEUE operations: dequeued = {item}")
        
        # 清理
        await async_redis.delete(test_key)
        await async_redis.delete(queue_key)
        print("✓ 清理测试数据")
        
        # 关闭连接
        await async_redis.close()
        
    except Exception as e:
        print(f"✗ 异步 Redis 测试失败: {e}")
        return False
    
    return True


def test_sync_redis():
    """测试同步 Redis 连接"""
    print("\n=== 测试同步 Redis 连接 ===")
    
    try:
        # 使用单例
        sync_redis = get_redis()
        
        # 测试基本操作
        test_key = f"test:sync:{datetime.now().timestamp()}"
        test_value = "Hello from Sync Redis!"
        
        # SET
        sync_redis.set(test_key, test_value, ex=60)
        print(f"✓ SET {test_key}")
        
        # GET
        value = sync_redis.get(test_key)
        print(f"✓ GET {test_key} = {value}")
        
        # EXISTS
        exists = sync_redis.exists(test_key)
        print(f"✓ EXISTS {test_key} = {exists}")
        
        # 队列操作
        queue_key = f"test:queue:sync"
        sync_redis.enqueue(queue_key, "item1")
        sync_redis.enqueue(queue_key, "item2")
        item = sync_redis.dequeue(queue_key)
        print(f"✓ QUEUE operations: dequeued = {item}")
        
        # 清理
        sync_redis.delete(test_key)
        sync_redis.delete(queue_key)
        print("✓ 清理测试数据")
        
        # 关闭连接
        sync_redis.close()
        
    except Exception as e:
        print(f"✗ 同步 Redis 测试失败: {e}")
        return False
    
    return True


def show_current_config():
    """显示当前 Redis 配置"""
    print("\n=== 当前 Redis 配置 ===")
    
    use_cluster = os.getenv("REDIS_USE_CLUSTER", "false").lower() == "true"
    
    if use_cluster:
        print("模式: 集群模式")
        cluster_nodes = os.getenv("REDIS_CLUSTER_NODES", "")
        if cluster_nodes:
            print(f"集群节点: {cluster_nodes}")
        else:
            print(f"集群节点: 未配置（将使用单节点集群 {os.getenv('REDIS_HOST')}:{os.getenv('REDIS_PORT')}）")
    else:
        print("模式: 单机模式")
        print(f"主机: {os.getenv('REDIS_HOST', 'localhost')}")
        print(f"端口: {os.getenv('REDIS_PORT', '6379')}")
    
    print(f"密码: {'已设置' if os.getenv('REDIS_PASSWORD') else '未设置'}")
    print(f"前缀: {os.getenv('REDIS_PREFIX', '')}")
    print(f"最大连接数: {os.getenv('REDIS_MAX_CONNECTIONS', '50')}")


async def main():
    """主测试函数"""
    print("Redis 配置测试工具")
    print("=" * 50)
    
    # 显示当前配置
    show_current_config()
    
    # 测试同步客户端
    sync_ok = test_sync_redis()
    
    # 测试异步客户端
    async_ok = await test_async_redis()
    
    # 结果汇总
    print("\n=== 测试结果 ===")
    if sync_ok and async_ok:
        print("✅ 所有测试通过！")
    else:
        print("❌ 部分测试失败")
        if not sync_ok:
            print("  - 同步 Redis 测试失败")
        if not async_ok:
            print("  - 异步 Redis 测试失败")
    
    # 使用建议
    print("\n=== 配置示例 ===")
    print("\n1. 单机模式（默认）:")
    print("   REDIS_USE_CLUSTER=false")
    print("   REDIS_HOST=localhost")
    print("   REDIS_PORT=6379")
    
    print("\n2. 集群模式:")
    print("   REDIS_USE_CLUSTER=true")
    print("   REDIS_CLUSTER_NODES=localhost:7001,localhost:7002,localhost:7003")
    
    print("\n3. 切换模式:")
    print("   # 切换到集群")
    print("   export REDIS_USE_CLUSTER=true")
    print("   export REDIS_CLUSTER_NODES=localhost:7001,localhost:7002,localhost:7003")
    print("   # 切换回单机")
    print("   export REDIS_USE_CLUSTER=false")


if __name__ == "__main__":
    # 设置日志级别
    logger.remove()
    logger.add(sys.stderr, level="INFO")
    
    # 运行测试
    asyncio.run(main())