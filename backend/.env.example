# LangGraph Configuration
LANGGRAPH_API_URL=http://localhost:2024

# Gemini API Key (Required)
GEMINI_API_KEY=your_gemini_api_key_here

# LangSmith API Key (Optional)
LANGSMITH_API_KEY=your_langsmith_api_key_here

# Database Configuration
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=financial_advisor

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_PREFIX=financial_advisor
REDIS_MAX_CONNECTIONS=50

# Redis Cluster Configuration (optional)
# Set REDIS_USE_CLUSTER=true to enable cluster mode
REDIS_USE_CLUSTER=false
# Comma-separated list of cluster nodes (only used when REDIS_USE_CLUSTER=true)
# Example: localhost:7001,localhost:7002,localhost:7003
REDIS_CLUSTER_NODES=

# Dify Configuration
DIFY_API_KEY=your_dify_api_key
DIFY_BASE_URL=https://api.dify.ai/v1
DIFY_EMBEDDING_MODEL_NAME=text-embedding-ada-002
DIFY_EMBEDDING_PROVIDER_NAME=openai
DIFY_RERANKING_MODEL_NAME=rerank-multilingual-v2.0
DIFY_RERANKING_PROVIDER_NAME=cohere
DIFY_RETRIEVAL_TOP_K=20
DIFY_RETRIEVAL_SCORE_THRESHOLD=0.5
DIFY_WEIGHT_KEYWORD=0.3
DIFY_WEIGHT_VECTOR=0.7

# Dify Sync Configuration
# 本地文件夹同步路径
DIFY_SYNC_LOCAL_FOLDER=/path/to/your/documents/folder

# 财富APP配置
# 基础URL - 必填，可以配置为实际环境或Mock服务器地址
WEALTH_APP_BASE_URL=http://***********:8888  # 必填
WEALTH_APP_SECRET_KEY=your_wealth_app_secret_key  # 16位或32位加密密钥

# 财富APP环境URL参考
# SIT环境: http://***********:8888
# TEST环境: http://***********:8888
# PROD环境: http://************:8888
# Mock服务器: http://localhost:8888

# Authentication
JWT_SECRET=your_jwt_secret_key
JWT_ALGORITHM=HS256
JWT_EXPIRATION_DELTA=3600

# CORS Settings
CORS_ALLOW_ORIGINS=["*"]
CORS_ALLOW_METHODS=["GET", "POST", "PUT", "DELETE"]
CORS_ALLOW_HEADERS=["*"]

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Application Settings
APP_ENV=development
DEBUG=true