#!/usr/bin/env python
"""Test script to find the correct import for LangGraph MySQL checkpointer."""

import sys

# Try different import paths
imports_to_try = [
    "from langgraph.checkpoint.mysql.pymysql import PyMySQLSaver",
    "from langgraph.checkpoint.mysql import MySQLSaver",
    "from langgraph_checkpoint_mysql import MySQLSaver",
    "from langgraph_checkpoint_mysql.pymysql import PyMySQLSaver",
    "import langgraph.checkpoint.mysql",
    "import langgraph_checkpoint_mysql",
]

for imp in imports_to_try:
    try:
        exec(imp)
        print(f"✅ Success: {imp}")
        break
    except ImportError as e:
        print(f"❌ Failed: {imp}")
        print(f"   Error: {e}")
else:
    print("\n⚠️  No import path worked!")
    
# Let's also check what's in langgraph.checkpoint
try:
    import langgraph.checkpoint
    print("\nContents of langgraph.checkpoint:")
    print(dir(langgraph.checkpoint))
except ImportError:
    print("\nCould not import langgraph.checkpoint")