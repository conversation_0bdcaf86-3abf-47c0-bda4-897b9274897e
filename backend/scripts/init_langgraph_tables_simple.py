#!/usr/bin/env python
"""
Initialize LangGraph internal tables in MySQL database.
Simple version based on user's example.
"""
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from langgraph.checkpoint.mysql.pymysql import PyMySQLSaver
from src.config.load_config import load_config

config = load_config(["MYSQL_URI"])

DB_URI = config.get("MYSQL_URI")  # "mysql://user:pass@host:port/dbname""MYSQL_URI"]

print("Connecting to database...")
print(f"Using URI: {DB_URI}")

try:
    with PyMySQLSaver.from_conn_string(DB_URI) as checkpointer:
        print("Setting up LangGraph tables...")
        checkpointer.setup()
        print("✅ LangGraph tables initialized successfully!")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()