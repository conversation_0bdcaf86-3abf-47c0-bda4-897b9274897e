# Backend Scripts

This directory contains utility scripts for the backend.

## Scripts

### init_langgraph_tables.py

Initializes LangGraph internal tables in the MySQL database. This script creates the necessary tables for LangGraph's checkpointing system.

**Prerequisites:**
- Python 3.11+
- MySQL database access
- Required packages: `langgraph-checkpoint-mysql`, `pymysql`

**Usage:**

```bash
# From the backend directory
# Set environment variables first
export MYSQL_HOST=your-mysql-host
export MYSQL_PORT=3306
export MYSQL_USER=your-mysql-user
export MYSQL_PASSWORD=your-mysql-password
export MYSQL_DATABASE=your-database-name

# Run the script
python scripts/init_langgraph_tables.py

# Or make it executable and run directly
./scripts/init_langgraph_tables.py
```

**What it does:**
- Reads database configuration from environment variables or config files
- Connects to MySQL database
- Creates LangGraph checkpoint tables using PyMySQLSaver
- Provides feedback on initialization success/failure

**Environment Variables:**
- `MYSQL_HOST`: Database host (default: localhost)
- `MYSQL_PORT`: Database port (default: 3306)
- `MYSQL_USER`: Database user (default: mysql)
- `MYSQL_PASSWORD`: Database password (default: mysql)
- `MYSQL_DATABASE`: Database name (default: mysql)

**Note:** Run this script once during initial setup or when setting up a new database instance.

### init_langgraph_tables_simple.py

A simpler version of the initialization script that uses hardcoded database credentials.

**Usage:**
```bash
# Edit the script to update DB_URI with your credentials
vim scripts/init_langgraph_tables_simple.py

# Run the script
python scripts/init_langgraph_tables_simple.py
```

**Note:** This is useful for quick testing but should not be used in production. Always use environment variables for production deployments.