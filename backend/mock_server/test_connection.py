"""测试财富APP连接"""
import os
import sys
import requests
from datetime import datetime, timedelta

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.config.load_config import load_config
from src.dify_sync.api.wealth_app_client import WealthAppClient
from loguru import logger


def test_direct_connection():
    """测试直接HTTP连接"""
    config = load_config(["WEALTH_APP_BASE_URL"])
    base_url = config.get("WEALTH_APP_BASE_URL")
    
    print(f"\n1. 测试直接连接到: {base_url}")
    print("-" * 50)
    
    try:
        # 测试根路径
        response = requests.get(base_url, timeout=5)
        print(f"GET {base_url} - 状态码: {response.status_code}")
        print(f"响应内容: {response.text[:200]}...")
        
        # 测试API路径
        api_url = f"{base_url}/openapi/ai/disCloseFileQry.do"
        print(f"\nPOST {api_url}")
        response = requests.post(api_url, json={"data": "test"}, timeout=5)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text[:200]}...")
        
    except requests.exceptions.ConnectionError as e:
        print(f"连接失败: {e}")
        print("\n可能的原因:")
        print("1. 服务器未启动")
        print("2. 地址或端口错误")
        print("3. 防火墙阻止连接")
    except Exception as e:
        print(f"错误: {e}")


def test_wealth_app_client():
    """测试WealthAppClient"""
    config = load_config(["WEALTH_APP_BASE_URL", "WEALTH_APP_SECRET_KEY"])
    base_url = config.get("WEALTH_APP_BASE_URL")
    secret_key = config.get("WEALTH_APP_SECRET_KEY")
    
    if not base_url or not secret_key:
        print("\n2. 无法测试WealthAppClient - 配置缺失")
        return
    
    print(f"\n2. 测试WealthAppClient")
    print("-" * 50)
    
    try:
        client = WealthAppClient(base_url, secret_key)
        query_date = datetime.now() - timedelta(days=30)
        
        # 启用debug日志
        logger.enable("src.dify_sync.api.wealth_app_client")
        
        print(f"查询日期: {query_date.strftime('%Y-%m-%d')}")
        result = client.query_disclosure_files(query_date, page_size=1, page_no=1)
        print(f"查询成功! 返回数据: {result}")
        
    except Exception as e:
        print(f"查询失败: {e}")


def suggest_solution():
    """建议解决方案"""
    print("\n3. 建议解决方案")
    print("-" * 50)
    print("如果连接失败，请尝试:")
    print("\n选项1: 使用Mock服务器测试")
    print("1. 启动Mock服务器: ./mock_server/start_mock.sh")
    print("2. 在.env中设置:")
    print("   WEALTH_APP_BASE_URL=http://localhost:8888")
    print("   WEALTH_APP_SECRET_KEY=1234567890123456")
    print("\n选项2: 连接真实服务器")
    print("1. 确认服务器地址和密钥正确")
    print("2. 检查网络连接和防火墙设置")
    print("3. 确认服务器API格式是否符合预期")


def main():
    print("财富APP连接测试")
    print("=" * 50)
    
    test_direct_connection()
    test_wealth_app_client()
    suggest_solution()


if __name__ == "__main__":
    main()