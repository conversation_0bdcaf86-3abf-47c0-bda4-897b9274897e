#!/bin/bash

# 启动财富APP Mock服务器脚本

echo "启动财富APP Mock服务器..."

# 切换到backend目录
cd "$(dirname "$0")/.." || exit

# 检查虚拟环境
if [ ! -d ".venv" ]; then
    echo "错误: 未找到虚拟环境，请先运行 'uv venv' 创建虚拟环境"
    exit 1
fi

# 激活虚拟环境
source .venv/bin/activate

# 设置PYTHONPATH
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 设置环境变量（用于测试）
export WEALTH_APP_BASE_URL="http://localhost:8888"
export WEALTH_APP_SECRET_KEY="1234567890123456"

# 获取端口参数，默认8889
PORT=${1:-8889}

# 更新环境变量
export WEALTH_APP_BASE_URL="http://localhost:$PORT"

# 启动Mock服务器
echo "Mock服务器将在 http://localhost:$PORT 启动"
echo "使用密钥: 1234567890123456"
echo "按 Ctrl+C 停止服务器"
echo ""
echo "在.env中设置以下配置:"
echo "WEALTH_APP_BASE_URL=http://localhost:$PORT"
echo "WEALTH_APP_SECRET_KEY=1234567890123456"
echo ""

python mock_server/wealth_app_mock/mock_server.py $PORT