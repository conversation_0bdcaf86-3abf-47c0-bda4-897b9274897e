"""
测试Dify同步功能的脚本
使用Mock服务器进行测试
"""
import os
import sys
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from src.dify_sync.sync_manager import DifySyncManager


def test_local_sync():
    """测试本地文件夹同步"""
    logger.info("=== 测试本地文件夹同步 ===")
    
    # 使用mock_server的示例文件目录
    sample_dir = os.path.join(os.path.dirname(__file__), "sample_files")
    
    # 设置环境变量
    os.environ["DIFY_SYNC_LOCAL_FOLDER"] = sample_dir
    
    # 执行同步（模拟运行）
    stats = DifySyncManager.sync_local_only(dry_run=True)
    
    logger.info(f"同步统计: {stats}")
    return stats


def test_wealth_app_sync():
    """测试财富APP文件同步"""
    logger.info("=== 测试财富APP文件同步 ===")
    
    # 设置Mock服务器的环境变量
    os.environ["WEALTH_APP_BASE_URL"] = "http://localhost:8888"
    os.environ["WEALTH_APP_SECRET_KEY"] = "1234567890123456"
    
    # 执行同步（模拟运行）
    stats = DifySyncManager.sync_wealth_app_only(dry_run=True)
    
    logger.info(f"同步统计: {stats}")
    return stats


def test_all_sync():
    """测试全部同步"""
    logger.info("=== 测试全部同步 ===")
    
    # 设置所需的环境变量
    sample_dir = os.path.join(os.path.dirname(__file__), "sample_files")
    os.environ["DIFY_SYNC_LOCAL_FOLDER"] = sample_dir
    os.environ["WEALTH_APP_BASE_URL"] = "http://localhost:8888"
    os.environ["WEALTH_APP_SECRET_KEY"] = "1234567890123456"
    
    # 执行同步（模拟运行）
    stats = DifySyncManager.sync_all(dry_run=True)
    
    logger.info(f"总体统计: {stats['total']}")
    return stats


def main():
    """主测试函数"""
    logger.add("test_sync_{time}.log", rotation="10 MB")
    
    print("\n" + "="*50)
    print("Dify同步功能测试")
    print("="*50 + "\n")
    
    print("请选择测试模式：")
    print("1. 测试本地文件夹同步")
    print("2. 测试财富APP同步（需要先启动Mock服务器）")
    print("3. 测试全部同步")
    print("0. 退出")
    
    choice = input("\n请输入选择 (0-3): ")
    
    try:
        if choice == "1":
            test_local_sync()
        elif choice == "2":
            print("\n请确保Mock服务器已启动 (运行 ./mock_server/start_mock.sh)")
            input("按Enter继续...")
            test_wealth_app_sync()
        elif choice == "3":
            print("\n请确保Mock服务器已启动 (运行 ./mock_server/start_mock.sh)")
            input("按Enter继续...")
            test_all_sync()
        elif choice == "0":
            print("退出测试")
            return
        else:
            print("无效选择")
            
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        raise
    
    print("\n测试完成！查看日志文件了解详细信息。")


if __name__ == "__main__":
    main()