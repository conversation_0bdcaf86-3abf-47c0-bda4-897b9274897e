"""
财富APP接口Mock服务器
用于本地开发和测试
"""
import base64
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from loguru import logger

from src.financial_advisor.api.encrypt import decrypt_aes, encrypt_aes


app = FastAPI(title="财富APP Mock Server")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mock数据配置
SECRET_KEY = "1234567890123456"  # 16位密钥，用于测试
MOCK_FILES_DIR = os.path.join(os.path.dirname(__file__), "..", "sample_files")


class EncryptedRequest(BaseModel):
    data: str


class MockFileInfo:
    """模拟文件信息"""
    
    @staticmethod
    def generate_disclosure_files() -> List[Dict]:
        """生成信息披露文件列表"""
        return [
            {
                "fileId": "DISC001",
                "fileName": "2024年11月净值周报",
                "discloseType": "01",
                "discloseTypeName": "净值公告",
                "projectName": "稳健1号集合资金信托计划",
                "updateDate": (datetime.now() - timedelta(days=2)).strftime("%Y-%m-%d %H:%M:%S")
            },
            {
                "fileId": "DISC002",
                "fileName": "2024年第三季度管理报告",
                "discloseType": "02",
                "discloseTypeName": "定期报告",
                "projectName": "成长2号集合资金信托计划",
                "updateDate": (datetime.now() - timedelta(days=5)).strftime("%Y-%m-%d %H:%M:%S")
            },
            {
                "fileId": "DISC003",
                "fileName": "2024年11月资产配置月报",
                "discloseType": "03",
                "discloseTypeName": "投资报告",
                "projectName": "配置3号集合资金信托计划",
                "updateDate": (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d %H:%M:%S")
            }
        ]
    
    @staticmethod
    def generate_project_files() -> List[Dict]:
        """生成项目附件列表"""
        return [
            {
                "fileId": "PROJ001",
                "fileName": "产品介绍-股票基金推介PPT",
                "type": "01",
                "typeName": "推介材料",
                "projectName": "股票优选1号",
                "createDate": (datetime.now() - timedelta(days=10)).strftime("%Y-%m-%d %H:%M:%S"),
                "creator": "001",
                "creatorName": "张经理",
                "memo": "产品推介使用"
            },
            {
                "fileId": "PROJ002",
                "fileName": "基金培训QA答疑手册",
                "type": "02",
                "typeName": "培训材料",
                "projectName": "混合配置2号",
                "createDate": (datetime.now() - timedelta(days=15)).strftime("%Y-%m-%d %H:%M:%S"),
                "creator": "002",
                "creatorName": "李经理",
                "memo": "内部培训使用"
            },
            {
                "fileId": "PROJ003",
                "fileName": "债券策略研究报告",
                "type": "03",
                "typeName": "研究报告",
                "projectName": "债券增强3号",
                "createDate": (datetime.now() - timedelta(days=20)).strftime("%Y-%m-%d %H:%M:%S"),
                "creator": "003",
                "creatorName": "王分析师",
                "memo": "投资策略参考"
            },
            {
                "fileId": "PROJ004",
                "fileName": "合格投资者认定规定",
                "type": "04",
                "typeName": "合规文件",
                "projectName": "通用",
                "createDate": (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d %H:%M:%S"),
                "creator": "004",
                "creatorName": "合规部",
                "memo": "合规要求"
            }
        ]


def decrypt_request(encrypted_data: str) -> Dict:
    """解密请求数据"""
    try:
        decrypted = decrypt_aes(encrypted_data, SECRET_KEY)
        return json.loads(decrypted)
    except Exception as e:
        logger.error(f"解密失败: {str(e)}")
        raise HTTPException(status_code=400, detail="请求数据解密失败")


def encrypt_response(data: Dict) -> str:
    """加密响应数据"""
    json_str = json.dumps(data, ensure_ascii=False)
    return encrypt_aes(json_str, SECRET_KEY)


def read_mock_file(filename: str) -> bytes:
    """读取模拟文件内容"""
    # 遍历所有子目录查找文件
    for root, dirs, files in os.walk(MOCK_FILES_DIR):
        for file in files:
            if filename in file:
                file_path = os.path.join(root, file)
                with open(file_path, 'rb') as f:
                    return f.read()
    
    # 如果找不到文件，返回默认内容
    return f"Mock file content for: {filename}".encode('utf-8')


@app.get("/")
async def root():
    return {"message": "财富APP Mock Server", "version": "1.0.0"}


@app.post("/openapi/ai/disCloseFileQry.do")
async def query_disclosure_files(request: EncryptedRequest):
    """查询信息披露附件"""
    try:
        # 解密请求
        data = decrypt_request(request.data)
        logger.info(f"信息披露查询请求: {data}")
        
        # 获取分页参数
        page_size = int(data.get("pageSize", "20"))
        page_no = int(data.get("pageNo", "1"))
        
        # 生成模拟数据
        all_files = MockFileInfo.generate_disclosure_files()
        
        # 分页处理
        start_idx = (page_no - 1) * page_size
        end_idx = start_idx + page_size
        page_files = all_files[start_idx:end_idx]
        
        # 构建响应
        response_data = {
            "totalCount": str(len(all_files)),
            "list": page_files
        }
        
        # 加密响应
        encrypted_response = encrypt_response(response_data)
        
        return {
            "code": "0",
            "msg": "成功",
            "data": encrypted_response
        }
        
    except Exception as e:
        logger.error(f"处理请求失败: {str(e)}")
        return {
            "code": "1",
            "msg": str(e),
            "data": None
        }


@app.post("/openapi/ai/projFileQry.do")
async def query_project_files(request: EncryptedRequest):
    """查询项目附件"""
    try:
        # 解密请求
        data = decrypt_request(request.data)
        logger.info(f"项目附件查询请求: {data}")
        
        # 获取分页参数
        page_size = int(data.get("pageSize", "20"))
        page_no = int(data.get("pageNo", "1"))
        
        # 生成模拟数据
        all_files = MockFileInfo.generate_project_files()
        
        # 分页处理
        start_idx = (page_no - 1) * page_size
        end_idx = start_idx + page_size
        page_files = all_files[start_idx:end_idx]
        
        # 构建响应
        response_data = {
            "totalCount": str(len(all_files)),
            "list": page_files
        }
        
        # 加密响应
        encrypted_response = encrypt_response(response_data)
        
        return {
            "code": "0",
            "msg": "成功",
            "data": encrypted_response
        }
        
    except Exception as e:
        logger.error(f"处理请求失败: {str(e)}")
        return {
            "code": "1",
            "msg": str(e),
            "data": None
        }


@app.post("/openapi/ai/downloadCrmFile.do")
async def download_file(request: EncryptedRequest):
    """下载附件"""
    try:
        # 解密请求
        data = decrypt_request(request.data)
        logger.info(f"文件下载请求: {data}")
        
        file_id = data.get("fileId")
        file_type = data.get("fileType")
        
        # 根据文件ID获取对应的文件名
        filename_map = {
            "DISC001": "2024年11月净值周报.txt",
            "DISC002": "2024年第三季度管理报告.txt",
            "DISC003": "2024年11月资产配置月报.txt",
            "PROJ001": "产品介绍-股票基金推介PPT.txt",
            "PROJ002": "基金培训QA答疑手册.txt",
            "PROJ003": "债券策略研究报告.txt",
            "PROJ004": "合格投资者认定规定.txt"
        }
        
        filename = filename_map.get(file_id, "unknown.txt")
        
        # 读取文件内容
        file_content = read_mock_file(filename)
        
        # 构建响应
        response_data = {
            "base64": base64.b64encode(file_content).decode('utf-8'),
            "fileSuffix": "txt"  # 简化处理，都返回txt
        }
        
        # 加密响应
        encrypted_response = encrypt_response(response_data)
        
        return {
            "code": "0",
            "msg": "成功",
            "data": encrypted_response
        }
        
    except Exception as e:
        logger.error(f"处理请求失败: {str(e)}")
        return {
            "code": "1",
            "msg": str(e),
            "data": None
        }


if __name__ == "__main__":
    import uvicorn
    import sys
    
    # 允许通过命令行参数指定端口
    port = int(sys.argv[1]) if len(sys.argv) > 1 else 8888
    
    logger.info(f"Mock服务器启动，密钥: {SECRET_KEY}")
    logger.info(f"文件目录: {MOCK_FILES_DIR}")
    logger.info(f"监听端口: {port}")
    
    uvicorn.run(app, host="0.0.0.0", port=port)