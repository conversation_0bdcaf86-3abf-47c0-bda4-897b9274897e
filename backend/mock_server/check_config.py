"""检查财富APP配置"""
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.config.load_config import load_config


def check_wealth_app_config():
    """检查财富APP配置"""
    print("检查财富APP配置...")
    print("-" * 50)
    
    # 加载配置
    config = load_config([
        "WEALTH_APP_BASE_URL",
        "WEALTH_APP_SECRET_KEY"
    ])
    
    base_url = config.get("WEALTH_APP_BASE_URL")
    secret_key = config.get("WEALTH_APP_SECRET_KEY")
    
    print(f"WEALTH_APP_BASE_URL: {base_url or '未配置'}")
    print(f"WEALTH_APP_SECRET_KEY: {'已配置' if secret_key else '未配置'}")
    
    if not base_url:
        print("\n错误: 未配置 WEALTH_APP_BASE_URL")
        print("请在 .env 文件中设置:")
        print("WEALTH_APP_BASE_URL=http://localhost:8888  # 使用Mock服务器")
        print("或")
        print("WEALTH_APP_BASE_URL=http://***********:8888  # 使用TEST环境")
        
    if not secret_key:
        print("\n错误: 未配置 WEALTH_APP_SECRET_KEY")
        print("请在 .env 文件中设置:")
        print("WEALTH_APP_SECRET_KEY=1234567890123456  # Mock服务器密钥")
        
    if base_url and secret_key:
        print("\n配置正确！")
        
        if "localhost" in base_url:
            print("\n注意: 当前配置指向本地Mock服务器")
            print("请确保Mock服务器已启动: ./mock_server/start_mock.sh")
        else:
            print("\n注意: 当前配置指向真实服务器")
            print("请确保密钥正确且服务器可访问")
    
    print("\n" + "-" * 50)


if __name__ == "__main__":
    check_wealth_app_config()