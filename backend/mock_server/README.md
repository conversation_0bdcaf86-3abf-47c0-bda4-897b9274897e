# Mock Server 测试环境

这个目录包含了用于测试 Dify 同步功能的 Mock 服务器和示例文件。

## 目录结构

```
mock_server/
├── sample_files/        # 示例文件目录
│   ├── 产品培训/       # 产品培训相关文档
│   ├── 投后报告/       # 投后管理报告
│   ├── 资产配置/       # 资产配置策略文档
│   ├── 合规运营/       # 合规操作文档
│   └── 服务信托/       # 服务信托业务文档
├── wealth_app_mock/     # 财富APP Mock服务器
├── start_mock.sh        # Mock服务器启动脚本
├── test_sync.py         # 同步功能测试脚本
└── README.md           # 本文档
```

## 使用说明

### 1. 启动 Mock 服务器

```bash
# 在 backend 目录下执行
./mock_server/start_mock.sh
```

Mock 服务器将在 `http://localhost:8888` 启动，模拟财富APP的接口。

### 2. 测试同步功能

```bash
# 在 backend 目录下执行
python mock_server/test_sync.py
```

测试脚本提供三种测试模式：
- 测试本地文件夹同步
- 测试财富APP同步（需要先启动Mock服务器）
- 测试全部同步

### 3. Mock 服务器接口

Mock 服务器实现了以下接口：

- `POST /openapi/ai/disCloseFileQry.do` - 查询信息披露附件
- `POST /openapi/ai/projFileQry.do` - 查询项目附件  
- `POST /openapi/ai/downloadCrmFile.do` - 下载附件

所有接口都使用 AES 加密通信，测试密钥为：`1234567890123456`

### 4. 示例文件说明

`sample_files` 目录包含了10个示例文件，分布在5个不同的类别目录中：

- **产品培训**: 推介PPT、QA答疑手册
- **投后报告**: 净值周报、季度管理报告
- **资产配置**: 债券策略研究、资产配置月报
- **合规运营**: 合格投资者认定、合规营销管理
- **服务信托**: 家族信托手册、保险金信托KYC表

这些文件会被自动分类到对应的 Dify 知识库中。

## 注意事项

1. Mock 服务器仅用于开发和测试，不要在生产环境使用
2. 测试时使用 `--dry-run` 参数可以模拟运行，不会实际上传文件到 Dify
3. 确保已安装所有依赖（FastAPI、uvicorn 等）
4. Mock 服务器的加密密钥是固定的，仅用于测试

## 测试流程

1. 启动 Mock 服务器
2. 运行测试脚本，选择测试模式
3. 查看控制台输出和日志文件
4. 验证文件分类是否正确
5. 检查同步统计信息