# Gemini Fullstack LangGraph Quickstart

This project provides a fullstack application using a React frontend and a LangGraph-powered backend agent. The agent is designed to be able to perform external research as well as use tools that can search internal data for financial reporting.

## Features (needs updating)

- 💬 Fullstack application with a React frontend and LangGraph backend.
- 🧠 Powered by a LangGraph agent for advanced research and conversational AI.
- 🔍 Dynamic search query generation using Google Gemini models.
- 🌐 Integrated web research via Google Search API.
- 🤔 Reflective reasoning to identify knowledge gaps and refine searches.
- 📄 Generates answers with citations from gathered sources.
- 🔄 Hot-reloading for both frontend and backend development during development.

## Project Structure

The project is divided into two main directories:

- `frontend/`: Contains the React application built with Vite.
- `backend/`: Contains the LangGraph/FastAPI application, including the research agent logic.

## Getting Started: Development and Local Testing

Follow these steps to get the application running locally for development and testing.

**1. Prerequisites:**

- Node.js and npm (or yarn/pnpm)
- Python 3.8+
- **`GEMINI_API_KEY`**: The backend agent requires a Google Gemini API key. You should be able to find this in GCP instance.
  1.  Navigate to the `backend/` directory.
  2.  Create a file named `.env` by copying the `backend/.env.example` file.
  3.  Open the `.env` file and add your Gemini API key: `GEMINI_API_KEY="YOUR_ACTUAL_API_KEY"`

**2. Install Dependencies:**
(needs updating to virtual env)
I am assuming you have uv set up.

**Backend:**

```bash
cd backend
uv venv
source .venv/bin/activate
uv sync
```

**Frontend:**

```bash
cd frontend
npm install
```

**3. Run Development Servers:**

**Backend & Frontend:**

I prefer to run the backend and frontend separately.

For the backend, open a terminal in the `backend/` directory and run (make sure you are in the venv you started above):

```bash
langgraph dev
```

> **Tip for macOS users:**  
> If you encounter DNS resolution issues when running the backend, try executing:
>
> ```bash
> source ../dns_resolution.sh
> ```
>
> in your terminal before starting the backend server.

The backend API will be available at `http://127.0.0.1:2024`.

For the frontend, open a terminal in the `frontend/` directory and run

```bash
npm run dev
```

The frontend will be available at `http://localhost:5173`

Alternatively, you can run:

```bash
make dev
```

This will run the backend and frontend development servers. Open your browser and navigate to the frontend development server URL (e.g., `http://localhost:5173/app`).

## How the Backend Agent Works (High-Level)

The core of the backend is a LangGraph agent defined in `backend/src/research_agent/graph.py`. It follows these steps:

![Agent Flow](./agent.png)

1.  **Generate Initial Queries:** Based on your input, it generates a set of initial search queries using a Gemini model.
2.  **Web Research:** For each query, it uses the Gemini model with the Google Search API to find relevant web pages.
3.  **Reflection & Knowledge Gap Analysis:** The agent analyzes the search results to determine if the information is sufficient or if there are knowledge gaps. It uses a Gemini model for this reflection process.
4.  **Iterative Refinement:** If gaps are found or the information is insufficient, it generates follow-up queries and repeats the web research and reflection steps (up to a configured maximum number of loops).
5.  **Finalize Answer:** Once the research is deemed sufficient, the agent synthesizes the gathered information into a coherent answer, including citations from the web sources, using a Gemini model.

## Deployment

In production, the backend server serves the optimized static frontend build. LangGraph requires a Redis instance and a Postgres database. Redis is used as a pub-sub broker to enable streaming real time output from background runs. Postgres is used to store assistants, threads, runs, persist thread state and long term memory, and to manage the state of the background task queue with 'exactly once' semantics. For more details on how to deploy the backend server, take a look at the [LangGraph Documentation](https://langchain-ai.github.io/langgraph/concepts/deployment_options/). Below is an example of how to build a Docker image that includes the optimized frontend build and the backend server and run it via `docker-compose`.

_Note: For the docker-compose.yml example you need a LangSmith API key, you can get one from [LangSmith](https://smith.langchain.com/settings)._

_Note: If you are not running the docker-compose.yml example or exposing the backend server to the public internet, you update the `apiUrl` in the `frontend/src/App.tsx` file your host. Currently the `apiUrl` is set to `http://localhost:8123` for docker-compose or `http://localhost:2024` for development._

**1. Build the Docker Image:**

Run the following command from the **project root directory**:

```bash
docker build -t gemini-fullstack-langgraph -f Dockerfile .
```

**2. Run the Production Server:**

```bash
GEMINI_API_KEY=<your_gemini_api_key> LANGSMITH_API_KEY=<your_langsmith_api_key> docker-compose up
```

Open your browser and navigate to `http://localhost:8123/app/` to see the application. The API will be available at `http://localhost:8123`.

## Technologies Used

- [React](https://reactjs.org/) (with [Vite](https://vitejs.dev/)) - For the frontend user interface.
- [Tailwind CSS](https://tailwindcss.com/) - For styling.
- [Shadcn UI](https://ui.shadcn.com/) - For components.
- [LangGraph](https://github.com/langchain-ai/langgraph) - For building the backend research agent.
- [Google Gemini](https://ai.google.dev/models/gemini) - LLM for query generation, reflection, and answer synthesis.

## License

This project is licensed under the Apache License 2.0. See the [LICENSE](LICENSE) file for details.

```sql
-- thread_run_steps definition

CREATE TABLE `thread_run_steps` (
  `user_id` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `thread_id` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `run_id` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `steps` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  PRIMARY KEY (`run_id`),
  KEY `thread_run_steps_idx` (`user_id`,`thread_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
```

card_id 映射 name -> card_id -> 卡片
思考过程接口 GET\POST
猜你想问
思考过程优化
AI 一键配置、相似产品
token 接口
模型测试 completion 接口
redis uri
tool 获取 state

预先问题接口
第二屏轮询
工具返回内容优化
java 接口
权限控制

python 依赖
docker 容器

8.23-24 周末不来
9.2 17 点前
9.3 不来

- 点击
    客户基本信息 - /userInfo?id=xxxxxx
    客户持仓信息 - /positionInfo?id=xxxx
    配置分析 - /configAsset?

- 轮询
    标准组合详情 - /standardCombinationDetail?unid=xxxx
      generateCacheLink 报错
    AI 解释文本 - /statusInfo?status=xx

export https_proxy=************:8080 && pip install pycryptodome -i https://mirrors.huaweicloud.com/repository/pypi/simple/ && unset https_proxy


剩余接口
会话用户ID
新增字段接口
crm文件
app接口
bug
