# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a fullstack financial advisor application using LangGraph for the backend and React/Next.js for the frontend. The application is designed to provide AI-powered financial advice with features for customer information retrieval, portfolio analysis, and product recommendations.

## Development Commands

### Backend (Python/LangGraph)

```bash
# Set up virtual environment using uv
cd backend
uv venv
source .venv/bin/activate
uv sync  # This will use pyproject.toml for dependencies

# Run development server
langgraph dev

# Run tests
make test
make test_database
make test_database_integration

# Linting and formatting
make lint
make format

# Install development dependencies
uv pip install -e ".[dev]"

# macOS DNS fix (if needed)
source ../dns_resolution.sh
```

### Frontend (React/Vite)

```bash
cd frontend
npm install
npm run dev

# Build
npm run build

# Linting
npm run lint
```

### Web Frontend (Next.js)

```bash
cd web
pnpm install
pnpm dev

# Build
pnpm build

# Linting and formatting
pnpm lint
pnpm format
```

### Run Both Frontend and Backend

```bash
make dev
```

## Architecture

### Backend Structure

The backend uses LangGraph to orchestrate a multi-agent financial advisor system:

- **Main Graph** (`backend/src/financial_advisor/graphs/graph.py`): Central orchestration that routes requests to specialized sub-graphs
- **Sub-graphs**:
  - `customer_graph`: Handles customer information queries
  - `product_graph`: Product search and comparison
  - `portfolio_graph`: Portfolio analysis and recommendations
  - `planning_graph`: Financial planning assistance
  - `search_graph`: General search functionality

- **State Management**: Uses a shared `State` object across graphs with conversation history and step tracking
- **LLM Configuration**: Supports multiple LLM models configured via `Configuration` class
- **Authentication**: Custom auth handler at `backend/src/financial_advisor/auth.py`

### Frontend Architecture

Two frontend applications:
1. **Vite React App** (`frontend/`): Primary UI for financial advisor interface
2. **Next.js App** (`web/`): Alternative frontend with agent-chat-ui

Both use:
- LangGraph SDK for backend communication
- Tailwind CSS for styling
- Shadcn UI components

### Key Integration Points

- **API Communication**: Frontends connect to backend at `http://localhost:2024` (development) or `http://localhost:8123` (docker-compose)
- **Database**: MySQL for persistence (checkpointing) and Redis for pub-sub
- **Authentication**: JWT-based auth with custom handler
- **CORS**: Configured to allow all origins in development

## Important Notes

- Always activate the virtual environment before running backend commands
- The backend requires `GEMINI_API_KEY` in the `.env` file
- Use `uv` for Python package management (with `--python-preference only-system`)
- Follow Python coding guidelines in `CLAUDE_PROJECT/python_coding_guidelines.md`
- Use loguru for all logging in the backend
- Frontend components should use Tailwind utility classes for styling