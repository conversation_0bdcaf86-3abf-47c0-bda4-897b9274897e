services:
  langgraph-api:
    image: langgraph-api-linux
    container_name: langgraph-api-linux
    command: langgraph dev --host 0.0.0.0 --port 2024
    ports:
      - "2024:2024"
    volumes:
      - ./backend/:/deps/backend/
    env_file:
      - ./backend/.env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:2024/ok"]
      interval: 30s
      timeout: 10s
      retries: 3
    privileged: true
