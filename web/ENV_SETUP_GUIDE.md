# 环境变量配置指南

本项目使用环境变量来配置 API 端点，使得在不同环境之间切换更加灵活。

## 配置说明

### 必需的环境变量

- `NEXT_PUBLIC_API_URL`: LangGraph API 服务器的 URL
  - 用于所有 API 请求
  - 必须以 `NEXT_PUBLIC_` 开头才能在客户端代码中使用

### 配置步骤

1. **复制环境变量模板**
   ```bash
   cp .env.example .env.local
   ```

2. **编辑 `.env.local` 文件**
   ```env
   # 开发环境
   NEXT_PUBLIC_API_URL=http://localhost:2024
   
   # 或者测试环境
   NEXT_PUBLIC_API_URL=http://fm-ai-test.qutke.cn:2024
   
   # 或者生产环境
   NEXT_PUBLIC_API_URL=http://fm-ai.qutke.cn:2024
   ```

3. **重启开发服务器**
   ```bash
   pnpm dev
   ```

## 使用的文件

以下文件使用了 `NEXT_PUBLIC_API_URL` 环境变量：

1. **`src/providers/Thread.tsx`**
   - 用于创建 Thread 客户端连接

2. **`src/utils/http.ts`**
   - 用于所有 HTTP 请求的基础 URL

3. **`src/providers/Stream.tsx`**
   - 用于流式响应的默认 API URL

4. **`src/components/thread/agent-inbox/components/thread-actions-view.tsx`**
   - 用于在 Studio 中打开的功能

5. **`src/components/CardComponents/Markdown/index.tsx`**
   - 用于加载时的光标占位符

## 环境特定配置

### 开发环境 (localhost)
```env
NEXT_PUBLIC_API_URL=http://localhost:2024
```

### 测试环境
```env
NEXT_PUBLIC_API_URL=http://fm-ai-test.qutke.cn:2024
```

### 生产环境
```env
NEXT_PUBLIC_API_URL=http://fm-ai.qutke.cn:2024
```

## 注意事项

1. **环境变量前缀**：在 Next.js 中，只有以 `NEXT_PUBLIC_` 开头的环境变量才能在浏览器端代码中使用。

2. **默认值**：所有使用环境变量的地方都提供了默认值，以确保向后兼容性：
   ```javascript
   process.env.NEXT_PUBLIC_API_URL || 'http://fm-ai.qutke.cn:2024'
   ```

3. **构建时替换**：Next.js 在构建时会将环境变量的值内联到代码中，因此更改环境变量后需要重新构建。

4. **Docker 部署**：在 Docker 容器中，可以通过以下方式设置环境变量：
   ```dockerfile
   ENV NEXT_PUBLIC_API_URL=http://your-api-server:2024
   ```
   
   或在运行时：
   ```bash
   docker run -e NEXT_PUBLIC_API_URL=http://your-api-server:2024 your-image
   ```

## 验证配置

启动应用后，可以在浏览器控制台中运行以下命令来验证配置：

```javascript
console.log(process.env.NEXT_PUBLIC_API_URL)
```

这应该显示你配置的 API URL。