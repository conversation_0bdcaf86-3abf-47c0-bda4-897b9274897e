/** @type {import('next').NextConfig} */
import withBundleAnalyzer from "@next/bundle-analyzer";
const bundleAnalyzer = withBundleAnalyzer({
  enabled: process.env.ANALYZE === "true",
});

const nextConfig = {
  output: "export",
  // 指定输出目录为 dist（默认是 out）
  distDir: "dist",
  // 启用压缩（默认支持gzip和brotli）
  // compress: true,
  // 可选：自定义压缩阈值（默认1KB）
  // 小于该值的文件不会被压缩
  // compressThreshold: 8192, // 8KB
  images: {
    unoptimized: true,
  },
  trailingSlash: true,
  typescript: {
    ignoreBuildErrors: true, // 👈 关键项：忽略构建时 TypeScript 报错
  },
  eslint: {
    ignoreDuringBuilds: true, // 打包时忽略所有ESLint错误
  },
  experimental: {
    serverActions: {
      bodySizeLimit: "10mb",
    },
  },
  devIndicators: false, //是否开启调试器
  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });
    return config;
  },
};

export default bundleAnalyzer(nextConfig);
