{"name": "agent-chat-ui", "readme": "https://github.com/langchain-ai/agent-chat-ui/blob/main/README.md", "homepage": "https://agentchat.vercel.app", "repository": {"type": "git", "url": "git+https://github.com/langchain-ai/agent-chat-ui.git"}, "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@langchain/core": "^0.3.44", "@langchain/langgraph": "^0.2.63", "@langchain/langgraph-sdk": "^0.0.73", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@svgr/webpack": "^6.5.1", "antd": "^5.27.1", "antd-mobile": "^5.39.0", "class-variance-authority": "^0.7.1", "classnames": "^2.3.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "esbuild": "^0.25.0", "esbuild-plugin-tailwindcss": "^2.0.1", "framer-motion": "^12.23.12", "install": "^0.13.0", "katex": "^0.16.21", "langgraph-nextjs-api-passthrough": "^0.0.4", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lucide-react": "^0.476.0", "nuqs": "^2.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.0.1", "react-syntax-highlighter": "^15.5.0", "react-virtualized-auto-sizer": "^1.0.26", "rehype-highlight": "^6.0.0", "rehype-katex": "^7.0.1", "remark-breaks": "^3.0.2", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sass": "^1.59.2", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@next/bundle-analyzer": "^15.5.2", "@tailwindcss/postcss": "^4.0.13", "@types/lodash": "^4.17.16", "@types/node": "^22.13.5", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "dotenv": "^16.4.7", "globals": "^15.14.0", "next": "^15.2.3", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwind-scrollbar": "^4.0.1", "tailwindcss": "^4.0.13", "typescript": "~5.7.2", "weixin-js-sdk": "^1.6.5"}, "overrides": {"react-is": "^19.0.0-rc-69d4b800-20241021"}, "packageManager": "pnpm@10.5.1"}