/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./index.html",
    "./src/**/*.{ts,tsx,js,jsx}",
    "./agent/**/*.{ts,tsx,js,jsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        // youshe: ["YouSheBiaoTiHei", "sans-serif"],
        custom: ["CustomFont", "sans-serif"], // 对应 @font-face 中的 font-family
      },
      fontSize: {
        // 覆盖 Tailwind 默认的 rem 计算逻辑，这里以 1rem=10px 为例
        // 语法：'自定义名称': ['字体大小(rem)', '行高']
        12: ["1.2rem", "1.5"], // 对应 14px（1.4rem * 10px/rem
        14: ["1.4rem", "1.5"], // 对应 14px（1.4rem * 10px/rem）
        16: ["1.6rem", "1.5"], // 对应 16px
        18: ["1.8rem", "1.5"], // 对应 18px
        20: ["2.0rem", "1.5"], // 对应 20px
        22: ["2.2rem", "1.5"], // 对应 22px
        // 按你的项目常用 px 尺寸补充，比如 12、20 等
      },
      // 2. 扩展内边距（p-*）的 rem 配置（对应原 px 需求）
      padding: {
        1: "0.1rem", // 原 p-1 对应 1px → 0.1rem（10px/rem 基准）
        2: "0.2rem", // 原 p-2 对应 2px → 0.2rem
        3: "0.3rem", // 按项目常用 padding 补充
        4: "0.5rem", // 按项目常用 padding 补充
        6: "0.75rem", // 按项目常用 padding 补充
        8: "1rem", // 按项目常用 padding 补充
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      components: {
        ".scrollbar-pretty":
          "overflow-y-scroll [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-track]:bg-transparent",
      },
      colors: {
        primary: {
          DEFAULT: "var(--adm-color-primary)",
          foreground: "var(--adm-color-white)",
        },
        secondary: {
          DEFAULT: "var(--adm-color-success)",
          foreground: "var(--adm-color-white)",
        },
        muted: {
          DEFAULT: "var(--adm-color-light)",
          foreground: "var(--adm-color-text-secondary)",
        },
        accent: {
          DEFAULT: "var(--adm-color-warning)",
          foreground: "var(--adm-color-white)",
        },
        destructive: {
          DEFAULT: "var(--adm-color-danger)",
          foreground: "var(--adm-color-white)",
        },
        border: "var(--adm-color-border)",
        input: "var(--adm-color-box)",
        background: "var(--adm-color-background)",
        foreground: "var(--adm-color-text)",
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("tailwind-scrollbar")],
};
