import React, { useState, useEffect } from "react";
import { login } from "@/services/api";
import Loading from "@/app/icons/three-dots.svg";

/**
 * 高阶组件：确保获取token后再渲染页面
 * @param {React.Component} WrappedComponent 需要被包裹的组件
 * @returns {React.Component} 处理登录逻辑后的组件
 */
const withAuth = (WrappedComponent) => {
  const AuthComponent = (props) => {
    const [token, setToken] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [parentInfo, setParentInfo] = useState(null);
    // 检查本地存储中是否已有token
    const getStoredToken = () => {
      return localStorage.getItem("authToken");
    };

    // 保存token到本地存储
    const saveToken = (newToken) => {
      localStorage.setItem("authToken", newToken);
      setToken(newToken);
    };

    // 获取token的逻辑
    const fetchToken = async () => {
      // 如果已有token，无需重复获取
      if (token) return;
      try {
        setLoading(true);
        // 先检查本地是否有token
        const storedToken = getStoredToken();

        if (storedToken) {
          setToken(storedToken);
          setLoading(false);
          return;
        }

        // 确保parentInfo存在再调用接口
        if (!parentInfo) {
          console.log("parentInfo尚未准备好");
          setLoading(false);
          return;
        }

        // 本地没有则调用登录接口获取
        console.log(4444, parentInfo);
        const newToken = await login();
        console.log("newToken====>", newToken);
        saveToken(newToken?.data?.data);
        setError(null);
      } catch (err) {
        setError("获取登录状态失败，请重试");
        console.error("获取token失败:", err);
      } finally {
        setLoading(false);
      }
    };

    // 当parentInfo变化时重新执行fetchToken
    useEffect(() => {
      console.log(777, "parentInfo变化，尝试获取token");
      fetchToken();
    }, [parentInfo]); // 依赖parentInfo，确保它就绪后执行

    // 加载中状态
    if (loading) {
      return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/80">
          <Loading className="h-10 w-10 animate-spin text-gray-600" />
        </div>
      );
    }

    // 错误状态
    if (error) {
      return (
        <div className="error">
          <p>{error}</p>
          <button onClick={fetchToken}>重试</button>
        </div>
      );
    }

    // 有token时渲染被包裹的组件，并传递token和其他props
    return (
      <WrappedComponent
        {...props}
        token={token}
      />
    );
  };

  return AuthComponent;
};

export default withAuth;
