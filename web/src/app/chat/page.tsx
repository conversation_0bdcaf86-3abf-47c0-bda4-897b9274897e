"use client";

import { Thread } from "@/components/thread"
import { StreamProvider } from "@/providers/Stream";
import { ThreadProvider } from "@/providers/Thread";
import { ArtifactProvider } from "@/components/thread/artifact";
import React from "react";
import { HistoricalActivitiesProvider } from "@/contexts/ThreadActivitiesContext";
import Loading from "@/app/icons/three-dots.svg";
import withAuth from '../withAuth'; // 导入高阶组件
const Page = () => {
  return (
    <React.Suspense fallback={<div
      className="flex h-full w-full items-center justify-center">
      <img width={100} height={30} src={Loading.src} />
    </div>}>
      <ThreadProvider>
        <HistoricalActivitiesProvider>
          <StreamProvider>
            <ArtifactProvider>
              <Thread />
            </ArtifactProvider>
          </StreamProvider>
        </HistoricalActivitiesProvider>
      </ThreadProvider>
    </React.Suspense>
  );
}
export default withAuth(Page)
