"use client";

import withAuth from "../withAuth"
import { StreamProvider } from "@/providers/Stream";
import { ThreadProvider } from "@/providers/Thread";
import { ArtifactProvider } from "@/components/thread/artifact";
import React from "react";
import ThreadHistory from "@/components/thread/history";

function Page(): React.ReactNode {
  return (
    <React.Suspense fallback={<div>Loading (layout)...</div>}>
      <ThreadProvider>
        <StreamProvider>
          <ArtifactProvider>
            <ThreadHistory />
          </ArtifactProvider>
        </StreamProvider>
      </ThreadProvider>
    </React.Suspense>
  );
}

export default withAuth(Page)