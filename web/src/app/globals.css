
@import "tailwindcss";

/* 1. 自定义字体优先定义（确保后续可调用） */
@font-face {
  font-family: 'YouSheBiaoTiHei'; /* 自定义标题黑体 */
  src: url('/fonts/YouSheBiaoTiHei.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap; /* 优化加载体验：先显示 fallback 字体，加载完成后替换 */
}

@plugin "tailwindcss-animate";


@layer base {
  :root {
  /* 强制覆盖 antd-mobile 默认主题 */
    --adm-color-primary: #bf9267FF !important;
    --adm-color-success: #00b578 !important;
    --adm-color-warning: #ff8f1f !important;
    --adm-color-danger: #ff3141 !important;
    --adm-color-white: #ffffff !important;
    --adm-color-text: #333333 !important;
    --adm-color-border: #eeeeee !important;
    --adm-color-background: #ffffff !important;
  }
  body {
    font-family: 
      /* 优先级1：项目自定义字体（标题黑体，适合标题/强调文本） */
     'Helvetica Neue','PingFang SC', 
      /* 优先级2：macOS/iOS 系统优先中文字体（保证苹果设备显示效果） */
      'SF Pro Text', 'SF Pro Icons',
      /* 优先级3：Windows 系统优先中文字体（保证Windows设备显示效果） */
      'Microsoft YaHei','YouSheBiaoTiHei',
      /* 优先级4：跨平台通用中文字体（兼容Linux/Android等） */
      'WenQuanYi Zen Hei', 'Noto Sans SC',
      /* 优先级5：系统无衬线字体 fallback（避免乱码） */
      -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
      /* 最终 fallback：通用无衬线字体族（所有系统都支持） */
      'sans-serif';
  }

  /* 以下为原有样式，保持不变 */
  .ant-input-outlined{
    border-color: #BF9267FF !important;
    box-shadow: inset 0 0 0 1px #BF9267FF !important;
  }
  .ant-btn-color-primary{
    background-color: #BF9267FF !important;
  }
  .ant-tabs-nav{
    margin-bottom: 4px !important;
  }
  .ant-input-affix-wrapper{
    border-radius: 20px!important;
  }
  .ant-tabs-tab-btn:hover{
    color: #BF9267FF!important;
  }
  .ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn{
    color: #BF9267FF!important;
  }
  .ant-tabs-ink-bar {
    color: #BF9267FF!important;
    background: #BF9267FF!important;
  }

  /* Hover 状态：未选中时 */
  .ant-checkbox:hover .ant-checkbox-inner {
    border-color: #bf9267 !important;
    background-color: transparent;
  }
  .ant-checkbox-wrapper:hover .ant-checkbox-inner{
    border-color: #bf9267 !important;
    background-color: transparent;
  }

  /* 选中状态 */
  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #bf9267 !important;
    border-color: #bf9267 !important;
  }

  /* 选中状态下的 Hover 效果 */
  .ant-checkbox-checked:hover .ant-checkbox-inner {
    background-color: #bf9267 !important;
    border-color: #bf9267 !important;
  }

  /* 禁用状态（可选） */
  .ant-checkbox-disabled .ant-checkbox-inner {
    background-color: #f5f5f5 !important;
    border-color: #d9d9d9 !important;
  }

  .ant-checkbox-checked.ant-checkbox-disabled .ant-checkbox-inner {
    background-color: #f5f5f5 !important;
    border-color: #d9d9d9 !important;
  }

  .ant-list-sm .ant-list-item{
    padding: 4px 10px !important;
  }
  /* 修改 Spin 加载动画的颜色 */
  .ant-spin .ant-spin-dot-item {
    background-color: white !important;
  }

  /* 如果需要修改文字颜色 */
  .ant-spin-text {
    color: white !important;
  }
  /*分段器组件*/
  .ant-segmented-group{
    padding: 2px !important;
  }


}


/* 滚动条样式（保持不变） */
::-webkit-scrollbar {
  --bar-width: 5px;
  width: var(--bar-width);
  height: var(--bar-width);
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--bar-color);
  border-radius: 20px;
  background-clip: content-box;
  border: 1px solid transparent;
}

/* 基础字体大小设置（保持不变） */
html {
  font-size: 16px; /* 1rem = 16px（行业通用基准） */
}

/* 响应式调整根字体大小 */
@media (max-width: 1200px) {
  html {
    font-size: 16px;
  }
}

@media (max-width: 992px) {
  html {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  html {
    font-size: 15px;
  }
}

@media (max-width: 576px) {
  html {
    font-size: 15px;
  }
}