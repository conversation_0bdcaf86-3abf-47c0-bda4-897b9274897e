type EnCnKey =
  | "customer_info"
  | "general_discussion"
  | "product_info"
  | "portfolio_info"
  | "dataset"
  | "financial_planning"
  | "customer_basic_info"
  | "customer_hold_info"
  | "customer_trade_info"
  | "product_basic_info"
  | "compare_products"
  | "similar_products"
  | "similar_products_for_customer"
  | "planning_for_new_customer"
  | "planning_for_existing_customer"
  | "planning_for_existing_customer_with_position"
  | "fetch_customer_basic_info"
  | "fetch_customer_hold_info"
  | "fetch_customer_trade_info"
  | "fetch_product_basic_info"
  | "fetch_product_comparison"
  | "fetch_product_similarity";

// 2. 映射表（使用严格类型，避免拼写错误）
const enCnMap: Record<EnCnKey, string> = {
  "customer_info": "查询客户相关信息",
  "general_discussion": "日常对话",
  "product_info": "查询信托产品相关信息",
  "portfolio_info": "查询标准组合相关信息",
  "dataset": "查询知识库相关信息",
  "financial_planning": "财富规划",
  "customer_basic_info": "查询客户基本信息",
  "customer_hold_info": "查询客户持仓信息",
  "customer_trade_info": "查询客户交易信息",
  "product_basic_info": "查询信托产品基本信息",
  "compare_products": "比较不同信托产品的表现",
  "similar_products": "推荐与某个信托产品相似的其他产品",
  "similar_products_for_customer": "推荐与某个客户持有的信托产品相似的其他产品",
  "planning_for_new_customer": "针对新客户的资产配置财富规划",
  "planning_for_existing_customer": "针对现有客户的资产配置财富规划",
  "planning_for_existing_customer_with_position": "针对现有客户的资产配置财富规划（带仓位）",
  "fetch_customer_basic_info": "查询客户基本信息",
  "fetch_customer_hold_info": "查询客户持仓信息",
  "fetch_customer_trade_info": "查询客户交易信息",
  "fetch_product_basic_info": "查询信托产品基本信息",
  "fetch_product_comparison": "查询信托产品比较信息",
  "fetch_product_similarity": "查询信托产品相似信息"
};

// 3. 处理函数（优化正则和匹配逻辑）
export function addEnCnToStr(rawText: string): string {
  // 正则优化：精确匹配映射表中的英文（仅包含字母、数字、下划线，且是完整单词）
  // 从映射表中动态生成匹配规则，避免硬编码
  const enKeys = Object.keys(enCnMap).join("|");
  const enReg = new RegExp(`\\b(${enKeys})\\b`, "g");

  return rawText.replace(enReg, (match) => {
    // 直接匹配（因为映射表键是小写，文本中的英文无论大小写都能被正则捕获）
    const cnMeaning = enCnMap[match as EnCnKey];
    return cnMeaning ? `${cnMeaning}` : match;
  });
}
