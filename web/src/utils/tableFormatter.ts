/**
 * 获取涨跌字体颜色
 * @param rise
 * @returns
 */
export const getTableRiseColor = (rise: boolean = true) => {
  return rise ? ' #ea4336' : '#34a852'
}
/**
 * 空处理
 * @param value
 * @returns
 */
export const emptyFormatter = (value: string | number | undefined | null) => {
  if (value === undefined || value === null) {
    return '--'
  }
  if (typeof value === 'string') {
    return value.trim() === '' ? '--' : value
  }
  return isNaN(value) ? '--' : value
}

export const percentFormatter = (
  value: number | undefined | null,
  isAdd: boolean = false,
) => {
  if (value === undefined || value === null) {
    return '--'
  }
  if (value > 0) {
    if (isAdd) {
      return `+${value}%`
    }
    return `${value}%`
  }
  return `${value}%`
}

export const percentFixedFormatter = (
  value: number | undefined | null,
  fixed: number = 2,
  isAdd: boolean = false,
) => {
  if (value === undefined || value === null) {
    return '--'
  }
  if (`${value}`.includes('e-')) {
    return percentFormatter(0, isAdd)
  }
  let fixedNum = fixed
  let fixedValue = value.toFixed(fixedNum)
  do {
    try {
      const num = Number(fixedValue)
      if (num === 0 && value !== 0) {
        fixedNum += 1
        fixedValue = value.toFixed(fixedNum)
        continue
      }
      return percentFormatter(num, isAdd)
    } catch (error) {
      return fixedValue
    }
  } while (value !== 0)
  return fixedValue
}

export const percentMultiplyFixedFormatter = (
  value: number | undefined | null,
  fixed: number = 2,
  isAdd: boolean = false,
) => {
  if (value === undefined || value === null) {
    return '--'
  }
  if (`${value}`.includes('e-')) {
    return percentFormatter(0, isAdd)
  }
  let fixedNum = fixed
  let fixedValue = (value * 100).toFixed(fixedNum)
  do {
    try {
      const num = Number(fixedValue)
      if (num === 0 && value !== 0) {
        fixedNum += 1
        fixedValue = value.toFixed(fixedNum)
        continue
      }
      return percentFormatter(num, isAdd)
    } catch (error) {
      return fixedValue
    }
  } while (value !== 0)
  return fixedValue
}

export const enum TableFormatterEnum {
  empty = 'empty',
  percent = 'percent',
  percentFixed = 'percentFixed',
}

export const tableFormatter = (
  value?: string | number | undefined | null,
  type: TableFormatterEnum = TableFormatterEnum.empty,
  fixed?: number,
  isAdd?: boolean,
) => {
  if (value === undefined || value === null) {
    return '--'
  }
  if (typeof value === 'number') {
    if (type === TableFormatterEnum.percent) {
      return percentFormatter(value, isAdd)
    }
    if (type === TableFormatterEnum.percentFixed) {
      return percentFixedFormatter(value, fixed, isAdd)
    }
  }
  return emptyFormatter(value)
}
