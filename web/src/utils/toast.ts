import { cn } from "@/lib/utils";

// 定义提醒类型
type ToastType = 'success' | 'error' | 'info' | 'warning';

// 生成唯一ID
const generateId = () => Math.random().toString(36).substring(2, 11);

// 获取对应类型的样式（调整为更贴近参考样式的配色、圆角等）
const getTypeStyles = (type: ToastType) => {
  switch (type) {
    case 'success':
      return 'bg-green-100 text-green-600 border border-green-200';
    case 'error':
      return 'bg-red-100 text-red-600 border border-red-200';
    case 'warning':
      return 'bg-yellow-100 text-yellow-600 border border-yellow-200';
    default:
      return 'bg-[white] text-[#0a0a0a] border border-gray-200';
  }
};

// 获取对应类型的图标（若图标字体或库有调整，可在此同步改）
const getTypeIcon = (type: ToastType) => {
  switch (type) {
    case 'success':
      return '<i class="fa fa-check-circle mr-2"></i>';
    case 'error':
      return '<i class="fa fa-times-circle mr-2"></i>';
    case 'warning':
      return '<i class="fa fa-exclamation-circle mr-2"></i>';
    default:
      return '<i class="fa fa-info-circle mr-2"></i>';
  }
};

// 显示提醒的函数
export const showToast = (
  message: string,
  type: ToastType = 'info',
  duration: number = 3000
) => {
  // 创建提醒元素
  const toastId = `toast-${generateId()}`;
  const toast = document.createElement('div');

  // 设置初始样式（不可见状态，调整为更柔和的初始过渡样式）
  toast.id = toastId;
  toast.className = cn(
    'fixed px-3 py-2 rounded-[16px] transition-all duration-300 flex items-center shadow-sm',
    getTypeStyles(type),
    'opacity-0 translate-y-[-10px] pointer-events-none z-50'
  );

  // 设置内容
  toast.innerHTML = `${getTypeIcon(type)}${message}`;

  // 添加到页面
  document.body.appendChild(toast);

  // 计算位置（右上角，考虑已有提醒，保持基础布局逻辑）
  // 计算位置（底部中间，考虑已有提醒）
  // const toasts = document.querySelectorAll('[id^="toast-"]');
  // 底部间距16px，每个提醒之间间隔12px
  const bottomPosition = 76;
  toast.style.bottom = `${bottomPosition}px`;
  toast.style.left = '50%';
  toast.style.transform = 'translateX(-50%)';

  // 触发淡入动画
  setTimeout(() => {
    toast.classList.remove('opacity-0', 'translate-y-[10px]', 'pointer-events-none');
    toast.classList.add('opacity-100', 'translate-y-0');
  }, 10);

  // 自动消失
  setTimeout(() => {
    // 触发淡出动画
    toast.classList.remove('opacity-100', 'translate-y-0');
    toast.classList.add('opacity-0', 'translate-y-[-10px]', 'pointer-events-none');

    // 动画结束后移除元素
    setTimeout(() => {
      document.body.removeChild(toast);
    }, 300);
  }, duration);
};