
interface HttpRequestOptions {
  headers?: Record<string, string>;
  params?: Record<string, any>;
  data?: any;
  timeout?: number;
  // 用于标记是否是登录请求，避免递归调用
  isLoginRequest?: boolean;
}

interface HttpResponse<T = any> {
  status: number;
  data: T;
  headers: Headers;
}

// Token存储键名
const TOKEN_KEY = 'authToken';
// 用于防止并发请求时多次调用登录接口
let isRefreshing = false;
// 存储等待刷新token的请求队列
let requestsQueue: Array<(token: string) => void> = [];

const BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://fm-ai.qutke.cn:2024';

// 获取本地存储的token
const getToken = (): string | null => {
  return localStorage.getItem(TOKEN_KEY);
};

// 保存token到本地存储
const setToken = (token: string): void => {
  localStorage.setItem(TOKEN_KEY, token);
};

// 清除token
const clearToken = (): void => {
  localStorage.removeItem(TOKEN_KEY);
};

// 登录接口，用于获取新的token
const login = async (): Promise<string> => {
  try {
    // 这里根据你的实际登录需求修改参数
    const response = await httpRequest<{ token: string }>(
      'POST',
      '/api/v1/financial_advisor_auth/sign_in',
      {
        data: {
          access_token: "11111",
          broker_account: "broker_account",
          dept_id_list: [],
          dept_list: null,
          unionid: null,
          user_id: "kkchentingyu",
          user_name: "况客陈婷玉1",
          work_no: "wb200093",
        },
        isLoginRequest: true // 标记为登录请求，避免递归处理
      }
    );
    console.log('response', response)
    if (response.data?.data) {
      setToken(response.data.data);
      return response.data.data;
    }
    throw new Error('登录失败，未返回token');
  } catch (error) {
    console.error('登录请求失败:', error);
    clearToken();
    throw error;
  }
};

// 处理等待队列，用新token重新发起请求
const handleRequestsQueue = (token: string): void => {
  requestsQueue.forEach(callback => callback(token));
  requestsQueue = [];
};

// 确保有Token，如果没有则先登录获取
const ensureToken = async (): Promise<string> => {
  let token = getToken();

  // 如果已有Token，直接返回
  if (token) {
    return token;
  }

  // 如果正在刷新token，则等待
  if (isRefreshing) {
    return new Promise((resolve) => {
      requestsQueue.push((newToken) => resolve(newToken));
    });
  }

  // 没有Token且不在刷新中，执行登录
  isRefreshing = true;
  try {
    const newToken = await login();
    handleRequestsQueue(newToken);
    return newToken;
  } finally {
    isRefreshing = false;
  }
};

async function httpRequest<T = any>(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
  url: string,
  options: HttpRequestOptions = {}
): Promise<HttpResponse<T>> {
  const {
    headers = {},
    params = {},
    data,
    timeout = 5000,
    isLoginRequest = false
  } = options;

  // 处理URL参数
  const queryString = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryString.append(key, String(value));
    }
  });

  const requestUrl = `${BASE_URL}${url}${queryString.toString() ? `?${queryString}` : ''}`;

  // 创建AbortController处理超时
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // 非登录请求需要确保有Token
    let token: string | null = null;
    if (!isLoginRequest) {
      // 确保获取到Token
      token = await ensureToken();
    } else {
      // 登录请求直接获取现有Token（可能为空）
      token = getToken();
    }

    // 构建请求头
    const requestHeaders = {
      'Content-Type': 'application/json',
      ...(token ? { 'Authorization': `Bearer ${token}` } : {}), // 假设使用Bearer认证方式
      ...headers,
    };

    const response = await fetch(requestUrl, {
      method,
      headers: requestHeaders,
      body: data ? JSON.stringify(data) : undefined,
      signal: controller.signal,
    });

    // 清除超时定时器
    clearTimeout(timeoutId);

    // 处理token失效（401状态码）
    if (response.status === 401 && !isLoginRequest) {
      // 清除无效Token
      clearToken();

      // 如果正在刷新token，则将当前请求加入队列等待
      if (isRefreshing) {
        return new Promise((resolve) => {
          requestsQueue.push((newToken) => {
            // 使用新token重新发起请求
            resolve(httpRequest<T>(method, url, options));
          });
        });
      }

      // 标记正在刷新token
      isRefreshing = true;

      try {
        // 调用登录接口获取新token
        const newToken = await login();
        // 处理等待队列中的请求
        handleRequestsQueue(newToken);

        // 用新token重新发起当前请求
        return httpRequest<T>(method, url, options);
      } catch (error) {
        console.error('刷新token失败，无法继续请求:', error);
        throw error;
      } finally {
        // 重置刷新状态
        isRefreshing = false;
      }
    }

    // 解析响应数据
    const responseData = await response.json();

    return {
      status: response.status,
      data: responseData,
      headers: response.headers,
    };
  } catch (error) {
    // 清除超时定时器
    clearTimeout(timeoutId);

    // 处理请求取消（超时）
    if (error instanceof DOMException && error.name === 'AbortError') {
      throw new Error(`请求超时 (${timeout}ms)`);
    }

    throw error;
  }
}

export async function get<T = any>(url: string, options?: Omit<HttpRequestOptions, 'data'>): Promise<HttpResponse<T>> {
  return httpRequest('GET', url, options);
}

export async function post<T = any>(url: string, data: any, options?: HttpRequestOptions): Promise<HttpResponse<T>> {
  return httpRequest('POST', url, { ...options, data });
}

export async function put<T = any>(url: string, data: any, options?: HttpRequestOptions): Promise<HttpResponse<T>> {
  return httpRequest('PUT', url, { ...options, data });
}

export async function del<T = any>(url: string, options?: HttpRequestOptions): Promise<HttpResponse<T>> {
  return httpRequest('DELETE', url, options);
}

export async function patch<T = any>(url: string, data: any, options?: HttpRequestOptions): Promise<HttpResponse<T>> {
  return httpRequest('PATCH', url, { ...options, data });
}
