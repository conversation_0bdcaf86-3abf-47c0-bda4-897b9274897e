import { percentFixedFormatter, getTableRiseColor } from './tableFormatter'

export const tablePercentFixedRender = (
  value: number | undefined | null,
  fixed?: number,
  isAdd?: boolean,
) => {
  if (value === undefined || value === null) {
    return '--'
  }
  const formatterValue = percentFixedFormatter(value, fixed, isAdd)
  if (value > 0) {
    return (
      <span style={{ color: getTableRiseColor(true) }}>{formatterValue}</span>
    )
  }
  if (value < 0) {
    return (
      <span style={{ color: getTableRiseColor(false) }}>{formatterValue}</span>
    )
  }
  return formatterValue
}

export const tablePercentMultiplyFixedRender = (
  value: number | undefined | null,
  fixed?: number,
  isAdd?: boolean,
) => {
  if (value === undefined || value === null) {
    return '--'
  }
  const formatterValue = percentFixedFormatter(value * 100, fixed, isAdd)
  if (value > 0) {
    return (
      <span style={{ color: getTableRiseColor(true) }}>{formatterValue}</span>
    )
  }
  if (value < 0) {
    return (
      <span style={{ color: getTableRiseColor(false) }}>{formatterValue}</span>
    )
  }
  return formatterValue
}

export const formatInternationalNumber = (num: any) => {
  // 处理非数字情况
  if (typeof num !== 'number' || isNaN(num)) {
    return '0.00';
  }

  // 保留两位小数并转换为字符串
  const fixedNum = num.toFixed(2);

  // 分割整数部分和小数部分
  const [integerPart, decimalPart] = fixedNum.split('.');

  // 处理整数部分，添加千位分隔符
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  // 组合整数和小数部分
  return `${formattedInteger}.${decimalPart}`;
}
