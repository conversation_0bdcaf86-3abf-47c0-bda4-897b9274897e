import { get, post, put, del } from "@/utils/http";
import { useQueryState } from "nuqs";

let pre = "/api/v1";
// 保存思考过程
export function saveThinkingProcess(data) {
  return post(`${pre}/thread_run_steps`, data);
}

// 获取思考过程列表
export function getThinkingList(params) {
  return get(`${pre}/thread_run_steps`, { params });
}

//获取智能投顾和智能问答钩子问题

export function getGuessQuestion(params) {
  return get(`${pre}/guess_you_want_to_ask/hook`, { params });
}

//猜你想问

export function getGuessList(params) {
  return get(`${pre}/guess_you_want_to_ask`, { params });
}

//相似基金
export function getSimilarFunds(thread_id, data) {
  return post(
    `${pre}/asset_allocation/thread/${thread_id}/products/replace `,
    data,
  );
}

//AI一键配置
export function getAiConfig(data) {
  return post(`${pre}/asset_allocation`, data);
}
//获取添加基金列表
export function getFunds(data) {
  return post(`${pre}/asset_allocation/products`, data);
}

//轮询接口
export function getPolling(params) {
  return get(`${pre}/polling`, { params });
}

//登录验证
export function login() {
  // const [user_id] = useQueryState("user_id");
  // const [work_no] = useQueryState("work_no");
  // const [broker_account] = useQueryState("broker_account");
  // const [dept_id_list] = useQueryState("dept_id_list");
  let data = {
    access_token: "11111",
    broker_account: "broker_account",
    dept_id_list: [],
    dept_list: null,
    unionid: null,
    user_id: "kkchentingyu",
    user_name: "况客陈婷玉",
    work_no: "wb200093",
  };

  return post(`${pre}/financial_advisor_auth/sign_in`);
}
