import { createContext, useContext, useState, ReactNode } from 'react';

type HistoricalActivity = {
  run_id: string;
  steps: string;
  thread_id: string;
  user_id: string;
};

type ThreadActivitiesContextType = {
  historicalActivities: HistoricalActivity[];
  setHistoricalActivities: (activities: HistoricalActivity[]) => void;
  inputDisabled: boolean;
  setinputDisabled: (disabled: boolean) => void;
  hisrotyInfo: any,
  sethisrotyInfo: React.Dispatch<React.SetStateAction<any>>; // 修改此处类型定义
};

const ThreadActivitiesContext = createContext<ThreadActivitiesContextType>({
  historicalActivities: [],
  setHistoricalActivities: () => { },
  inputDisabled: true,
  setinputDisabled: () => { },
  hisrotyInfo: {
    threadId: null,
    messages: []
  },
  sethisrotyInfo: () => { }
});

export const HistoricalActivitiesProvider = ({ children }: { children: ReactNode }) => {
  const [historicalActivities, setHistoricalActivities] = useState<HistoricalActivity[]>([]);
  const [inputDisabled, setinputDisabled] = useState(true);
  const [hisrotyInfo, sethisrotyInfo] = useState(null)

  return (
    <ThreadActivitiesContext.Provider value={{
      historicalActivities,
      setHistoricalActivities,
      inputDisabled,
      setinputDisabled,
      hisrotyInfo,
      sethisrotyInfo
    }}>
      {children}
    </ThreadActivitiesContext.Provider>
  );
};

export const useThreadActivities = () => useContext(ThreadActivitiesContext);