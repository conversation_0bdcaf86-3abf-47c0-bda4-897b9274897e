import React, {
  createContext,
  useContext,
  ReactNode,
  useState,
  useEffect,
} from "react";
import { useStream } from "@langchain/langgraph-sdk/react";
import { useThreadActivities } from '@/contexts/ThreadActivitiesContext'
import { type Message } from "@langchain/langgraph-sdk";
import {
  uiMessageReducer,
  isUIMessage,
  isRemoveUIMessage,
  type UIMessage,
  type RemoveUIMessage,
} from "@langchain/langgraph-sdk/react-ui";
import { useQueryState } from "nuqs";
import { getApiKey } from "@/lib/api-key";
import { useThreads } from "./Thread";
import { saveThinkingProcess } from '@/services/api'
export type StateType = {
  current_step_detail: any;
  current_step: any;
  messages: Message[];
  ui?: UIMessage[];
  processedEventsTimeline: any,
  showGuessQuestion: boolean;
  setShowGuessQuestion: any,
};

const useTypedStream = useStream<
  StateType,
  {
    UpdateType: {
      messages?: Message[] | Message | string;
      ui?: (UIMessage | RemoveUIMessage)[] | UIMessage | RemoveUIMessage;
      context?: Record<string, unknown>;
    };
    CustomEventType: UIMessage | RemoveUIMessage;
  }
>;

type StreamContextType = ReturnType<typeof useTypedStream>;
const StreamContext = createContext<StreamContextType | undefined>(undefined);

async function sleep(ms = 4000) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

async function checkGraphStatus(
  apiUrl: string,
  apiKey: string | null,
): Promise<boolean> {
  try {
    const res = await fetch(`${apiUrl}/info`, {
      ...(apiKey && {
        headers: {
          "X-Api-Key": apiKey,
        },
      }),
    });

    return res.ok;
  } catch (e) {
    console.error(e);
    return false;
  }
}
export interface ProcessedEvent {
  title: string;
  data: any;
}

const getCurrentStep = (event: any) => {
  const targetObj: any = Object.values(event).find(
    obj => obj && typeof obj === 'object' && 'current_step' in obj
  );
  return targetObj;
}

export interface ProcessedEvent {
  title: string;
  data: any;
}


const StreamSession = ({
  children,
  apiKey,
  apiUrl,
  assistantId,
}: {
  children: ReactNode;
  apiKey: string | null;
  apiUrl: string;
  assistantId: string;
}) => {
  const [processedEventsTimeline, setProcessedEventsTimeline] = useState<
    ProcessedEvent[]
  >([]);
  const [threadId, setThreadId] = useQueryState("threadId");
  const [isPost, setisPost] = useState(false)
  const [id, setid] = useState(null)
  const { getThreads, setThreads } = useThreads();
  const [showGuessQuestion, setShowGuessQuestion] = useState(threadId ? true : false)
  const { historicalActivities, setHistoricalActivities } = useThreadActivities()
  useEffect(() => {
    if (isPost && id && processedEventsTimeline.length > 0) {
      let data = {
        thread_id: threadId,
        run_id: id,
        steps: JSON.stringify(processedEventsTimeline)
      }
      //这里不可以放在接口里面，会造成时间差导致有段时间空白
      // @ts-ignore
      setHistoricalActivities([...historicalActivities, data])
      saveThinkingProcess(data).then(res => {
        if (res && res.data) {
          setProcessedEventsTimeline([])
          setid(null)
          setisPost(false)

        }
      })
    }
  }, [processedEventsTimeline.length, id, isPost])

  const token = localStorage.getItem("authToken");
  const streamValue = useTypedStream({
    apiUrl,
    apiKey: apiKey ?? undefined,
    assistantId,
    threadId: threadId ?? null,
    defaultHeaders: { Authorization: `Bearer ${token}` },
    onUpdateEvent: (event: any) => {
      console.log('event==', event)
      if (event && getCurrentStep(event) && getCurrentStep(event).current_step) {
        let data: ProcessedEvent | null = null;
        data = {
          title: getCurrentStep(event).current_step,
          data: getCurrentStep(event).current_step_detail,
        }
        setProcessedEventsTimeline((prev) => [...prev, data])
      }
      console.log('event && event.__interrupt__', event && event.__interrupt__)
      if (event && event.__interrupt__) {
        let data: ProcessedEvent | null = null;
        data = {
          title: event.__interrupt__[0].value.current_step,
          data: event.__interrupt__[0].value.current_step_detail,
        }
        setProcessedEventsTimeline((prev) => [...prev, data])
      }
    },
    onFinish: (data) => {
      console.log('onFinish==>')
      const { values, tasks } = data
      let length: number = values.messages.length
      let message: any = values.messages[length - 1]
      if (tasks.length > 0 && tasks[0].interrupts && tasks[0].interrupts.length > 0) {
        let interruptId = tasks[0].interrupts[0].value.message.id
        setid(interruptId)
        setisPost(true)
      }
      if (message.type === 'ai') {
        setid(message.id)
        setisPost(true)
      }
      if (data.next.length == 0) {
        setShowGuessQuestion(true)
      }

    },
    onCustomEvent: (event, options) => {
      if (isUIMessage(event) || isRemoveUIMessage(event)) {
        options.mutate((prev) => {
          const ui = uiMessageReducer(prev.ui ?? [], event);
          return { ...prev, ui };
        });
      }
    },
    onThreadId: (id) => {
      setThreadId(id);
      sleep().then(() => getThreads().then(setThreads).catch(console.error));
    },
  });
  // useEffect(() => {
  //   checkGraphStatus(apiUrl, apiKey).then((ok) => {
  //     if (!ok) {
  //       toast.error("连接服务器失败", {
  //         description: () => (
  //           <p>
  //             请确认你的对话还在进行中 <code>{apiUrl}</code> and
  //             your API key is correctly set (if connecting to a deployed graph).
  //           </p>
  //         ),
  //         duration: 10000,
  //         richColors: true,
  //         closeButton: true,
  //       });
  //     }
  //   });
  // }, [apiKey, apiUrl]);
  return (
    <StreamContext.Provider
      value={{
        ...streamValue,
        // @ts-ignore
        showGuessQuestion,
        setShowGuessQuestion,
        processedEventsTimeline
      }}>
      {children}
    </StreamContext.Provider>
  );
};

const DEFAULT_API_URL = process.env.NEXT_PUBLIC_API_URL || "http://************:2024";
const DEFAULT_ASSISTANT_ID = "financial_advisor";

export const StreamProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const envApiUrl: string | undefined = DEFAULT_API_URL
  const envAssistantId: string | undefined = DEFAULT_ASSISTANT_ID

  const [apiUrl, setApiUrl] = useQueryState("apiUrl", {
    defaultValue: envApiUrl || "",
  });
  const [assistantId, setAssistantId] = useQueryState("assistantId", {
    defaultValue: envAssistantId || "",
  });

  const [apiKey, _setApiKey] = useState(() => {
    const storedKey = getApiKey();
    return storedKey || "";
  });

  const setApiKey = (key: string) => {
    window.localStorage.setItem("lg:chat:apiKey", key);
    _setApiKey(key);
  };

  // const finalApiUrl = apiUrl || envApiUrl;
  // const finalAssistantId = assistantId || envAssistantId;

  // if (!finalApiUrl || !finalAssistantId) {
  //   return (
  //     <div className="flex min-h-screen w-full items-center justify-center p-4">
  //       <div className="animate-in fade-in-0 zoom-in-95 bg-background flex max-w-3xl flex-col rounded-lg border shadow-lg">
  //         <div className="mt-14 flex flex-col gap-2 border-b p-6">
  //           <div className="flex flex-col items-start gap-2">
  //             <LangGraphLogoSVG className="h-7" />
  //             <h1 className="text-xl font-semibold tracking-tight">
  //               Agent Chat
  //             </h1>
  //           </div>
  //           <p className="text-muted-foreground">
  //             AI大模型翻译
  //             欢迎来到客服聊天！在开始之前，您需要先登录
  //             部署的URL以及助手/图ID。
  //           </p>
  //         </div>
  //         <form
  //           onSubmit={(e) => {
  //             e.preventDefault();

  //             const form = e.target as HTMLFormElement;
  //             const formData = new FormData(form);
  //             const apiUrl = formData.get("apiUrl") as string;
  //             const assistantId = formData.get("assistantId") as string;
  //             const apiKey = formData.get("apiKey") as string;

  //             setApiUrl(apiUrl);
  //             setApiKey(apiKey);
  //             setAssistantId(assistantId);

  //             form.reset();
  //           }}
  //           className="bg-muted/50 flex flex-col gap-6 p-6"
  //         >
  //           <div className="flex flex-col gap-2">
  //             <Label htmlFor="apiUrl">
  //               部署URL<span className="text-rose-500">*</span>
  //             </Label>
  //             <p className="text-muted-foreground text-sm">
  //               这是您的LangGraph部署的URL。它可以是本地的，也可以是
  //               生产部署。
  //             </p>
  //             <Input
  //               id="apiUrl"
  //               name="apiUrl"
  //               className="bg-background"
  //               defaultValue={apiUrl || DEFAULT_API_URL}
  //               required
  //             />
  //           </div>

  //           <div className="flex flex-col gap-2">
  //             <Label htmlFor="assistantId">
  //               Assistant / Graph ID<span className="text-rose-500">*</span>
  //             </Label>
  //             <p className="text-muted-foreground text-sm">
  //               这是图形的ID（可以是图形名称），或
  //               助手从中获取线程，并在操作发生时调用
  //               拿
  //             </p>
  //             <Input
  //               id="assistantId"
  //               name="assistantId"
  //               className="bg-background"
  //               defaultValue={assistantId || DEFAULT_ASSISTANT_ID}
  //               required
  //             />
  //           </div>

  //           <div className="flex flex-col gap-2">
  //             <Label htmlFor="apiKey">LangSmith API Key</Label>
  //             <p className="text-muted-foreground text-sm">
  //               如果使用本地LangGraph，则不需要
  //               服务器。此值存储在浏览器的本地存储中
  //               仅用于验证发送到LangGraph的请求
  //               服务器。
  //             </p>
  //             <PasswordInput
  //               id="apiKey"
  //               name="apiKey"
  //               defaultValue={apiKey ?? ""}
  //               className="bg-background"
  //               placeholder="lsv2_pt_..."
  //             />
  //           </div>

  //           <div className="mt-2 flex justify-end">
  //             <Button
  //               type="submit"
  //               size="lg"
  //             >
  //               继续
  //               <ArrowRight className="size-5" />
  //             </Button>
  //           </div>
  //         </form>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <StreamSession
      apiKey={apiKey}
      apiUrl={apiUrl}
      assistantId={assistantId}
    >
      {children}
    </StreamSession>
  );
};

// Create a custom hook to use the context
export const useStreamContext = (): StreamContextType => {
  const context = useContext(StreamContext);
  if (context === undefined) {
    throw new Error("useStreamContext must be used within a StreamProvider");
  }
  return context;
};

export default StreamContext;
