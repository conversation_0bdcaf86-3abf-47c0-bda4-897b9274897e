import { get<PERSON><PERSON><PERSON><PERSON> } from "@/lib/api-key";
import { Thread } from "@langchain/langgraph-sdk";
import {
  createContext,
  useContext,
  ReactNode,
  useCallback,
  useState,
  Dispatch,
  SetStateAction,
} from "react";
import { createClient } from "./client";

interface ThreadContextType {
  getThreads: () => Promise<Thread[]>;
  threads: Thread[];
  deleteThread: (threadId: string) => Promise<Thread>;
  creatThread: (threadId: string) => Promise<Thread>;
  updateThread: (threadId: string, name: string) => Promise<Thread>;
  setThreads: Dispatch<SetStateAction<Thread[]>>;
  threadsLoading: boolean;
  setThreadsLoading: Dispatch<SetStateAction<boolean>>;
}

const ThreadContext = createContext<ThreadContextType | undefined>(undefined);


export function ThreadProvider({ children }: { children: ReactNode }) {
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || "http://54.223.63.61:2024"
  const assistantId = 'financial_advisor';
  const [threads, setThreads] = useState<Thread[]>([]);
  const [threadsLoading, setThreadsLoading] = useState(false);

  const getThreads = useCallback(async (): Promise<Thread[]> => {
    if (!apiUrl || !assistantId) return [];
    const client = createClient(apiUrl, getApiKey() ?? undefined);
    const threads = await client.threads.search({
      metadata: {
        graph_id: 'financial_advisor',
      },
      limit: 100,
      offset: 0

    });

    return threads;
  }, [apiUrl, assistantId]);
  //删除对话
  const deleteThread = useCallback(async (threadId: string) => {
    if (!apiUrl || !assistantId) return [];
    const client = createClient(apiUrl, getApiKey() ?? undefined);
    const threads = await client.threads.delete(threadId);
    return threads;
  }, [apiUrl, assistantId]);
  //新增对话
  const creatThread = useCallback(async () => {
    const iframeData = localStorage.getItem('iframeData')
    if (iframeData) {
      let parsedData = JSON.parse(iframeData);
      if (!apiUrl || !assistantId) return [];
      const client = createClient(apiUrl, getApiKey() ?? undefined);
      const threads = await client.threads.create({
        metadata: {
          name: '新的聊天',
          user_id: parsedData.user_id
        },
        graphId: "financial_advisor"
      });
      return threads;
    }
  }, [apiUrl, assistantId]);
  //更新对话
  const updateThread = useCallback(async (id: string, name: string | null) => {
    if (!apiUrl || !assistantId) return [];
    const client = createClient(apiUrl, getApiKey() ?? undefined);
    const threads = await client.threads.update(id, {
      metadata: { name }
    });
    return threads;
  }, [apiUrl, assistantId]);

  const value = {
    getThreads,
    threads,
    deleteThread,
    creatThread,
    updateThread,
    setThreads,
    threadsLoading,
    setThreadsLoading,
  };

  return (
    // @ts-ignore
    <ThreadContext.Provider value={value}>{children}</ThreadContext.Provider>
  );
}

export function useThreads() {
  const context = useContext(ThreadContext);
  if (context === undefined) {
    throw new Error("useThreads must be used within a ThreadProvider");
  }
  return context;
}
