import React, { memo, HTMLAttributes, useState } from "react";
import MarkdownContent from "../MarkdownContent";
import LoadingIcon from "@/app/icons/three-dots.svg";

export interface MarkdownProps extends HTMLAttributes<HTMLDivElement> {
  inMiniprogram?: boolean;
  content?: string;
  loading?: boolean;
  style?: React.CSSProperties;
  onLongClick?: () => void;
}

export const Markdown: React.FC<MarkdownProps> = (props) => {
  const {
    inMiniprogram,
    content,
    loading,
    style,
    onClick,
    onContextMenu,
    onDoubleClickCapture,
    onLongClick,
  } = props;

  // console.log("Markdown");
  const [timeOutEvent, setTimeOutEvent] = useState<number | undefined>(
    undefined,
  );

  const onTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    // 手指按下去
    const timeOutId = window.setTimeout(() => {
      console.log("长按事件触发了");
      clearTimeout(timeOutId);
      setTimeOutEvent(undefined);
      onLongClick && onLongClick();
    }, 1000); // 这里可以自定义长按的时间，比如500毫秒
    setTimeOutEvent(timeOutId);
  };

  const onTouchEnd = (e: React.TouchEvent<HTMLDivElement>) => {
    // 手指离开屏幕
    clearTimeout(timeOutEvent);
    setTimeOutEvent(undefined);
  };

  const onTouchMove = (e: React.TouchEvent<HTMLDivElement>) => {
    // 手指移动
    clearTimeout(timeOutEvent);
    setTimeOutEvent(undefined);
  };

  return (
    <div
      className="markdown-body"
      style={{
        fontSize: 14,
        ...(style || {}),
      }}
      onContextMenu={onContextMenu}
      onClick={onClick}
      onDoubleClickCapture={onDoubleClickCapture}
      onTouchStart={onTouchStart}
      onTouchEnd={onTouchEnd}
      onTouchMove={onTouchMove}
    >
      {loading && !content ? (
        <LoadingIcon />
      ) : (
        <MarkdownContent
          inMiniprogram={inMiniprogram}
          content={
            loading ? `${content}[cursor](${process.env.NEXT_PUBLIC_API_URL || 'http://************:2024'})` : content
          }
        />
      )}
    </div>
  );
};

export default memo(Markdown);
