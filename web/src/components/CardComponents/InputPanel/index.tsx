import IconButton from "../IconButton";
import SendIcon from "@/app/icons/send.svg";
import { autoGrowTextArea } from '@/utils/utils'
import Loading from "@/app/icons/three-dots.svg";
import { SpinLoading } from 'antd-mobile'


import React, {
  memo,
  useRef,
  useMemo,
  useState,
  useEffect,
  useCallback,
  HTMLAttributes,
} from "react";
// @ts-nocheck

export enum SubmitKey {
  Enter = "Enter",
  CtrlEnter = "Ctrl + Enter",
  ShiftEnter = "Shift + Enter",
  AltEnter = "Alt + Enter",
  MetaEnter = "Meta + Enter",
}

export interface InputPanelProps extends HTMLAttributes<HTMLElement> {
  submitKey?: string;
  hitBottom?: boolean;
  inputChange: (text?: string) => void;
  inputFocus?: () => void;
  inputBlur?: () => void;
  toBottom?: () => void;
  loading: boolean
  inputTypeChange?: (val: any) => {}
  submitClick: (text: any) => void
  inputDisabled?: boolean;
}

const InputPanel: React.FC<InputPanelProps> = (props) => {
  const { toBottom, inputDisabled, inputTypeChange, inputFocus, inputBlur, inputChange, loading, submitKey } = props;
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const [inputRows, setInputRows] = useState(1);
  const [userInput, setUserInput] = useState<string | undefined>(undefined);
  const [doLoading, setDoLoading] = useState<boolean>(false);

  const shouldSubmit = useCallback(
    (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (e.key !== "Enter") return false;
      if (e.key === "Enter" && e.nativeEvent.isComposing) return false;
      return (
        (submitKey === SubmitKey.AltEnter && e.altKey) ||
        (submitKey === SubmitKey.CtrlEnter && e.ctrlKey) ||
        (submitKey === SubmitKey.ShiftEnter && e.shiftKey) ||
        (submitKey === SubmitKey.MetaEnter && e.metaKey) ||
        (submitKey === SubmitKey.Enter &&
          !e.altKey &&
          !e.ctrlKey &&
          !e.shiftKey &&
          !e.metaKey)
      );
    },
    [submitKey],
  );

  const onInputKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (
      e.key === "ArrowUp" &&
      (!userInput || userInput.length <= 0) &&
      !(e.metaKey || e.altKey || e.ctrlKey)
    ) {
      e.preventDefault();
      return;
    }

    // 处理组合键换行的情况
    if ((e.ctrlKey && e.key === "Enter") || (e.metaKey && e.key === "Enter")) {
      userInputChange(userInput + "\n");
      e.preventDefault();
      return;
    }

    // 如果满足提交条件，则执行提交
    if (shouldSubmit(e)) {
      e.preventDefault();
      // 添加这行代码来触发提交
      doSubmit(userInput);
    }
  };

  const userInputChange = (text: string | undefined) => {
    setUserInput(text);
    if (text) {
      const rows = inputRef.current ? autoGrowTextArea(inputRef.current) : 1;
      const newinputRows = Math.min(20, Math.max(1, rows));
      setInputRows(newinputRows);
      inputChange(text)
    } else {
      setInputRows(1);
    }
  };

  const doSubmit = (text: string | undefined) => {
    if (!text || !text.trim()) return;
    userInputChange(undefined);
    props.submitClick && props.submitClick(text);
  };

  const buttonDisabled = useMemo(() => {
    return doLoading || !userInput
  }, [doLoading, userInput]);
  return (
    <div className="w-full box-border bg-white pb-[env(safe-area-inset-bottom)]">
      {inputDisabled && (
        <div className="box-border px-5 pb-5 pt-2.5">
          <div className="flex flex-1 relative">
            <textarea
              className="w-full h-full rounded-3xl shadow-[0_4px_16px_rgba(191,146,103,0.1),0_-2px_5px_rgba(0,0,0,0.03)] bg-white border border-[var(--primary)] font-inherit outline-none resize-none px-3 py-2"
              ref={inputRef}
              placeholder="发送消息..."
              rows={inputRows}
              onInput={(e) => {
                const text = e.currentTarget.value;
                userInputChange(text);
              }}
              value={userInput || ""}
              onKeyDown={onInputKeyDown}
              onFocus={() => {
                toBottom && toBottom();
                inputFocus && inputFocus();
              }}
              onBlur={() => {
                inputBlur && inputBlur();
              }}
              style={{
                maxHeight: 300,
                lineHeight: "21px",
                paddingRight: userInput ? 130 : 14,
              }}
            />
            {(userInput || loading) &&
              <div
                onClick={() => {
                  if (buttonDisabled) return;
                  doSubmit(userInput);
                }}
                className={`absolute bg-[#29304BFF] right-1.5 bottom-1  h-[30px] w-[124px] border  text-white border-[var(--primary)] rounded-[18px] box-border flex items-center justify-center gap-2 ${loading ? "cursor-not-allowed" : "cursor-pointer"}`}>
                {loading ? (
                  <div className="flex items-center justify-center">
                    <SpinLoading style={{ '--size': '14px' }} color='primary'></SpinLoading>
                    <div className="ml-2">加载中</div>
                  </div>
                ) : (
                  <div className="flex">
                    <div className="relative top-[3px]"><SendIcon /></div>
                    <div className="ml-2">发送</div>
                  </div>
                )}
              </div>
            }
          </div>
        </div>
      )}
    </div>
  );
}

export default InputPanel
