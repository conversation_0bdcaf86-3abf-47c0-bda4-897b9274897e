import React from 'react';
import { Avatar } from 'antd-mobile'

interface UserProfileProps {
  data?: any;
  children?: React.ReactNode;
  tagChildren?: React.ReactNode;
}

const UserProfile: React.FC<UserProfileProps> = ({ data, tagChildren, children }) => {
  let avatarUrl = 'https://randomuser.me/api/portraits/men/1.jpg'
  return (
    <div className="w-full block p-4 bg-gradient-to-r from-[rgba(242,188,102,0.05)] to-[rgba(242,188,102,0.2)] rounded-t-lg">
      <div className="flex items-center mb-1">
        <Avatar size={48} src={avatarUrl} />
        <div className="ml-4">
          <h3>{data?.customer_name}</h3>
          <p>编号: {data?.customer_id}</p>
          <p>手机号: {data?.phone_number}</p>
        </div>
      </div>
      {tagChildren ? tagChildren :
        <div className="flex gap-2 mt-3">
          {
            data?.customer_age &&
            <div className="px-3 py-1.5 rounded text-sm font-medium bg-[#29304B1A] text-[#29304BFF]">
              {data?.customer_age}岁
            </div>
          }

          {/* <div className="px-3 py-1.5 rounded text-sm font-medium bg-[#DD4E3F1A] text-[#DD4E3FFF]">
            {data?.invest_seniority}
          </div> */}
          {
            data?.holding_amount &&
            <div className="px-3 py-1.5 rounded text-sm font-medium bg-[#BF92671A] text-[#BF9267FF]">
              持仓金额{data?.holding_amount / 10000}万元
            </div>
          }

        </div>
      }
      {children}
    </div>
  );
};

export default UserProfile;