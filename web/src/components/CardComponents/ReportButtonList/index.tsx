import React, { useState, useEffect } from 'react';
import { useStreamContext } from "@/providers/Stream";
import { v4 as uuidv4 } from "uuid";
import { useThreadActivities } from '@/contexts/ThreadActivitiesContext'
import InformationOrigin from '@/components/CardComponents/InformationOrigin'
// @ts-nocheck

const ButtonList = ({ setNewMessages, data }: { setNewMessages: any, data: any }) => {
  const thread = useStreamContext();
  const { completed } = data;
  // 初始激活"生成推荐话术"按钮
  const { setinputDisabled } = useThreadActivities();

  useEffect(() => {
    setinputDisabled(false)
  }, [])

  // 按钮点击处理函数
  const handleButtonClick = () => {
    // 如果是生成按钮且completed为true，则不执行任何操作
    if (completed) return;

    setinputDisabled(true);

    const newHumanMessage: any = {
      id: uuidv4(),
      type: "human",
      response_metadata: {
        interrupting: true
      },
      content: '生成推荐话术'
    };
    if (!!thread.interrupt && thread.interrupt.value) {
      let newAiMessage = {
        //@ts-ignore
        ...thread.interrupt.value.message,
        tool_calls: [
          {
            //@ts-ignore
            ...thread.interrupt.value.message.tool_calls[0],
            args: {
              //@ts-ignore
              ...thread.interrupt.value.message.tool_calls[0].args,
              card_data: {
                //@ts-ignore
                ...thread.interrupt.value.message.tool_calls[0].args.card_data,
                completed: true
              }
            }
          }
        ]
      }
      setNewMessages([...thread.messages, newAiMessage, newHumanMessage])
      thread.submit({},
        {
          command:
          {
            resume: '生成推荐话术',
            update: {
              messages: [
                newAiMessage,
                newHumanMessage
              ],
            }
          }
        })
    }
  };

  // 计算按钮样式的辅助函数
  const getButtonStyles = (buttonId: any) => {
    // 生成按钮且completed为true时特殊处理
    if (buttonId === 'generate' && completed) {
      return 'mb-2 px-3 py-2 w-[150px] rounded-[8px] text-center cursor-pointer bg-gray-200 text-gray-500 ';
    }

    return `mb-2 px-3 py-2 w-[150px] rounded-[8px] text-center bg-[#BF9267FF] text-[white] cursor-pointer`;
  };

  const cardData = {
    table: [
      {
        name: '配置分析'
      },
      {
        name: '规划报告'
      }
    ]
  }

  return (
    <div className='p-2 pl-0 '>
      <div
        className={getButtonStyles('generate')}
        onClick={() => handleButtonClick()}
      >
        生成推荐话术
      </div>
      <div className='relative left-[-10px]'>
        <InformationOrigin noTitle={true} data={cardData}></InformationOrigin>
      </div>
    </div>
  );
};

export default ButtonList;
