// @ts-nocheck

import { useEffect, useState } from 'react';
import { useStreamContext } from "@/providers/Stream";
import { v4 as uuidv4 } from "uuid";
import { useThreadActivities } from '@/contexts/ThreadActivitiesContext'
interface CommunicateInfoType {
  setNewMessages: any;
  data: any;
}
const CommunicateInfo = ({ setNewMessages, data }: CommunicateInfoType) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const toggleExpand = () => setIsExpanded(!isExpanded);
  const thread = useStreamContext();
  const { completed } = data
  const { setinputDisabled } = useThreadActivities();
  useEffect(() => {
    setinputDisabled(false)
  }, [])
  const sections = [
    {
      title: '一、基本信息',
      items: [
        '1. 需要明确客户的姓名、性别和出生年份。',
        '2. 需要了解客户的婚姻状况，包括未婚、已婚、离异或丧偶。',
        '3. 需要知晓客户目前居住的城市以及户籍所在地。'
      ]
    },
    {
      title: '二、家庭信息',
      items: [
        '4. 需要了解客户的家庭成员构成，例如是否包含配偶、子女、父母等。',
        '5. 需要确认家庭成员中是否存在需要长期赡养或抚养的对象，比如年幼的子女或年迈的父母。',
        '6. 需要明确家庭主要的经济来源是本人、配偶、共同承担还是其他。',
        '7. 需要了解家庭的年总收入大致范围。'
      ]
    },

  ];
  const moreSections = [
    {
      title: '三、职业与收入',
      items: [
        '8. 需要了解客户目前的职业和工作单位性质，例如是企业主、公务员、事业单位员工、私企员工还是自由职业者等。',
        '9. 需要知晓客户的月收入或年收入的大致范围。',
        '10. 需要评估客户收入的稳定性，分为非常稳定、较为稳定、一般或不稳定。'
      ]
    },
    {
      title: '四、资产与负债',
      items: [
        '11. 需要了解客户目前拥有的主要资产类型，例如房产、汽车、存款、股票、基金、保险等。',
        '12. 需要了解客户目前是否存在房贷、车贷、信用卡等负债，并明确负债总额的大致范围。',
        '13. 需要了解客户是否持有应急备用金及其金额。'
      ]
    },
    {
      title: '五、支出与储蓄',
      items: [
        '14. 需要了解客户家庭每月或每年的主要支出项目，例如房贷、教育、医疗、日常生活开销等。',
        '15. 需要了解客户每月或每年大约能结余多少资金用于投资。'
      ]
    },
    {
      title: '六、保险保障',
      items: [
        '16. 需要了解客户和家庭成员是否已配置保险，例如寿险、重疾险、医疗险、意外险等。',
        '17. 需要评估现有保险保障是否充足，以及是否存在补充保险的需求。'
      ]
    },
    {
      title: '七、投资经验与偏好',
      items: [
        '18. 需要了解客户过往主要投资过的产品类型，例如银行理财、基金、股票、债券、私募、期货等。',
        '19. 需要了解客户对哪些投资产品比较熟悉或感兴趣。',
        '20. 需要了解客户是否经历过较大的投资亏损，以及当时的感受和应对方式。'
      ]
    },
    {
      title: '八、投资目标与规划',
      items: [
        '21. 需要了解客户进行投资的主要目标，例如资产增值、资产保值、子女教育、退休养老、购房购车、财富传承等。',
        '22. 需要了解客户对资金流动性的要求高不高，是否需要随时可以取用。',
        '23. 需要了解客户对投资收益和风险的期望值分别是多少。',
        '24. 需要了解客户计划投资的时间周期是多久。'
      ]
    },
    {
      title: '九、其他特殊需求',
      items: [
        '25. 需要了解客户是否计划在未来几年内有大额支出，例如购房、子女留学、创业等。',
        '26. 需要了解客户是否有境外投资或移民的打算。',
        '27. 需要了解客户是否有遗产规划或财富传承的需求。'
      ]
    },
    {
      title: '十、补充信息',
      items: [
        '28. 需要了解客户是否有其他需要补充说明的个人或家庭情况。'
      ]
    }
  ]
  const handleClick = (type: any) => () => {
    setinputDisabled(true)
    if (type == 'prefill') {
      return
    }
    const newHumanMessage: any = {
      id: uuidv4(),
      type: "human",
      response_metadata: {
        interrupting: true
      },
      content: '直接让AI帮我填写规划需求，我再手动调整'
    };
    if (!!thread.interrupt && thread.interrupt.value) {
      let newAiMessage = {
        ...thread?.interrupt?.value?.message,
        tool_calls: [
          {
            ...thread.interrupt.value.message.tool_calls[0],
            args: {
              ...thread.interrupt.value.message.tool_calls[0].args,
              card_data: {
                ...thread.interrupt.value.message.tool_calls[0].args.card_data,
                completed: true
              }
            }
          }
        ]
      }
      setNewMessages([...thread.messages, newAiMessage, newHumanMessage])
      thread.submit({},
        {
          command:
          {
            resume: '',
            update: {
              messages: [
                newAiMessage,
                newHumanMessage
              ],
            }
          }
        })
    }
  }

  const showData = isExpanded ? sections.concat(moreSections) : sections;
  return (
    <div>
      <div className="bg-white rounded-lg p-5 mt-4 mb-5 shadow-[0_2px_8px_rgba(0,0,0,0.1)]">
        <div className="text-base text-[#333] mb-5 leading-[1.6] flex flex-col gap-3">
          {data.title}
        </div>
        <div className="relative bg-[rgba(10,10,10,0.03)] rounded-lg p-2.5 px-3">
          {showData.map((section, index) => (
            <div key={index} className="mb-4 pb-0">
              <div className="font-medium text-sm text-[#0A0A0A] text-left font-['PingFang_SC']">
                {section.title}
              </div>
              <div>
                {section.items.map((item, i) => (
                  <p className="indent-4 font-normal text-sm text-[rgba(10,10,10,0.6)] text-left font-['PingFang_SC'] leading-[21px]" key={i}>
                    {item}
                  </p>
                ))}
              </div>
            </div>
          ))}
          {isExpanded ? '' : <p className="indent-4 font-normal text-sm text-[rgba(10,10,10,0.6)] text-left font-['PingFang_SC'] leading-[21px]">......</p>}
          <span
            className="absolute top-0 right-2.5 text-[#BF9267FF] text-sm cursor-pointer bg-none border-none p-0 self-start mt-2 transition-all duration-200 ease-in-out active:scale-95"
            onClick={toggleExpand}
          >
            {isExpanded ? '收起' : '展开'}
          </span>
        </div>

      </div>
      <div className='flex gap-2'>
        <div
          onClick={!completed ? handleClick('prefill') : undefined}
          className={`px-3 py-2 cursor-pointer rounded-[8px] text-center ${completed ? 'bg-[#cacaca88] text-[#7b7b7b88]' : 'bg-[#BF9267FF] text-white'}`}
        >
          补充信息后AI预填
        </div>
        <div
          onClick={!completed ? handleClick('adjust') : undefined}
          className={`px-3 cursor-pointer py-2 rounded-[8px] text-center ${completed ? 'bg-[#cacaca88] text-[#7b7b7b88]' : 'bg-[#BF9267FF] text-white'}`}
        >
          AI预填后手动调整
        </div>
      </div>
    </div>
  );
}

export default CommunicateInfo;