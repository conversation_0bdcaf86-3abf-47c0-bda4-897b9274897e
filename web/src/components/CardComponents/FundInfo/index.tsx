// @ts-nocheck

const FundInfo = ({ data }: { data: any }) => {
  return (
    <div className="bg-white rounded-lg w-full p-4 bg-gradient-to-b from-[rgba(191,146,103,0.1)] to-transparent">
      <h3 className="text-[#3E2723] text-lg font-semibold mb-2">{data?.product_name}</h3>
      <div className="flex gap-2 mb-3">
        <span className="bg-[#BF9267FF] text-white px-2 py-1  rounded-[4px] text-12">
          {data?.product_category}
        </span>
        <span className="bg-[#BF92671A] text-[#BF9267FF] px-2 py-1  rounded-[4px] text-12">
          {data?.product_type}
        </span>
        <span className="bg-[#BF92671A] text-[#BF9267FF] px-2 py-1  rounded-[4px] text-12">
          {data?.product_strategy}
        </span>
        <span className="bg-[#ED7B2F1A] text-[#ED7B2FFF] px-2 py-1  rounded-[4px] text-12">
          {data?.risk_level}
        </span>
      </div>
      <p className="bg-[#0A0A0A08] rounded-[6px] px-3 py-2 text-[#757575] text-sm">业绩基准：{data?.performance_benchmark}</p>
    </div>
  )
}

export default FundInfo