"use client";
import React, { useState, useEffect, useRef } from 'react';
import { Search, Trash2, RefreshCw } from 'lucide-react';
import { Tabs, Divider, Checkbox, List, Button, Empty, Spin, Alert } from 'antd';
import { Popup, SearchBar, InfiniteScroll } from 'antd-mobile';
import { getFunds } from '@/services/api'
import res from './data.json';
import { SpinLoading } from 'antd-mobile'
const mockProducts: any = res.data.apqList

// 分类映射（与原fetchProducts逻辑保持一致）
const categoryMap: Record<string, string> = {
  'DCM': '固定类',
  'EQ': '权益类',
  'MM': '混合类',
  'OTH': '商品及衍生品' // 匹配category.assetcatCode = "OTH"
};



const flattenCategory = (category: any) => {
  if (!category || !category.secondaryCategories?.length) return [];
  // 遍历二级分类 → 遍历产品，组合成一维数据
  return category.secondaryCategories.reduce((acc: any[], secondary: any) => {
    const secondaryProducts = secondary.products?.map((product: any) => ({
      // 一级分类信息
      assetcatCode: category.assetcatCode,
      assetcatName: categoryMap[category.assetcatCode] || category.assetcatName, // 统一分类名称格式
      // 二级分类信息
      pfcatCode: secondary.pfcatCode,
      pfcatName: secondary.pfcatName,
      // 产品基础信息（从category提取）
      proCode: product.proCode,
      proName: product.proName,
      proRiskCode: product.proRiskCode,
      proRiskName: product.proRiskName,
      weight: product.weight,
      weightAmount: product.weightAmount,
      nav: '0.0000', // 若有真实净值可替换，此处默认0
      date: new Date().toISOString().split('T')[0], // 格式：YYYY-MM-DD
    })) || [];

    return [...acc, ...secondaryProducts];
  }, []);
};

const fetchProducts = async (
  page: number,
  pageSize: number = 10,
  assetcatName: string,
  activeSubCategory: string,
  searchText: string
): Promise<{ data: any[], hasMore: boolean }> => {
  let data = {
    page_num: page,
    page_size: pageSize,
    asset_cat_code: assetcatName,
    pf_cat_code: activeSubCategory
  }
  const relres = await getFunds(data);

  // 获取接口返回的总条数
  const totalCount = relres.data?.totalCount || 0;
  const products: any = relres.data?.apqList || mockProducts;

  // 修改hasMore逻辑：基于总条数判断是否还有更多数据
  // 已加载的数量 = 当前页 * 每页大小
  // 如果已加载数量 < 总条数，则还有更多数据
  const hasMore = page * pageSize < totalCount;

  return { data: products, hasMore };
};


const AddProductPage = ({ children, category, onSelect }: { children: React.ReactNode, category: any, onSelect: (funds: any) => void }) => {
  const [searchText, setSearchText] = useState('');
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<any[]>([]);
  const [activeCategory, setActiveCategory] = useState('DCM');
  const [activeSubCategory, setActiveSubCategory] = useState('FUND');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [error, setError] = useState<string | null>(null);
  const initProductList = async () => {
    setLoading(true);
    setError(null);
    setPage(1);
    try {
      const { data, hasMore } = await fetchProducts(page, 10, activeCategory, activeSubCategory, searchText);
      setFilteredProducts(data);
      setHasMore(hasMore);
    } catch (err) {
      setError('加载失败，请重试');
      console.error('初始化产品列表失败:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!category) return;
    const flattenedData = flattenCategory(category);
    setActiveCategory(category.assertatCode)
    setSelectedProducts(flattenedData);
  }, [category]);

  useEffect(() => {
    if (isModalOpen) {
      initProductList();
    }
  }, [isModalOpen, activeCategory, activeSubCategory, searchText]);

  // 移除 IntersectionObserver 相关代码

  const loadMoreProducts = async () => {
    if (loading || !hasMore) return;
    setLoading(true);
    setError(null);
    try {
      const nextPage = page + 1;
      const { data, hasMore: newHasMore } = await fetchProducts(
        nextPage, 10, activeCategory, activeSubCategory, searchText
      );
      setFilteredProducts(prev => [...prev, ...data]);
      setPage(nextPage);
      setHasMore(newHasMore);
    } catch (err) {
      setError('加载更多失败，请重试');
      console.error('加载更多产品失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  const handleProductSelect = (product: any, checked: boolean) => {
    if (checked) {
      if (!selectedProducts.some(p => p.proCode === product.proCode)) {
        setSelectedProducts(prev => [...prev, product]);
      }
    } else {
      setSelectedProducts(prev => prev.filter(p => p.proCode !== product.proCode));
    }
  };

  const removeSelectedProduct = (proCode: string) => {
    setSelectedProducts(prev => prev.filter(p => p.proCode !== proCode));
  };

  const isProductSelected = (proCode: string) => {
    return selectedProducts.some(product => product.proCode === proCode);
  };

  const handleConfirmSelection = () => {
    setIsModalOpen(false);
    setSelectedProducts([])
    setFilteredProducts([])
    onSelect(selectedProducts);
  };

  return (
    <div>
      {/* 触发模态框的元素 */}
      <div onClick={() => setIsModalOpen(true)} style={{ cursor: 'pointer' }}>
        {children}
      </div>
      <Popup
        visible={isModalOpen}
        onMaskClick={() => {
          setIsModalOpen(false)
        }}
        onClose={() => setIsModalOpen(false)}
        position="bottom"
        bodyStyle={{
          height: '90vh',
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          padding: '0'
        }}
      >
        <div className="p-4 text-base font-bold sticky top-0 bg-white z-10 border-b border-gray-200">
          添加产品
        </div>
        <div className="bg-[white]">
          {/* 搜索框 */}
          <div className="p-2 bg-white">
            <SearchBar
              clearable
              clearOnCancel
              placeholder="请输入产品名称"
              value={searchText}
              onChange={(value) => handleSearch(value)}
              showCancelButton
              className="rounded-full"
            />
          </div>
          {/* 分类标签 */}
          <div className="mx-2 rounded-[8px] py-2 px-3 bg-white">
            <Tabs
              activeKey={activeSubCategory}
              onChange={setActiveSubCategory}
              size="small"
              items={[
                { key: 'TRUST', label: '信托' },
                { key: 'FUND', label: '公募基金' },
              ]}
            />
          </div>

          {/* 已选产品区域 */}
          <div className="mx-2 rounded-[8px] p-4 bg-white max-h-[200px] overflow-y-scroll">
            <div className="flex justify-between items-center mb-2">
              <div>
                已选择<span className="text-[#BF9267FF]"> {selectedProducts.length}</span> 只产品，请选择 2～20 支产品
              </div>
            </div>
            <List
              locale={{ emptyText: '暂无已选产品' }}
              dataSource={selectedProducts}
              renderItem={(item) => (
                <List.Item
                  className="flex justify-between items-center mb-2 border-0 bg-[#f7f7f7] rounded-[6px] p-2 py-3"
                >
                  <div className="pl-2 flex items-center">
                    <div className="font-medium mr-1 text-sm">{item.proName}</div>
                    <div className="text-xs text-blue-400">{item.proRiskName}</div>
                  </div>
                  <Button
                    icon={<Trash2 size={14} color="#BF9267FF" />}
                    size="small"
                    type="link"
                    onClick={() => removeSelectedProduct(item.proCode)}
                  />
                </List.Item>
              )}
            />
          </div>

          {/* 产品列表（带无限滚动） */}
          <div className="mx-2 rounded-[8px] p-4 bg-white" style={{ height: '400px', overflow: 'auto' }}>
            <Divider size="small" />
            <div className="flex justify-between mb-3 text-[#0a0a0a77] text-sm">
              <div className="font-medium">产品名称</div>
              <div className="font-medium">最新净值</div>
            </div>
            {filteredProducts.length > 0 ? (
              <List
                dataSource={filteredProducts}
                renderItem={(item) => (
                  <List.Item
                    className="flex px-4 py-3 justify-between items-center border-b last:border-0 hover:bg-[#fafafa]"
                  >
                    <div className="flex items-center">
                      <Checkbox
                        checked={isProductSelected(item.proCode)}
                        onChange={(e) => handleProductSelect(item, e.target.checked)}
                        disabled={selectedProducts.length >= 20 && !isProductSelected(item.proCode)}
                      />
                      <div className="ml-2 font-medium text-sm">{item.proName && item.proName.length > 16 ? item.proName.slice(0, 16) + '...' : item.proName}</div>
                    </div>
                  </List.Item>
                )}
              />
            ) : (
              <Empty description="未找到匹配的产品" className="my-8" />
            )}

            <InfiniteScroll
              loadMore={loadMoreProducts}
              hasMore={hasMore}
              threshold={50}
            >
              {loading && (
                <div className="flex justify-center p-4 text-[#BF9267FF]">
                  <SpinLoading style={{ '--size': '14px' }} color='#00b578'></SpinLoading>加载中
                </div>
              )}
              {error && (
                <div className="flex justify-center p-4">
                  <span className="text-red-500 mr-2">{error}</span>
                  <Button
                    size="small"
                    onClick={loadMoreProducts}
                  >
                    <RefreshCw size={12} className="mr-1" />
                    重试
                  </Button>
                </div>
              )}
              {!hasMore && (
                <div className="text-center p-4 text-sm text-gray-500">
                  已加载全部数据
                </div>
              )}
            </InfiniteScroll>
          </div>
          <div className="sticky bottom-0 p-4 bg-white border-t border-gray-200 flex justify-between">
            <Button
              onClick={() => setIsModalOpen(false)}
              className="flex-1 mr-2"
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={handleConfirmSelection}
              className="flex-1"
            >
              确认
            </Button>
          </div>
        </div>
      </Popup>
    </div>
  );
};

export default AddProductPage;