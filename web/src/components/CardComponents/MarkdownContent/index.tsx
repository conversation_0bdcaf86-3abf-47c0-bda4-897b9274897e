import React, { memo, HTMLAttributes } from "react";
import ReactMarkdown from "react-markdown";
import "katex/dist/katex.min.css";
import RemarkMath from "remark-math";
import RemarkBreaks from "remark-breaks";
import RehypeKatex from "rehype-katex";
import RemarkGfm from "remark-gfm";
import <PERSON><PERSON><PERSON><PERSON><PERSON>light from "rehype-highlight";
import WeiXin from "weixin-js-sdk";

import styles from "./index.module.scss";

export interface MarkdownContentProps extends HTMLAttributes<HTMLElement> {
  inMiniprogram?: boolean;
  content?: string;
}

const MarkdownContent: React.FC<MarkdownContentProps> = (props) => {
  const { inMiniprogram, content } = props;

  const miniLinkClick = (href: string) => {
    // 组合
    // /workbench/public/portfolio/detail/20230918134615936640
    // 基金
    // /workbench/public/products/analysis/overview?id=${id}
    // 经理
    // /workbench/public/manager/detail?id=${id}
    // 公司
    // /workbench/public/company/detail?id=${id}

    /**
     * 校验code是否合法
     * @param code
     * @returns
     */
    const chackeId = (code: string) => {
      return /^\d+$/.test(code);
    };
    // const portfolioTag = 'portfolio/detail/'
    const fundTag = "products/analysis/overview?id=";
    const managerTag = "manager/detail?id=";
    const companyTag = "company/detail?id=";
    if (href.includes(fundTag)) {
      const codes = href.split(fundTag);
      if (codes.length <= 0) {
        console.log("没有code");
        return;
      }
      const mainCode = codes[codes.length - 1];
      // 检查是否是数字字符串
      if (!chackeId(mainCode)) {
        console.log("不是数字字符串");
        return;
      }
      WeiXin.miniProgram.navigateTo({
        url: `/subPackageB/fundInfo/index?mainCode=${mainCode}`,
      });
      return;
    }
    if (href.includes(managerTag)) {
      const codes = href.split(managerTag);
      if (codes.length <= 0) {
        console.log("没有code");
        return;
      }
      const mngCode = codes[codes.length - 1];
      // 检查是否是数字字符串
      if (!chackeId(mngCode)) {
        console.log("不是数字字符串");
        return;
      }
      WeiXin.miniProgram.navigateTo({
        url: `/subPackageA/fundManager/index?mid=${mngCode}`,
      });
      return;
    }
    if (href.includes(companyTag)) {
      const codes = href.split(companyTag);
      if (codes.length <= 0) {
        console.log("没有code");
        return;
      }
      const comCode = codes[codes.length - 1];
      // 检查是否是数字字符串
      if (!chackeId(comCode)) {
        console.log("不是数字字符串");
        return;
      }
      WeiXin.miniProgram.navigateTo({
        url: `/subPackageA/fundCompany/index?cid=${comCode}`,
      });
      return;
    }
    const codes = href.split("/");
    if (codes.length <= 0) {
      console.log("没有code");
      return;
    }
    const cmtCode = codes[codes.length - 1];
    // 检查是否是数字字符串
    if (!chackeId(cmtCode)) {
      console.log("不是数字字符串");
      return;
    }
    WeiXin.miniProgram.navigateTo({
      url: `/subPackageC/superCmtInfo/index?code=${cmtCode}`,
    });
  };

  return (
    <ReactMarkdown
      ////@ts-ignore
      remarkPlugins={[RemarkMath, RemarkGfm]}
      rehypePlugins={[
        RehypeKatex,
        [
          //@ts-ignore
          RehypeHighlight,
          {
            detect: false,
            ignoreMissing: true,
          },
        ],
      ]}
      components={{
        // pre: PreCode,
        a: (aProps) => {
          const href = aProps.href;
          // console.log("aProps", aProps);
          const children = aProps.children;
          if (
            href &&
            Array.isArray(children) &&
            children.length >= 1 &&
            children[0] === "cursor"
          ) {
            return <span className={styles["custom-cursor"]}></span>;
          }
          if (href) {
            const isInternal = /^\/#/i.test(href);
            const reg = /[\u4e00-\u9fa5]/g;
            const newHref = href.replace(reg, "");
            const target = isInternal ? "_self" : aProps.target ?? "_blank";
            let cmtText = "--";
            const list = aProps.children;
            if (Array.isArray(list) && list.length >= 1) {
              cmtText = list.join(",");
            }
            // console.log("cmtText", cmtText, aProps);
            if (inMiniprogram) {
              return (
                <div
                  style={{
                    display: "inline-block",
                    color: "var(--primary)",
                    textDecoration: "underline",
                  }}
                  onClick={() => miniLinkClick(newHref)}
                >
                  {cmtText}
                </div>
              );
            }
            return (
              <a
                {...aProps}
                href={`${newHref}`}
                target={target}
                style={{ textDecoration: "underline" }}
              />
            );
          }
          return <span></span>;
        },
      }}
    >
      {content || ""}
    </ReactMarkdown>
  );
};

export default memo(MarkdownContent);
