import React, { memo } from "react";
import * as Echarts from "echarts/core";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>map<PERSON>hart,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>atter<PERSON>hart,
  <PERSON>dlestick<PERSON>hart,
  <PERSON>map<PERSON><PERSON>,
  <PERSON>plot<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LegendComponent,
  ToolboxComponent,
  DataZoomComponent,
  DataZoomInsideComponent,
  DataZoomSliderComponent,
  MarkAreaComponent,
  CalendarComponent,
  VisualMapComponent,
  MarkPointComponent,
} from "echarts/components";
import { SVGRenderer, CanvasRenderer } from "echarts/renderers";
// 标签自动布局，全局过渡动画等特性
import { LabelLayout, UniversalTransition } from "echarts/features";
import ReactEChartsCore from "echarts-for-react/lib/core";

Echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  Transform<PERSON>omponent,
  LegendComponent,
  ToolboxComponent,
  <PERSON><PERSON>hart,
  Bar<PERSON>hart,
  LabelLayout,
  UniversalTransition,
  SVGRenderer,
  TreemapChart,
  DataZoomComponent,
  DataZoomInsideComponent,
  DataZoomSliderComponent,
  PieChart,
  RadarChart,
  MarkAreaComponent,
  MarkPointComponent,
  GaugeChart,
  MapChart,
  ScatterChart,
  CanvasRenderer,
  CalendarComponent,
  HeatmapChart,
  CandlestickChart,
  BoxplotChart,
  VisualMapComponent,
  EffectScatterChart,
  FunnelChart,
]);

type BasicChartsType = {
  /**
   * Echarts配置
   */
  option: object;
  /**
   * class name
   */
  className?: string;
  /**
   * echarts div的样式
   */
  echartStyle?: React.CSSProperties;
  /**
   * 渲染模式
   */
  renderer?: "canvas" | "svg";
  /**
   * 是否不跟之前设置的 option 进行合并，默认为 false，即表示合并。
   * 如果为 true，表示所有组件都会被删除，然后根据新 option 创建所有新组件。
   */
  notMerge?: boolean;
  /**
   * 设置完 option 后是否不立即更新图表，默认为 false，即同步立即更新。
   * 如果为 true，则会在下一个 animation frame 中，才更新图表。
   */
  lazyUpdate?: boolean;
  /**
   * 当图表准备好时，将以echarts对象作为参数回调函数。
   */
  onChartReady?: (instance: any) => void;
  /**
   * binding the echarts event, will callback with the echarts event object, and the echart object as it's paramters. e.g: { 'click': this.onClick, ... }
   */
  onEvents?: Record<string, Function>;
  /**
   * 下载组件
   */
  download?: React.ReactNode;
};
const BasicCharts: React.FC<BasicChartsType> = ({
  option,
  className,
  echartStyle,
  renderer = "canvas",
  notMerge = true,
  lazyUpdate = true,
  onChartReady,
  onEvents,
  download,
}) => {
  return (
    <div>
      {download}
      <ReactEChartsCore
        className={className}
        echarts={Echarts}
        option={option}
        notMerge={notMerge}
        lazyUpdate={lazyUpdate}
        onChartReady={onChartReady}
        onEvents={onEvents}
        opts={{
          locale: "zh",
          renderer,
        }}
        style={echartStyle}
      />
    </div>
  );
};

export default memo(BasicCharts);
