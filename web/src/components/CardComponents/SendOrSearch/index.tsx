import React, { memo, HTMLAttributes } from "react";
import SendIcon from "@/app/icons/send.svg";
import AgentSearch from "@/app/icons/agent-search.svg";
import IconButton from "../IconButton";
import classNames from "classnames";
import styles from "./index.module.scss";
// @ts-nocheck

export interface SendOrSearchProps extends HTMLAttributes<HTMLElement> {
  loading?: boolean;
  disabled?: boolean;
  sendClick?: (text?: string) => void;
  searchClick?: (text?: string) => void;
}

const SendOrSearch: React.FC<SendOrSearchProps> = (props) => {
  const { loading, disabled, className, style, sendClick, searchClick } = props;

  return (
    <div className={classNames(styles.page, className)} style={style}>
      <IconButton
        icon={<SendIcon />}
        text={'发送'}
        className={styles["chat-send"]}
        type="primary"
        disabled={disabled}
        loading={loading}
        onClick={() => {
          if (disabled) return;
          sendClick && sendClick();
        }}
      />
      {/* <IconButton
        icon={<AgentSearch />}
        text="搜索"
        className={styles["search-send"]}
        disabled={disabled}
        onClick={() => {
          if (disabled) return;
          searchClick && searchClick();
        }}
      /> */}
    </div>
  );
};

export default memo(SendOrSearch);
