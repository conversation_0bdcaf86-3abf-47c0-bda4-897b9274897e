.page {
    height: 36px;
    width: 124px;
    border: solid 1px var(--primary);
    border-radius: 18px;
    box-sizing: border-box;

    display: flex;
    align-items: stretch;

    .chat-send {    
        background-color: var(--primary);
        color: white;
        font-size: 16px;
        line-height: 18px;
        font-weight: bold;
        border-radius: 16px 0px 0px 16px;
        padding: 8px 6px;
        width: 61px;
        height: 100%;
    }

    .search-send {
        background-color: white;
        color: var(--primary);
        font-size: 16px;
        line-height: 18px;
        font-weight: bold;
        border-radius: 0px 16px 16px 0px;
        padding: 8px 6px;
        width: 61px;
        height: 100%;

        .search-icon {
            width: 16px;
            height: 16px;
        }
    }
}