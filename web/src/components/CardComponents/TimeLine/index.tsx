import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
} from "./components/card";
import { ScrollArea } from "./components/scroll-area";
import { addEnCnToStr } from '@/utils/englishToChinese'
import {
  Loader2,
  Info,
  TextSearch,
  Brain,
} from "lucide-react";
import { useEffect, useState } from "react";

export interface ProcessedEvent {
  title: string;
  data: any;
}

interface TimelineProps {
  processedEvents: ProcessedEvent[];
  isLoading: boolean;
  isActive: boolean; // 是否有活跃的事件流，有的话显示加载中的状态
}

export default function TimeLine({
  processedEvents,
  isLoading,
  isActive
}: TimelineProps) {
  const [isTimelineCollapsed, setIsTimelineCollapsed] =
    useState<boolean>(false);
  const getEventIcon = (title: string, index: number) => {
    if (index === 0 && isLoading && isActive && processedEvents.length === 0) {
      return <Loader2 className="h-4 w-4 text-neutral-800 animate-spin" />;
    }
    if (title && title.toLowerCase().includes("route_query")) {
      return <TextSearch className="h-4 w-4 text-neutral-800" />;
    } else if (title && title.toLowerCase().includes("general_discussion")) {
      return <Loader2 className="h-4 w-4 text-neutral-800 animate-spin" />;
    } else if (title && title.toLowerCase().includes("truncate_history")) {
      return <Brain className="h-4 w-4 text-neutral-800" />;
    }
    // 统一序号容器样式，确保与图标尺寸一致
    return (
      <div className="w-5 h-5 flex items-center justify-center text-xs border border-neutral-300 rounded-full text-[#0A0A0A66]">
        {index + 1}
      </div>
    );
  };

  return (
    <Card className="border-none mb-4 text-[#0A0A0A99] rounded-[8px] bg-[white] max-h-96">
      <CardHeader>
        <CardDescription className="flex items-center justify-between">
          <div
            className="relative flex text-[#0A0A0A99] items-center justify-start text-sm w-full cursor-pointer gap-2"
            onClick={() => setIsTimelineCollapsed(!isTimelineCollapsed)}
          >
            Agent思考过程
            {isTimelineCollapsed ? (
              <span className="absolute right-0.5 top-0.5">展开</span>
            ) : (
              <span className="absolute right-0.5 top-0">收起</span>
            )}
          </div>
        </CardDescription>
      </CardHeader>
      {!isTimelineCollapsed && (
        <ScrollArea className="max-h-96 overflow-y-auto">
          <CardContent>
            {isLoading && isActive && processedEvents.length === 0 && (
              <div className="relative pl-8 pb-4">
                {/* 调整连接线位置使其居中 */}
                <div className="absolute left-2.5 top-6 h-[60%] w-0.5 bg-neutral-300" />
                <div className="absolute left-0 top-2 h-5 w-5 rounded-full bg-white border border-neutral-300 flex items-center justify-center">
                  <Loader2 className="h-3 w-3 text-[#0A0A0A66] animate-spin" />
                </div>
                <div>
                  <p className="text-sm relative top-[7px] text-[#0A0A0A1A] font-medium">
                    查询中....
                  </p>
                </div>
              </div>
            )}
            {processedEvents.length > 0 ? (
              <div className="space-y-0">
                {processedEvents.map((eventItem, index) => (
                  <div key={index} className="relative pl-8 pb-4">
                    {index < processedEvents.length - 1 ||
                      (isLoading && isActive && index === processedEvents.length - 1) ? (
                      // 连接线垂直居中对齐
                      <div className="absolute left-2.5 top-6 h-[calc(100%-12px)] w-0.5 bg-neutral-300" />
                    ) : null}
                    {/* 统一图标容器样式 */}
                    <div className="absolute left-0 top-2 h-5 w-5 rounded-full bg-white border border-neutral-300 flex items-center justify-center">
                      {getEventIcon(eventItem.title, index)}
                    </div>
                    <div>
                      <p className="text-sm text-neutral-600 font-medium mb-0.5">
                        {eventItem.title}
                      </p>
                      <p className="text-sm text-neutral-800 leading-relaxed">
                        {addEnCnToStr(eventItem.data)}
                      </p>
                    </div>
                  </div>
                ))}
                {isLoading && isActive && processedEvents.length > 0 && (
                  <div className="relative pl-8 pb-4">
                    <div className="absolute left-0 top-2 h-5 w-5 rounded-full bg-white border border-neutral-300 flex items-center justify-center">
                      <Loader2 className="h-3 w-3 text-[#0A0A0A66] animate-spin" />
                    </div>
                    <div>
                      <p className="text-sm relative top-[7px]  text-[#0A0A0A1A] font-medium">
                        查询中....
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ) : <></>}
          </CardContent>
        </ScrollArea>
      )}
    </Card>
  );
}
