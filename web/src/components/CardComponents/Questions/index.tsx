import right from '@/app/icons/right.png';
import React, { useState, useEffect } from 'react';
import { useStreamContext } from "@/providers/Stream";
import { Message } from "@langchain/langgraph-sdk";
import { getGuessQuestion } from '@/services/api'

// 定义后端返回数据的类型接口
interface BackendQuestion {
  category: string;
  scene: string;
  question: string;
}

export default ({ setQuickSelect, type }: { setQuickSelect: (value: string | null) => void, type: string | null }) => {
  const [activeCategory, setActiveCategory] = useState('');
  const [activeScene, setActiveScene] = useState('');
  const stream = useStreamContext();
  const { setShowGuessQuestion } = stream as any;

  // 按分类和场景分组后的数据
  const [groupedQuestions, setGroupedQuestions] = useState<Record<string, Record<string, string[]>>>({});
  const [loading, setLoading] = useState(true);

  // 从后端获取数据并按分类和场景分组
  useEffect(() => {
    const fetchQuestions = async () => {
      try {
        setLoading(true);
        const res = await getGuessQuestion();
        const data: BackendQuestion[] = res.data.data || [];

        // 按类型过滤数据（不修改原始分类和场景名称）
        const filteredData = type === 'advisoryScenarios'
          ? data.filter(item => item.category === '智能投顾')
          : data.filter(item => item.category === '智能问答');


        // 按分类和场景分组
        const grouped: Record<string, Record<string, string[]>> = {};
        filteredData.forEach(item => {
          // 初始化分类
          if (!grouped[item.category]) {
            grouped[item.category] = {};
          }

          // 初始化场景
          if (!grouped[item.category][item.scene]) {
            grouped[item.category][item.scene] = [];
          }

          // 添加问题
          grouped[item.category][item.scene].push(item.question);
        });

        setGroupedQuestions(grouped);

        // 设置默认选中第一个分类和场景
        const firstCategory = Object.keys(grouped)[0];
        if (firstCategory) {
          setActiveCategory(firstCategory);
          const firstScene = Object.keys(grouped[firstCategory])[0];
          if (firstScene) {
            setActiveScene(firstScene);
          }
        }
      } catch (error) {
        console.error('获取问题数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchQuestions();
  }, [type]);

  // 当分组数据变化时更新默认选中项
  useEffect(() => {
    if (Object.keys(groupedQuestions).length > 0 && !activeCategory) {
      const firstCategory = Object.keys(groupedQuestions)[0];
      setActiveCategory(firstCategory);

      const firstScene = Object.keys(groupedQuestions[firstCategory])[0];
      if (firstScene) {
        setActiveScene(firstScene);
      }
    }
  }, [groupedQuestions]);
  // 提交消息处理函数
  const submitMessage = (context: string) => {
    if (!stream || !context) return;

    setShowGuessQuestion(false);
    const newMessage: Message = { type: "human", content: context };

    stream.submit(
      {
        messages: [newMessage],
        //@ts-ignore
        current_step: null,
        current_step_detail: "",
        next_node: null,
        next_state: null,
      },
      {
        streamMode: ["values"],
        optimisticValues: (prev: any) => ({
          ...prev,
          context,
          messages: [
            ...(prev.messages ?? []),
            newMessage,
          ],
        }),
      },
    );
  };

  // 加载状态显示
  if (loading) {
    return (
      <div className="mt-4 p-4 bg-white rounded-lg shadow-sm">
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#BF9267FF]"></div>
        </div>
      </div>
    );
  }

  // 没有数据时显示
  const hasData = Object.keys(groupedQuestions).length > 0;
  if (!hasData) {
    return (
      <div className="mt-4 p-4 bg-white rounded-lg shadow-sm">
        <p className="text-center text-gray-500 py-4">暂无相关问题</p>
      </div>
    );
  }

  // 确保当前选中的分类和场景有效
  if (!activeCategory || !groupedQuestions[activeCategory] || !activeScene || !groupedQuestions[activeCategory][activeScene]) {
    return null;
  }
  return (
    <div className="mt-4">
      <div
        style={{
          borderRadius: '6px',
          background: 'linear-gradient(180deg, rgba(191,146,103,0.2) 0%, white 15%, white 100%)'
        }}
        className="p-4 bg-white rounded-lg shadow-sm"
      >
        <h2 style={{ fontFamily: "YouSheBiaoTiHei, sans-serif" }} className="text-lg text-black mb-4">
          {Object.keys(groupedQuestions)}
        </h2>

        {/* 场景选择 */}
        <div className="flex flex-wrap gap-2 mb-4">
          {Object.keys(groupedQuestions[activeCategory]).map((scene) => (
            <button
              key={scene}
              onClick={() => setActiveScene(scene)}
              style={{
                border: activeScene === scene ? '1px solid #BF9267FF' : '1px solid #0A0A0A1A',
                color: activeScene === scene ? '#BF9267FF' : '#0A0A0A99',
                backgroundColor: activeScene === scene ? '#BF92671A' : 'white'
              }}
              className="px-[12px] py-[6px] rounded-full text-sm transition-all duration-200 hover:shadow-sm"
              aria-pressed={activeScene === scene}
            >
              {scene}
            </button>
          ))}
        </div>

        {/* 问题列表 */}
        <div className="bg-white transition-all duration-300">
          <ul className="space-y-2 text-[#0A0A0AFF]">
            {groupedQuestions[activeCategory][activeScene].map((item, index) => (
              <li
                key={index}
                onClick={() => {
                  submitMessage(item);
                  setQuickSelect(null);
                }}
                className="flex items-center px-4 py-3 rounded-[8px] mb-2 bg-[#29304B08] text-[#0A0A0A99] cursor-pointer transition-colors hover:bg-[#29304B12]"
              >
                <span className="flex-1">{item}</span>
                <img
                  src={right.src}
                  alt="选择此项"
                  className="w-4 h-4 ml-2 flex-shrink-0"
                />
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}
