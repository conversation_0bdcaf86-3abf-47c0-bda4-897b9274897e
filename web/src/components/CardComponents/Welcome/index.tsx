import React, { useEffect, useState } from 'react';
import { Segmented } from 'antd-mobile'
// @ts-nocheck

const Welcome = () => {
  const [selected, setSelected] = useState<string | null>('智能问答')
  const allText: any = {
    '智能问答': '快速获取客户信息、产品要素、知识库解答',
    '智能知识库': '整合多源知识库，智能问答精准溯源',
    '智能投顾': '智能生成资产配置方案及推介话术',
  }
  const onChange = (val: string) => {
    setSelected(val)
  }

  return (
    <div className="pt-4 bg-gradient-to-b from-[rgb(248,231,206)] to-[#F7F7F7FF] to-20%">
      <div className="p-4 pt-2 rounded-lg">
        <div className="mb-4">
          <p className="font-bold text-20">
            我是您的智能理财助手，专注服务信托理财经理
          </p>
        </div>
        {/* <div className='p-4 bg-[#BF92671A] text-12 rounded-[8px] text-[#0A0A0A99] mb-4'>
          <img
            src={reference.src}
            alt="参考图标"
            width={20}
            height={20}
            className='inline-block mr-2'
          />
          可查客户资产/产品信息、生成财富规划、智能匹配方案，助您高效服务客户。试试输入问题或点击下方快捷工具吧！
        </div> */}
        <div className="pb-3 bg-white mt-2 rounded-[6px]">
          <div className='flex justify-between items-center'>
            <h2 className="min-w-[80px] pt-4 mb-4 text-lg  text-20 text-black text-left ml-4" style={{ fontFamily: "YouSheBiaoTiHei, sans-serif" }}>
              <span className='relative top-[-5px] right-1'>.</span>
              核心功能
              <span className='relative top-[-5px] left-1'>.</span>
            </h2>
            <div className='mr-2'>
              <Segmented onChange={onChange} size='small' options={['智能问答', '智能知识库', '智能投顾']} />
            </div>
          </div>
          <div className="rounded-[8px] bg-[#f7f7f7] mx-2 p-4 py-2 hover:shadow-md transition-all"  >
            <p className="text-md text-[#bf9267] ">
              {allText[selected || '智能问答']}
            </p>
          </div>
        </div>
        {/* <Questions type='advisoryScenarios'></Questions> */}
      </div >
    </div >
  );
};

export default Welcome;