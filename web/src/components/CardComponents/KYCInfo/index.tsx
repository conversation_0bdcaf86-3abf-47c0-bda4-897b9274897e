import { useState } from 'react';

// 定义后端返回数据的类型接口
interface CustomerData {
  customer_id: string;
  customer_name: string;
  mobile_phone?: string;
  gender?: string;
  customer_age: number;
  birthday?: string;
  email?: string;
  address?: string;
  id_status?: string;
  bank_card_count?: number;
  prove_status?: string;
  prove_expires_date?: string;
  risk_level_status?: string;
  risk_level?: string;
  risk_level_name?: string;
  risk_level_expires_date?: string;
  tax_id_status?: string;
  tax_id?: string;
  tax_id_name?: string;
  tax_id_expires_date?: string;
  customer_status?: string;
  marital_status?: string;
  nation?: string;
  highest_education?: string;
  last_login_time?: string;
  holding_amount?: number;
  total_asset?: number;
  total_income?: number;
}

// 证件状态映射
const idStatusMap = {
  '0': '未实名',
  '1': '已实名',
  '2': '待审核'
};

// 合格投资者状态映射
const proveStatusMap = {
  '0': '否',
  '1': '是'
};

// 风险测评状态映射
const riskLevelStatusMap = {
  '0': '否',
  '1': '是'
};

// 税收居民身份映射
const taxIdStatusMap = {
  '0': '否',
  '1': '是'
};

// 税收居民身份状态映射
const taxIdMap = {
  '0': '未申报',
  '1': '已申报'
};

// 客户状态映射
const customerStatusMap = {
  '0': '注册',
  '1': '实名',
  '2': '意向',
  '3': '流失',
  '4': '休眠',
  '5': '持仓'
};

const KYCInfo = ({ data }: { data: CustomerData }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  // 动态生成KYC数据配置
  const kycConfig = [
    { label: '客户号', field: 'customer_id' },
    { label: '性别', field: 'gender' },
    { label: '年龄', field: 'customer_age' },
    { label: '出生日期', field: 'birthday' },
    { label: '手机号', field: 'mobile_phone' },
    { label: '邮箱', field: 'email' },
    { label: '地址', field: 'address' },
    { label: '证件状态', field: 'id_status', map: idStatusMap },
    { label: '银行卡数量', field: 'bank_card_count' },
    { label: '合格投资者', field: 'prove_status', map: proveStatusMap },
    { label: '合格投资者失效日', field: 'prove_expires_date' },
    { label: '风险测评', field: 'risk_level_status', map: riskLevelStatusMap },
    { label: '风测等级', field: 'risk_level' },
    { label: '风测等级名称', field: 'risk_level_name' },
    { label: '风测失效日', field: 'risk_level_expires_date' },
    { label: '税收居民身份', field: 'tax_id_status', map: taxIdStatusMap },
    { label: '税收居民身份状态', field: 'tax_id', map: taxIdMap },
    { label: '税收居民身份状态名称', field: 'tax_id_name' },
    { label: '税收居民身份失效日', field: 'tax_id_expires_date' },
    { label: '客户状态', field: 'customer_status', map: customerStatusMap },
    { label: '婚姻状况', field: 'marital_status' },
    { label: '民族', field: 'nation' },
    { label: '最高学历', field: 'highest_education' },
    { label: '最后登录时间', field: 'last_login_time' },
    { label: '持仓金额', field: 'holding_amount', format: (value: number) => `¥${value.toLocaleString()}` },
    { label: '总资产', field: 'total_asset', format: (value: number) => `¥${value.toLocaleString()}` },
    { label: '累计收益', field: 'total_income', format: (value: number) => `¥${value.toLocaleString()}` },
  ];

  // 根据后端实际返回的数据过滤并生成要显示的KYC数据
  const kycData = kycConfig
    // 过滤掉后端没有返回的字段（null或undefined）
    .filter(item => data[item.field as keyof CustomerData] !== null && data[item.field as keyof CustomerData] !== undefined)
    // 处理每个字段的值，应用映射和格式化
    .map(item => {
      let value = data[item.field as keyof CustomerData];

      // 如果有映射表，使用映射后的值
      if (item.map && typeof value === 'string' && item.map[value as keyof typeof item.map]) {
        value = item.map[value as keyof typeof item.map];
      }

      // 如果有格式化函数，应用格式化
      if (item.format && value !== undefined) {
        value = item.format(value);
      }
      return {
        label: item.label,
        value
      };
    });

  if (isCollapsed) {
    return (
      <div className="bg-white rounded-lg p-4 shadow-[0_2px_8px_rgba(0,0,0,0.1)] mb-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-bold text-[#333] m-0 text-center flex-1">KYC信息</h2>
        </div>
        <div className="flex flex-col gap-3 bg-[#fdfbfaFF] rounded-lg p-3 px-[15px] relative">
          <div className="flex items-center font-normal text-sm text-[#0A0A0A] text-left">
            <span className="text-[#BF9267FF] mr-3.5 text-base">•</span>
            <span className="mr-2">姓名:</span>
            <span>{data.customer_name || ''}</span>
          </div>
          <button
            className="absolute right-2.5 top-1.5 bg-none border-none text-[#8B5A2B] text-sm cursor-pointer px-2 py-1 transition-all duration-300 ease-in-out hover:underline hover:scale-[1.05] active:text-[#6B4A1B] active:scale-95"
            onClick={() => setIsCollapsed(false)}
          >
            展开
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-4 shadow-[0_2px_8px_rgba(0,0,0,0.1)] mb-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold text-[#333] m-0 text-center flex-1">KYC信息</h2>
      </div>
      <div className="flex flex-col gap-3 bg-[#fdfbfaFF] rounded-lg p-3 px-[15px] relative">
        <div className="flex items-center font-normal text-sm text-[#0A0A0A] text-left">
          <span className="text-[#BF9267FF] mr-3.5 text-base">•</span>
          <span className="mr-2">姓名:</span>
          <span>{data.customer_name || ''}</span>
        </div>
        {kycData.map((item, index) => (
          <div key={index} className="flex items-center font-normal text-sm text-[#0A0A0A] text-left">
            <span className="text-[#BF9267FF] mr-3.5 text-base">•</span>
            <span className="mr-2">{item.label}:</span>
            <span>{item.value || '--'}</span>
          </div>
        ))}
        <button
          className="absolute right-2.5 top-1.5 bg-none border-none text-[#8B5A2B] text-sm cursor-pointer px-2 py-1 transition-all duration-300 ease-in-out hover:underline hover:scale-[1.05] active:text-[#6B4A1B] active:scale-95"
          onClick={() => setIsCollapsed(true)}
        >
          收起
        </button>
      </div>
    </div>
  );
};

export default KYCInfo;
