import UserProfile from "../Components/UserProfile/index"
import { formatInternationalNumber } from '@/utils/renderFormatter'
// @ts-nocheck

export default ({ data }: { data: any }) => {

  let userData = {
    customer_name: data.customer_name,
    customer_id: data.customer_id,
    phone_number: data.phone_number,
    customer_age: data.customer_age,
    invest_seniority: data.invest_seniority,
    holding_amount: data.holding_amount,
  }
  return <div className="bg-white mb-4">
    <UserProfile data={userData}>
      <div>
        <div className="flex mt-3.5">
          <div className="text-base font-medium">当前总资产(元)</div>
          <div className="text-sm text-[#aaa] leading-6 pl-2">
            {formatInternationalNumber(data.total_asset)}
          </div>
        </div>
        <div className="flex mt-3.5">
          <div className="text-base font-medium">历史累计收益(元)</div>
          <div className="text-sm text-[#aaa] leading-6 pl-2">
            {formatInternationalNumber(data.total_income)}
          </div>
        </div>
      </div>
    </UserProfile>
    <div className="p-4">
      <div className="text-[#0A0A0A] leading-[50px] border-b border-[#eee] flex justify-between">
        风险等级：
        <span className="text-[#aaa]">{data.risk_level_name}</span>
      </div>
      <div className="text-[#0A0A0A] leading-[50px] border-b border-[#eee] flex justify-between">
        风测失效日期:
        <span className="text-[#aaa]">{data.risk_level_expires_date}</span>
      </div>
      <div className="text-[#0A0A0A] leading-[50px] border-b border-[#eee] flex items-center justify-between">
        合格投资者状态：
        <span className="text-[#aaa] text-sm">
          {data.prove_status ? '已认证' : '未认证'}
          （有效期至{data.prove_expires_date}）</span>
      </div>
      <div className="text-[#0A0A0A] leading-[50px] border-b border-[#eee] flex justify-between">
        税收居民身份状态：
        <span className="text-[#aaa]">{data.tax_id ? '申报' : '未申报'}</span>
      </div>
    </div>
  </div>
}