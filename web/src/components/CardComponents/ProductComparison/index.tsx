import { tablePercentFixedRender } from '@/utils/renderFormatter'
import { Table } from 'antd'
import { useState, memo, useMemo, useEffect } from "react"
import BasicCharts from '../BasicCharts'
import { subMonths, subYears, parseISO, isAfter, isBefore, isSameDay, format } from 'date-fns';
// @ts-nocheck
// 替代 isSameOrAfter
const isSameOrAfter = (date: any, dateToCompare: any) =>
  isSameDay(date, dateToCompare) || isAfter(date, dateToCompare);

// 替代 isSameOrBefore
const isSameOrBefore = (date: any, dateToCompare: any) =>
  isSameDay(date, dateToCompare) || isBefore(date, dateToCompare);

// 定义产品数据类型接口
interface ProductData {
  product_id: string;
  product_name: string;
  setup_date: string;
  risk_level: string;
  performance_benchmark: string;
  minimum_investment?: number;
  redemption_schedule: string;
  nets: Array<{
    date: string;
    value: number;
  }>;
}

// 辅助函数：从颜色值中提取RGB成分
const getRGBFromColor = (color: string): [number, number, number] => {
  // 处理十六进制颜色 (如 #RRGGBB 或 #RRGGBBAA)
  if (color.startsWith('#')) {
    const hex = color.slice(1);
    let r, g, b;

    if (hex.length === 6 || hex.length === 8) {
      r = parseInt(hex.slice(0, 2), 16);
      g = parseInt(hex.slice(2, 4), 16);
      b = parseInt(hex.slice(4, 6), 16);
    } else if (hex.length === 3 || hex.length === 4) {
      // 处理简写形式 (如 #RGB 或 #RGBA)
      r = parseInt(hex[0] + hex[0], 16);
      g = parseInt(hex[1] + hex[1], 16);
      b = parseInt(hex[2] + hex[2], 16);
    } else {
      // 默认返回黑色
      return [0, 0, 0];
    }

    return [r, g, b];
  }

  // 这里可以扩展处理其他颜色格式如 rgb()、rgba() 等
  // 简化处理，默认返回黑色
  return [0, 0, 0];
};

const ProductComparison = ({ data }: { data?: ProductData[] }) => {
  const [selectedRange, setSelectedRange] = useState('近1年');
  const [filteredData, setFilteredData] = useState<ProductData[] | undefined>(data);

  // 当原始数据或选中的时间范围变化时，重新筛选数据
  useEffect(() => {
    if (!data || data.length === 0) {
      setFilteredData([]);
      return;
    }

    // 根据选中的时间范围计算起始日期
    let startDate: Date;
    const now = new Date();

    switch (selectedRange) {
      case '近1月':
        startDate = subMonths(now, 1);
        break;
      case '近3月':
        startDate = subMonths(now, 3);
        break;
      case '近6月':
        startDate = subMonths(now, 6);
        break;
      case '近1年':
        startDate = subYears(now, 1);
        break;
      case '成立以来':
        // 对于"成立以来"，使用产品成立日期作为起始日期
        startDate = parseISO(data[0].setup_date);
        break;
      default:
        startDate = subYears(now, 1);
    }

    // 筛选每个产品的净值数据
    const filtered = data.map(product => ({
      ...product,
      nets: product.nets?.filter(item => {
        const itemDate = parseISO(item.date);
        // 只保留在时间范围内的数据
        return isSameOrAfter(itemDate, startDate) && isSameOrBefore(itemDate, now);
      })
    }));

    setFilteredData(filtered);
  }, [data, selectedRange]);

  const handleTimeRangeChange = (range: string) => {
    setSelectedRange(range);
  };

  const columns: any = [
    {
      title: '产品名称',
      dataIndex: 'field',
      key: 'field',
      width: 120,
      fixed: 'left',
    },
    ...(data?.map((product: any) => ({
      title: product.product_name,
      dataIndex: product.product_id,
      key: product.product_id,
      width: 180,
      render: (_: any, record: any) => {
        switch (record.field) {
          case '成立日期':
            return product.setup_date || '--';
          case '风险等级':
            return product.risk_level || '--';
          case '业绩基准':
            return product.performance_benchmark || '--';
          case '起投金额':
            return `¥${product.min_subscription_amount?.toLocaleString() || '-'}`;
          case '开放日':
            return product.open_date || '--';

          default:
            return '-';
        }
      }
    })) || [])
  ];

  const tableData = [
    { field: '成立日期' },
    { field: '风险等级' },
    { field: '业绩基准' },
    { field: '起投金额' },
    { field: '开放日' },
    // { field: '费用信息' }
  ];

  // 使用筛选后的数据作为图表数据源
  const chartOption = useMemo(() => {
    const colors = ['#29304BFF', '#BF9267FF']
    if (!filteredData || filteredData.length === 0) return null;

    // 检查是否有产品有净值数据
    const hasData = filteredData.some(product => product.nets && product.nets.length > 0);
    if (!hasData) return null;

    const series = filteredData.map((item, index) => {
      // 获取当前产品的主色
      const mainColor = colors[index % colors.length];
      // 提取RGB值
      const [r, g, b] = getRGBFromColor(mainColor);
      // 创建基于主色的半透明色和完全透明色
      const transparentColor = `rgba(${r}, ${g}, ${b}, 0.3)`;
      const fullyTransparentColor = `rgba(${r}, ${g}, ${b}, 0)`;

      return {
        name: item.product_name,
        type: 'line',
        symbol: 'none',
        lineStyle: {
          color: mainColor,
          width: 1
        },
        itemStyle: { color: mainColor },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: transparentColor // 使用主色的半透明版本
            }, {
              offset: 1,
              color: fullyTransparentColor // 主色的完全透明版本
            }]
          }
        },
        data: item.nets.map((netItem) => ({
          value: netItem.value || 0,
          date: netItem.date
        })),
      }
    })

    // 获取有数据的第一个产品的日期作为x轴数据
    const firstValidProduct = filteredData.find(p => p.nets.length > 0);
    if (!firstValidProduct) return null;

    const option = {
      "legend": {
        "bottom": 0,
        "icon": "path://M0,5 L0,15 Q0,20 5,20 L15,20 Q20,20 20,15 L20,5 Q20,0 15,0 L5,0 Q0,0 0,5 Z",
        "itemWidth": 12,
        "itemHeight": 12,
        "itemGap": 15
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
        },
        borderWidth: 1,
        formatter: (params: any) => {
          if (!params || params.length === 0) return '';
          const dateValue = params[0].value?.date || params[0].axisValue;
          // 使用date-fns的format替代moment的format
          const dateStr = format(parseISO(dateValue), 'yyyy-MM-dd');
          let html = `<div style="margin: 0 0 5px;font-weight:bold">${dateStr}</div>`;
          params.forEach((item: any) => {
            const color = item.color
            const value = item.value?.value || item.value;
            html += `
              <div style="display:flex;align-items:center;margin:5px 0">
                <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background:${color};margin-right:5px"></span>
                <span style="flex:1">${item.seriesName}:</span>
                <span style="margin-left:10px;font-weight:bold">${value?.toFixed(4) || '0.0000'}</span>
              </div>
            `;
          });
          return html;
        },
      },
      grid: {
        left: 0,
        right: 0,
        top: 10,
        bottom: 30,
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        axisTick: {
          show: false,
        },
        data: firstValidProduct.nets.map(item => item.date),
        axisLabel: {
          formatter: (value: string) => {
            // 使用date-fns解析并格式化日期，只显示月-日
            const date = parseISO(value);
            return `${date.getMonth() + 1}-${date.getDate()}`;
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          position: 'left',
          min: 'dataMin',
          max: 'dataMax',
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          },
          axisLabel: {
            formatter: (value: number) => {
              return `${value.toFixed(2)}`
            },
          },
        },
      ],
      series: series,
    }
    return option
  }, [filteredData])


  return (
    <div>
      <div className="bg-white p-4 mb-4 rounded-lg">
        <div className="font-medium text-base text-[#0A0A0A] text-left mb-4.5 h-[17px]">
          产品对比信息
        </div>
        <div>
          <Table
            scroll={{ x: 'max-content' }}
            bordered
            columns={columns}
            dataSource={tableData}
            pagination={false}
            rowKey="field"
          />
        </div>
        <div className="flex justify-between mt-4">
          <div className="font-medium text-sm text-[#0A0A0A] text-left mb-3 h-5">
            净值走势
          </div>
          <div className="font-normal text-sm text-[#0A0A0A66] leading-5 text-right">
            *净值包含未计提的浮动管理费（如有）
          </div>
        </div>
        <div>
          {chartOption && (
            <BasicCharts
              option={chartOption}
              echartStyle={{ minHeight: 200, height: 360 }}
              onChartReady={() => {
                console.info(`Line Vertical Bar Chart is ready`)
              }}
            ></BasicCharts>
          )}
          {!chartOption && filteredData && filteredData.length > 0 && (
            <div style={{ height: 360 }} className='flex items-center justify-center'>该时间范围内没有数据</div>
          )}
        </div>
        {chartOption &&
          <div>
            <div className="flex gap-2 mb-3">
              {['近1月', '近3月', '近6月', '近1年', '成立以来'].map((item) => (
                <button
                  key={item}
                  className={`px-4 py-2 rounded-4 border-none text-sm font-normal text-[#8C8C8C] bg-transparent cursor-pointer transition-all duration-200 ${item === selectedRange ? 'bg-[#BF92671A] text-[#BF9267FF] font-medium' : ''
                    }`}
                  onClick={() => handleTimeRangeChange(item)}
                >
                  {item}
                </button>
              ))}
            </div>
            <div className="text-[#0A0A0A66] text-12">
              温馨提示：过往业绩不代表本信托计划投资的未来表现或实际运作效果，不等于本信托计划实际收益，不作为我们对本信托计划未来业绩的承诺与保证，投资须谨慎。
            </div>
          </div>}
      </div>
    </div>
  )
}

export default memo(ProductComparison);
