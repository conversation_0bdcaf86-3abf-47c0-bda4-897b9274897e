import Reference from "@/app/icons/reference.png";
import { Divider } from 'antd-mobile'
// @ts-nocheck


const KnowledgeBase = ({ data }: { data: any }) => {
  return (
    <div className="bg-white p-2 rounded-lg">
      <div className="flex h-[30px] mx-3">
        <img className="relative top-1 w-4 h-3" src={Reference.src}></img>
        <div className="w-10 mx-1 font-normal text-sm text-[rgba(10,10,10,0.6)] leading-5 text-left">
          来源
        </div>
        <div className="w-[90%] relative top-[-14px]">
          <Divider />
        </div>
      </div>
      {(data || []).map((document: any) => (
        <div key={document.name + Math.random()} className="flex items-center mb-2 bg-[rgba(10,10,10,0.03)] rounded-lg border border-[rgba(10,10,10,0.1)] p-2 px-3">
          <div className="flex-1">
            <div className="font-medium text-sm text-[#0A0A0A] leading-[18px] text-left">
              {
                document.url ?
                  <span onClick={() => {
                    window.open(document.url, '_blank');
                  }} className="text-[#587DABFF] text-sm font-normal pl-1 mb-1 break-all cursor-pointer">{document.name}</span> :
                  <span>{document.name || '--'}</span>
              }
            </div>
            <div className="font-normal text-sm text-[rgba(10,10,10,0.4)] leading-4 text-left pt-2 pb-2">
              <span>由 {document.uploader || '--'}</span>
              <span>{document.date || '--'} 上传</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default KnowledgeBase;