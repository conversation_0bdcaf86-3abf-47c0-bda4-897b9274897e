"use client";
import React, { useEffect, useState } from 'react';
import { CalendarPicker } from 'antd-mobile';
import AddBenchmark from '../AddBenchmark';
import { ArrowRight } from 'lucide-react'
import { format } from 'date-fns';
const ParamsSetting = ({ onChange }: { onChange: any }) => {
  const [date, setDate] = useState<any>(null);
  const [benchmark, setBenchmark] = useState<string>(null);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    onChange({
      date,
      benchmark
    })
  }, [date, benchmark])

  return (
    <div className="p-4">
      <div className='flex items-center mb-4'>
        <div className='mr-2 h-[10px] w-[3px] rounded-b-sm bg-[#29304BFF]'></div>
        <div className='text-16 font-bold'>参数选择</div>
      </div>
      <div onClick={() => setVisible(true)} className="flex items-center mb-4">
        <div className="text-14 text-gray-800 w-full">回测时间：
          {date ?
            `${format(new Date(date[0]), 'yyyy-MM-dd')}
            ~
            ${format(new Date(date[1]), 'yyyy-MM-dd')}`
            :
            '请选择日期范围'}
        </div>
        <div className="flex-1" >
          <CalendarPicker
            visible={visible}
            value={date}
            selectionMode='range'
            onClose={() => setVisible(false)}
            onMaskClick={() => setVisible(false)}
            onChange={val => {
              setDate(val)
            }}
          />
        </div>
      </div>

      <AddBenchmark onSelect={(val) => {
        setBenchmark(val)
      }}>
        <div className="flex items-center mb-4">
          <div className="w-full text-14 text-gray-800">基准选择：{!benchmark && <span>请选择--</span>}</div>
          <div className="flex justify-between items-center">
            {benchmark ? <div className=" mt-2 text-xs text-gray-500">
              {benchmark?.productname}
            </div> : <div></div>}
            <div>
              <ArrowRight size={12}></ArrowRight>
            </div>
          </div>
        </div>
      </AddBenchmark>
    </div >
  );
}


export default ParamsSetting