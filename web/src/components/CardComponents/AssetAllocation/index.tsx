import { useState, useEffect } from 'react'
import Turn from "@/app/icons/turn.png";
import { Input, message, Popconfirm } from 'antd';
import { useStreamContext } from "@/providers/Stream";
import { v4 as uuidv4 } from "uuid";
import { useThreadActivities } from '@/contexts/ThreadActivitiesContext'
import { getSimilarFunds, getAiConfig } from '@/services/api'
import AddProductPage from '../AddProduct';
import ParamsSetting from './ParamsSetting'
interface SimilarFund {
  proCode: string;
  proName: string;
  proRiskCode: string;
  proRiskName: string;
  type: string;
  code?: string;
  riskLevel?: string;
}

interface Product {
  proCode: string;
  proName: string;
  proRiskCode: string;
  proRiskName: string;
  weight: number;
  weightAmount: number;
}

interface SecondaryCategory {
  pfcatCode: string;
  pfcatName: string;
  products: Product[];
  totalAllocationRatio: number;
  totalAllocationAmount: number;
}

interface AssetCategory {
  assetcatCode: string;
  assetcatName: string;
  secondaryCategories: SecondaryCategory[];
  totalAllocationRatio: number;
  totalAllocationAmount: number;
}

interface OriginalDataStructure {
  amount: number;
  products: Array<{
    proCode: string;
    proName: string;
    assetcatCode: string;
    assetcatName: string;
    pfcatCode: string;
    pfcatName: string;
    proRiskCode: string;
    proRiskName: string;
    weight: number;
    weightAmount: number;
  }>;
  completed?: boolean;
}

const CATEGORY_MAP = {
  'DCM': '固定类',
  'EQ': '权益类',
  'MM': '混合类',
  'OTH': '商品及衍生品'
};

interface AssetAllocationProps {
  setNewMessages: any;
  data: OriginalDataStructure;
}
function adjustWeights(items: Array<{ weight: number }>): Array<{ weight: number }> {
  // 计算所有weight的总和
  const totalWeight = items.reduce((sum, item) => sum + (item.weight || 0), 0);

  // 根据总和决定是否乘以100
  const multiplier = totalWeight > 1 ? 1 : 100;

  // 返回新数组，避免修改原数组
  return items.map(item => ({
    ...item,
    weight: item.weight * multiplier
  }));
}

const AssetAllocation = ({ setNewMessages, data }: AssetAllocationProps) => {
  const [categories, setCategories] = useState<AssetCategory[]>([]);
  const [totalAllocationRatio, setTotalAllocationRatio] = useState<number>(0);
  const [isSubmittable, setIsSubmittable] = useState<boolean>(true);
  const [params, setParams] = useState<any>(null)
  const [isReplacing, setIsReplacing] = useState<Record<string, boolean>>({});
  const [messageApi, contextHolder] = message.useMessage();
  const thread: any = useStreamContext();
  const { setinputDisabled } = useThreadActivities();
  const { completed } = data;

  const transformToInternal = (sourceData: OriginalDataStructure): AssetCategory[] => {
    // 初始化四个大类
    const groupedByPrimary: Record<string, {
      assetcatName: string;
      secondaryCategories: Record<string, SecondaryCategory>;
      totalRatio: number;
      totalAmount: number;
    }> = {
      'DCM': { assetcatName: '固定类', secondaryCategories: {}, totalRatio: 0, totalAmount: 0 },
      'EQ': { assetcatName: '权益类', secondaryCategories: {}, totalRatio: 0, totalAmount: 0 },
      'MM': { assetcatName: '混合类', secondaryCategories: {}, totalRatio: 0, totalAmount: 0 },
      'OTH': { assetcatName: '商品及衍生品', secondaryCategories: {}, totalRatio: 0, totalAmount: 0 }
    };

    adjustWeights(sourceData.products).forEach(product => {
      const primaryKey = product.assetcatCode;
      const secondaryKey = product.pfcatCode;
      const secondaryName = product.pfcatName;

      // 确保分类名称使用预设值
      const assetcatName = CATEGORY_MAP[primaryKey as keyof typeof CATEGORY_MAP] || product.assetcatName;

      // 确保大类已存在（即使没有数据也会保留）
      if (!groupedByPrimary[primaryKey]) {
        groupedByPrimary[primaryKey] = {
          assetcatName,
          secondaryCategories: {},
          totalRatio: 0,
          totalAmount: 0
        };
      }
      const primaryGroup = groupedByPrimary[primaryKey];

      if (!primaryGroup.secondaryCategories[secondaryKey]) {
        primaryGroup.secondaryCategories[secondaryKey] = {
          pfcatCode: secondaryKey,
          pfcatName: secondaryName,
          products: [],
          totalAllocationRatio: 0,
          totalAllocationAmount: 0
        };
      }
      const secondaryGroup = primaryGroup.secondaryCategories[secondaryKey];

      const productItem: Product = {
        proCode: product.proCode,
        proName: product.proName,
        proRiskCode: product.proRiskCode,
        proRiskName: product.proRiskName,
        weight: product.weight,
        weightAmount: product.weightAmount
      };

      secondaryGroup.products.push(productItem);
      secondaryGroup.totalAllocationRatio += product.weight;
      secondaryGroup.totalAllocationAmount += product.weightAmount;

      primaryGroup.totalRatio += product.weight;
      primaryGroup.totalAmount += product.weightAmount;
    });

    return Object.entries(groupedByPrimary).map(([assetcatCode, primaryGroup]) => ({
      assetcatCode: assetcatCode,
      assetcatName: primaryGroup.assetcatName,
      secondaryCategories: Object.values(primaryGroup.secondaryCategories),
      totalAllocationRatio: primaryGroup.totalRatio,
      totalAllocationAmount: primaryGroup.totalAmount
    }));
  };

  const transformToOriginal = (internalData: AssetCategory[]): OriginalDataStructure => {
    const allProducts = internalData.flatMap(primary =>
      primary.secondaryCategories.flatMap(secondary =>
        secondary.products.map(product => ({
          proCode: product.proCode,
          proName: product.proName,
          assetcatCode: primary.assetcatCode,
          assetcatName: primary.assetcatName,
          pfcatCode: secondary.pfcatCode,
          pfcatName: secondary.pfcatName,
          proRiskCode: product.proRiskCode,
          proRiskName: product.proRiskName,
          weight: parseFloat(Number(product.weight).toFixed(2)),
          weightAmount: parseFloat(Number(product.weightAmount).toFixed(2))
        }))
      )
    );

    const totalAmount = allProducts.reduce((sum, p) => sum + (p.weightAmount || 0), 0);
    return {
      amount: parseFloat(Number(totalAmount).toFixed(2)),
      products: allProducts,
      completed: data?.completed
    };
  };

  useEffect(() => {
    if (data?.products?.length) {
      const transformed = transformToInternal(data);
      setCategories(transformed);
    }
  }, [data]);

  useEffect(() => {
    if (categories.length === 0) return;

    const total = categories.reduce((primarySum, primary) => {
      const secondarySum = primary.secondaryCategories.reduce((secSum, secondary) => {
        return secSum + secondary.products.reduce((pSum, p) => pSum + p.weight, 0);
      }, 0);
      return primarySum + secondarySum;
    }, 0);

    setTotalAllocationRatio(total);
    setIsSubmittable(Math.abs(total - 100) < 0.01);
  }, [categories]);

  const showRatioError = () => {
    messageApi.error(`比例总和必须为100%，当前为${Number(totalAllocationRatio).toFixed(2)}%`);
  };

  const validateNumberInput = (value: string): string => {
    if (/^(\d+(\.\d{0,2})?|\.\d{1,2})?$/.test(value)) {
      return value;
    }
    return value.slice(0, -1);
  };

  const handleProductChange = (
    categoryId: string,
    secondaryId: string,
    productId: string,
    field: 'weight' | 'weightAmount',
    value: string
  ) => {
    const processedValue = validateNumberInput(value);
    const numericValue = parseFloat(processedValue) || 0;

    setCategories(prev => prev.map(primary => {
      // 找到当前产品所在的一级分类
      if (primary.assetcatCode !== categoryId) return primary;

      // 计算一级分类总占比和总金额
      const primaryTotalRatio = primary.totalAllocationRatio;
      const primaryTotalAmount = primary.totalAllocationAmount;

      // 计算一级分类下除当前产品外所有其他产品的总占比
      const otherProductsTotalRatio = primary.secondaryCategories
        .flatMap(secondary => secondary.products)
        .filter(p => p.proCode !== productId)
        .reduce((sum, p) => sum + p.weight, 0);

      // 计算当前产品可分配的最大占比（基于一级分类总占比）
      const maxAllowedRatio = primaryTotalRatio - otherProductsTotalRatio;

      // 计算一级分类下除当前产品外所有其他产品的总金额
      const otherProductsTotalAmount = primary.secondaryCategories
        .flatMap(secondary => secondary.products)
        .filter(p => p.proCode !== productId)
        .reduce((sum, p) => sum + p.weightAmount, 0);

      // 计算当前产品可分配的最大金额（基于一级分类总金额）
      const maxAllowedAmount = primaryTotalAmount - otherProductsTotalAmount;

      // 更新二级分类
      const updatedSecondaries = primary.secondaryCategories.map(secondary => {
        if (secondary.pfcatCode !== secondaryId) return secondary;

        // 更新产品信息
        const updatedProducts = secondary.products.map(p => {
          if (p.proCode !== productId) return p; // 不修改其他产品

          if (field === 'weight') {
            // 占比调整逻辑：如果超过最大允许值（基于一级分类），则设为最大值
            const validRatio = Math.max(0, Math.min(numericValue, maxAllowedRatio));
            // 根据占比计算金额（基于一级分类总金额）
            const calculatedAmount = (primaryTotalAmount * validRatio) / 100;
            return { ...p, weight: validRatio, weightAmount: calculatedAmount };
          } else {
            // 金额调整逻辑：如果超过最大允许值（基于一级分类），则设为最大值
            const validAmount = Math.max(0, Math.min(numericValue, maxAllowedAmount));
            // 根据金额计算占比（基于一级分类总金额）
            const calculatedRatio = primaryTotalAmount > 0 ? (validAmount / primaryTotalAmount) * 100 : 0;
            return { ...p, weightAmount: validAmount, weight: calculatedRatio };
          }
        });

        // 更新二级分类的总占比和总金额
        const newSecondaryRatio = updatedProducts.reduce((sum, p) => sum + p.weight, 0);
        const newSecondaryAmount = updatedProducts.reduce((sum, p) => sum + p.weightAmount, 0);

        return {
          ...secondary,
          products: updatedProducts,
          totalAllocationRatio: newSecondaryRatio,
          totalAllocationAmount: newSecondaryAmount
        };
      });

      return {
        ...primary,
        secondaryCategories: updatedSecondaries
        // 不再更新一级分类的总占比和总金额，因为只是内部调整
      };
    }));
  };

  const handleDelete = (categoryId: string, secondaryId: string, productId: string) => {
    setCategories(prev => prev.map(primary => {
      if (primary.assetcatCode !== categoryId) return primary;

      const updatedSecondaries = primary.secondaryCategories.map(secondary => {
        if (secondary.pfcatCode !== secondaryId) return secondary;

        const updatedProducts = secondary.products.filter(p => p.proCode !== productId);
        const productCount = updatedProducts.length;

        if (productCount === 0) {
          return {
            ...secondary,
            products: [],
            totalAllocationRatio: 0,
            totalAllocationAmount: 0
          };
        }

        const avgRatio = secondary.totalAllocationRatio / productCount;
        const avgAmount = (secondary.totalAllocationAmount * avgRatio) / 100;
        const adjustedProducts = updatedProducts.map(p => ({
          ...p,
          weight: avgRatio,
          weightAmount: avgAmount
        }));

        return { ...secondary, products: adjustedProducts };
      });

      const newPrimaryRatio = updatedSecondaries.reduce((sum, s) => sum + s.totalAllocationRatio, 0);
      const newPrimaryAmount = updatedSecondaries.reduce((sum, s) => sum + s.totalAllocationAmount, 0);

      return {
        ...primary,
        secondaryCategories: updatedSecondaries,
        totalAllocationRatio: newPrimaryRatio,
        totalAllocationAmount: newPrimaryAmount
      };
    }));
  };

  const handleReplace = async (product: Product, categoryId: string, secondaryId: string) => {
    if (isReplacing[product.proCode]) return;
    setIsReplacing(prev => ({ ...prev, [product.proCode]: true }));

    try {
      let data = {
        ...product,
        assertatCode: categoryId,
        pfcatCode: secondaryId,
      }
      const res = await getSimilarFunds(data);
      if (!res?.data?.data) {
        messageApi.warning('未获取到相似基金');
        return;
      }

      const similar = res.data.data as SimilarFund;
      if (!similar.proCode || !similar.proName || !similar.proRiskCode) {
        messageApi.warning('相似基金数据不完整，无法替换');
        return;
      }

      setCategories(prev => prev.map(primary => {
        if (primary.assetcatCode !== categoryId) return primary;

        const updatedSecondaries = primary.secondaryCategories.map(secondary => {
          if (secondary.pfcatCode !== secondaryId) return secondary;

          const updatedProducts = secondary.products.map(p =>
            p.proCode === product.proCode
              ? {
                ...p,
                proCode: similar.proCode,
                proName: similar.proName,
                proRiskCode: similar.proRiskCode,
                proRiskName: similar.proRiskName
              }
              : p
          );
          return { ...secondary, products: updatedProducts };
        });

        return { ...primary, secondaryCategories: updatedSecondaries };
      }));

      messageApi.success(`已替换为：${similar.proName}（风险等级：${similar.proRiskName}）`);
    } catch (err) {
      messageApi.error('相似基金获取失败，请重试');
    } finally {
      setIsReplacing(prev => ({ ...prev, [product.proCode]: false }));
    }
  };

  const handleAddProduct = (
    categoryId: string,
    newProducts: any[]
  ) => {
    // 边界校验：产品列表非空
    if (newProducts.length === 0) {
      // 清空当前大类下的所有产品
      setCategories(prev =>
        prev.map(primary => {
          if (primary.assetcatCode !== categoryId) return primary;
          return {
            ...primary,
            secondaryCategories: [],
            totalAllocationRatio: 0,
            totalAllocationAmount: 0
          };
        })
      );
      messageApi.info("已清空当前大类下的所有产品");
      return;
    }

    // 1. 按产品所属的二级分类（pfcatCode）分组
    const productsBySecondary: Record<string, {
      name: string;
      products: any[];
    }> = {};

    newProducts.forEach(product => {
      const secCode = product.pfcatCode;
      const secName = product.pfcatName;

      if (!secCode || !secName) {
        messageApi.warning(`产品「${product.proName || '未知产品'}」缺少分类信息，已跳过`);
        return;
      }

      if (!productsBySecondary[secCode]) {
        productsBySecondary[secCode] = {
          name: secName,
          products: []
        };
      }
      productsBySecondary[secCode].products.push(product);
    });

    // 若所有产品都因缺少分类信息被过滤，直接返回
    if (Object.keys(productsBySecondary).length === 0) {
      messageApi.error("所有产品均缺少有效的二级分类信息，无法添加");
      return;
    }

    setCategories(prev =>
      prev.map(primary => {
        if (primary.assetcatCode !== categoryId) return primary;

        // 完全重建二级分类结构
        const updatedSecondaries = Object.entries(productsBySecondary).map(([secCode, group]) => {
          // 转换产品格式
          const products: Product[] = group.products.map(prod => ({
            proCode: prod.proCode,
            proName: prod.proName,
            proRiskCode: prod.proRiskCode || "",
            proRiskName: prod.proRiskName || "未知风险",
            weight: 0, // 初始权重设为0
            weightAmount: 0 // 初始金额设为0
          }));

          // 计算总占比和总金额
          const totalRatio = products.reduce((sum, p) => sum + p.weight, 0);
          const totalAmount = products.reduce((sum, p) => sum + p.weightAmount, 0);

          return {
            pfcatCode: secCode,
            pfcatName: group.name,
            products,
            totalAllocationRatio: totalRatio,
            totalAllocationAmount: totalAmount
          };
        });

        // 计算一级分类总数据
        const totalRatio = updatedSecondaries.reduce((sum, sec) => sum + sec.totalAllocationRatio, 0);
        const totalAmount = updatedSecondaries.reduce((sum, sec) => sum + sec.totalAllocationAmount, 0);

        return {
          ...primary,
          secondaryCategories: updatedSecondaries,
          totalAllocationRatio: totalRatio,
          totalAllocationAmount: totalAmount
        };
      })
    );

    // 生成成功提示
    const successMessages = Object.entries(productsBySecondary).map(([secCode, group]) => {
      const uniqueCount = group.products.filter(
        (prod, index, self) => self.findIndex(p => p.proCode === prod.proCode) === index
      ).length;
      return `「${group.name}」(${secCode}) 设置 ${uniqueCount} 个产品`;
    });
    messageApi.success(`产品设置完成：\n${successMessages.join('\n')}`);
  };


  const setAiConfig = () => {
    getAiConfig({ broker_account: 'cty0304' }).then(res => {
      if (!res?.data?.data) {
        messageApi.error('AI配置数据为空');
        return;
      }
      const aiProducts = res.data.data.map((item: any) => ({
        proCode: item.proCode || '',
        proName: item.proName || '',
        assetcatCode: item.assetcatCode || '',
        assetcatName: item.assetcatName || '',
        pfcatCode: item.pfcatCode || '',
        pfcatName: item.pfcatName || '',
        proRiskCode: item.proRiskCode || '',
        proRiskName: item.proRiskName || '',
        weight: item.weight || 0,
        weightAmount: item.weightAmount || 0
      })).filter((p: any) => p.proCode && p.assetcatCode && p.pfcatCode);

      const aiData: OriginalDataStructure = {
        amount: aiProducts.reduce((sum: number, p: any) => sum + p.weightAmount, 0),
        products: aiProducts
      };
      setCategories(transformToInternal(aiData));
    }).catch(() => messageApi.error('AI配置获取失败'));
  };

  const handleSubmit = () => {
    if (!isSubmittable) {
      showRatioError();
      return;
    }

    const submitData = transformToOriginal(categories);
    const newHumanMsg = {
      id: uuidv4(),
      type: "human",
      response_metadata: { interrupting: true },
      content: '确认当前方案'
    };

    if (thread.interrupt?.value) {
      const newAiMsg = {
        ...thread.interrupt.value.message,
        tool_calls: [
          {
            ...thread.interrupt.value.message.tool_calls[0],
            args: {
              ...thread.interrupt.value.message.tool_calls[0].args,
              card_data: {
                ...thread.interrupt.value.message.tool_calls[0].args.card_data,
                ...submitData,
                params,
                completed: true
              }
            }
          }
        ]
      };

      setinputDisabled(true);
      setNewMessages([...thread.messages, newAiMsg, newHumanMsg]);
      thread.submit({}, {
        command: { resume: submitData.products, update: { messages: [newAiMsg, newHumanMsg] } }
      });
    }
  };

  useEffect(() => {
    setinputDisabled(false);
  }, []);

  return (
    <div className='mt-4'>
      {contextHolder}

      <div className='flex justify-end text-12 text-[#0A0A0A66] mb-2 cursor-pointer hover:text-[#BF9267FF]'>
        查看配置逻辑
      </div>

      {!completed && (
        <div
          onClick={setAiConfig}
          className='w-full bg-[#BF9267FF] text-center text-white rounded-[10px] py-2 mb-4 cursor-pointer hover:bg-[#b38050] transition-colors'
        >
          AI一键配置
        </div>
      )}
      {/* 遍历一级分类 */}
      <div className='bg-[white] p-2 rounded-xl'>
        {categories.length > 0 ? (
          categories.map(primary => (
            <div key={primary.assetcatCode} className="p-4 mt-4 rounded-lg bg-gradient-to-b from-[rgba(191,146,103,0.1)] via-transparent via-10% to-white shadow-md shadow-bottom">
              <div className="flex justify-between items-center pb-2">
                {/* 一级分类名称 + 总占比 */}
                <div className="font-semibold text-16 text-[#0A0A0A]">
                  {primary.assetcatName}
                  <span className="ml-2 font-medium text-12 text-[#BF9267]">
                    {Number(primary.totalAllocationRatio.toFixed(2))}%
                  </span>
                </div>

                {/* 添加产品按钮（与一级分类平级） */}
                {!completed && (
                  <AddProductPage
                    // 若需要默认选中该一级分类下的某个二级分类，可在这里指定，如：
                    category={primary}
                    onSelect={(newProduct) => {
                      if (!newProduct[0]?.pfcatCode) {
                        messageApi.info('请先为产品选择所属二级分类');
                        return;
                      }
                      handleAddProduct(
                        primary.assetcatCode,
                        newProduct
                      );
                    }}
                  >
                    <div className="text-[#8b572a] px-3 py-1 cursor-pointer text-14">
                      + 添加产品
                    </div>
                  </AddProductPage>
                )}
              </div>

              <div className="flex text-12 text-[#0A0A0A] mb-3">
                <div className='mr-4'>
                  <span className="text-[#0A0A0A66] mr-2">已配置:</span>
                  {Number(primary.totalAllocationRatio).toFixed(2)}%
                </div>
                <div>
                  <span className="text-[#0A0A0A66] mr-2">金额:</span>
                  {Number(primary.totalAllocationAmount).toFixed(2)}元
                </div>
              </div>

              {/* 遍历二级分类（核心修改2：删除二级分类内的"添加产品"按钮） */}
              {primary.secondaryCategories.map(secondary => (
                <div key={secondary.pfcatCode} className="mb-6">
                  {/* 二级分类标题（移除原有的添加产品按钮） */}
                  <div className="flex justify-between items-center mb-3">
                    <div className="font-medium text-14 text-[#0A0A0A]">
                      {secondary.pfcatName}
                    </div>
                  </div>

                  {secondary.products.length > 0 ? (
                    secondary.products.map(product => (
                      <div key={product.proCode} className="p-3 mb-3 bg-[#BF92670D] rounded-lg">
                        <div className="flex justify-between mb-3">
                          <div className="font-medium text-14 text-[#0A0A0A] mr-8">
                            {product.proName}
                          </div>

                          {!completed && (
                            <div className="flex">
                              <div
                                className='flex mr-2 cursor-pointer'
                                onClick={() => handleReplace(product, primary.assetcatCode, secondary.pfcatCode)}
                                style={{ opacity: isReplacing[product.proCode] ? 0.6 : 1 }}
                              >
                                <img
                                  className='mr-1 h-3 w-3 relative top-[5px]'
                                  src={Turn.src}
                                  alt="替换"
                                  style={{ animation: isReplacing[product.proCode] ? 'spin 1s linear infinite' : 'none' }}
                                />
                                <span className='text-12 text-[#BF9267FF]'>
                                  {isReplacing[product.proCode] ? '替换中...' : '相似替换'}
                                </span>
                              </div>

                              <Popconfirm
                                title="确认删除该产品？"
                                onConfirm={() => handleDelete(primary.assetcatCode, secondary.pfcatCode, product.proCode)}
                                okText="确认"
                                cancelText="取消"
                              >
                                <div
                                  className='text-[#E34D59FF] text-12 cursor-pointer'
                                  style={{
                                    opacity: isReplacing[product.proCode] ? 0.6 : 1,
                                    pointerEvents: isReplacing[product.proCode] ? 'none' : 'auto'
                                  }}
                                >
                                  删除
                                </div>
                              </Popconfirm>
                            </div>
                          )}
                        </div>

                        <div className="flex text-14 text-[#333]">
                          <div className='mr-4 flex items-center'>
                            <div className="w-[70px] font-normal text-12 text-[#0A0A0A66]">占比</div>
                            <Input
                              className="w-auto rounded-lg !ml-3 !mr-3 font-medium text-14 !bg-white !min-h-8"
                              size='small'
                              disabled={completed || isReplacing[product.proCode]}
                              bordered={false}
                              value={Number(product.weight).toFixed(2)}
                              onChange={(e) => handleProductChange(
                                primary.assetcatCode,
                                secondary.pfcatCode,
                                product.proCode,
                                'weight',
                                e.target.value
                              )}
                            />
                            <div className="text-12 text-[#0A0A0A66]">%</div>
                          </div>
                          <div className='flex items-center'>
                            <div className="w-[70px] font-normal text-12 text-[#0A0A0A66]">金额</div>
                            <Input
                              className="w-auto rounded-lg !ml-3 !mr-3 font-medium text-14 !bg-white !min-h-8"
                              size='small'
                              disabled={completed || isReplacing[product.proCode]}
                              bordered={false}
                              value={Number(product.weightAmount).toFixed(2)}
                              onChange={(e) => handleProductChange(
                                primary.assetcatCode,
                                secondary.pfcatCode,
                                product.proCode,
                                'weightAmount',
                                e.target.value
                              )}
                            />
                            <div className="text-12 text-[#0A0A0A66]">元</div>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-12 text-[#999] italic">暂无产品，可点击一级分类右侧"添加产品"按钮新增</div>
                  )}
                </div>
              ))}
            </div>
          ))
        ) : (
          <div className="text-center text-14 text-[#999] py-8">暂无配置数据，请点击"AI一键配置"或添加产品</div>
        )}
        <ParamsSetting onChange={(val) => {
          setParams(val)
        }}></ParamsSetting>
      </div>

      <div className='flex mt-4'>
        {/* <div className='text-[#BF9267FF] text-center bg-[#BF92671A] px-3 py-2 rounded-[8px] mr-2 cursor-pointer hover:bg-[#BF92672A] transition-colors'>
          查看配置分析
        </div> */}
        <div
          className={`text-center px-3 py-2 rounded-[8px] cursor-pointer ${completed
            ? 'bg-[#cacaca88] text-[#7b7b7b88]'
            : 'bg-[#BF9267FF] text-white hover:bg-[#b38050] transition-colors'
            }`}
          onClick={handleSubmit}
          style={{ pointerEvents: (isSubmittable && !completed) ? 'auto' : 'none' }}
        >
          确认当前方案
        </div>
      </div>
    </div>
  );
};

export default AssetAllocation;