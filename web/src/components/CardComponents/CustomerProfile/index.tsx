import { MarkdownText } from "../../thread/markdown-text";

const CustomerProfile = ({ data }: { data: any }) => {
  const { text, title } = data
  return <div >
    <div className="p-4 rounded-[10px]" style={{ background: 'linear-gradient( 180deg, rgba(191,146,103,0.1) 0%, rgba(255,255,255,0) 25%)' }}>
      <div className="font-medium text-16 text-[#0A0A0A] mb-2">{title}</div>
      <MarkdownText>{text}</MarkdownText>
    </div>
  </div>
}

export default CustomerProfile;