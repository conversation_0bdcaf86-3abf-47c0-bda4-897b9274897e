import userImg from '@/app/icons/user.png';
import rateImg from '@/app/icons/rate.png';
import reportImg from '@/app/icons/report.png';
import configImg from '@/app/icons/config.png'

// 定义条目配置映射，关联名称、图标和点击事件
// @ts-nocheck

const itemConfig: any = {
  '基本信息': {
    icon: userImg,
    onClick: (item: any) => {
      let post = {
        route: '/aihome/userinfo',
        state: {
          content: item.data,
        }
      }
      console.log('唤起基本信息第二屏', item);
    }
  },
  '持仓信息': {
    icon: rateImg,
    onClick: () => console.log('唤起持仓信息第二屏')
  },
  '配置分析': {
    icon: configImg,
    onClick: () => console.log('唤起基本信息第二屏')
  },
  '规划报告': {
    icon: reportImg,
    onClick: () => console.log('唤起基本信息第二屏')
  },
  // 可以在这里添加更多条目配置
};

// @ts-ignore
const InformationOrigin = ({ noTitle, data }) => {
  const { table = [] } = data;

  return (
    <div className="p-4 bg-white rounded-lg">
      {
        !noTitle && <div className="font-medium text-base text-[#0A0A0A] text-left font-['PingFang_SC'] mb-3">信息溯源</div>
      }
      <div className="flex flex-wrap gap-2">

        {table.map((item: any, index: any) => {
          // 获取当前条目的配置
          const config = itemConfig[item.name];

          // 如果没有配置则跳过（可选）
          if (!config) return null;

          return (
            <div
              key={index}
              onClick={() => { config.onClick(item) }}
              className="flex-1 p-3 px-4 bg-[rgba(191,146,103,0.05)] rounded-lg cursor-pointer"
            >
              <div className="font-medium text-sm text-[#0A0A0A] leading-5 text-left relative">
                {item.name}
                <img
                  src={config.icon.src}
                  alt={item.name}
                  className="absolute top-[-3px] right-1 w-[60px] h-[46px]"
                />
              </div>
              <div className="font-normal text-sm text-[rgba(10,10,10,0.4)] leading-4 text-left mt-1" >
                点击查看
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default InformationOrigin;
