import { useEffect, useState } from 'react'
import { AnimatePresence, motion } from "framer-motion";
import {
  Co<PERSON>,
  CopyCheck,
} from "lucide-react";
import { showToast } from '../../../utils/toast'
import { useThreadActivities } from '@/contexts/ThreadActivitiesContext'

const CopyContent = ({ data }: { data: any }) => {
  const [copied, setCopied] = useState(false);
  const { setinputDisabled } = useThreadActivities()

  function copyWithExecCommand(textToCopy: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // 创建临时文本框
      const textarea = document.createElement('textarea');
      textarea.value = textToCopy;
      // 隐藏元素（避免影响布局，同时确保能被选中）
      textarea.style.position = 'fixed';
      textarea.style.top = '-999px';
      textarea.style.left = '-999px';
      textarea.style.opacity = '0';
      document.body.appendChild(textarea);

      try {
        // 选中内容（兼容移动端和特殊环境）
        textarea.select();
        textarea.setSelectionRange(0, textToCopy.length); // 强制选中全部内容

        // 执行复制（execCommand 返回布尔值表示成功与否）
        const isSuccess = document.execCommand('copy');
        if (isSuccess) {
          resolve();
        } else {
          reject(new Error('传统复制命令执行失败'));
        }
      } catch (err) {
        reject(err);
      } finally {
        // 清理临时元素
        document.body.removeChild(textarea);
      }
    });
  }

  const handleCopy = async () => {
    const textToCopy = data.text;
    try {
      // 检测是否为企业微信环境
      const isWeCom = /wxwork/i.test(navigator.userAgent);
      // 企业微信环境强制使用传统方案（Clipboard API 大概率被禁用）
      if (isWeCom) {
        await copyWithExecCommand(textToCopy);
        showToast('已写入剪切板', 'info');
        setCopied(true)
        return;
      }
      // 非企业微信环境：优先用现代 API，失败则降级
      if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
        try {
          await navigator.clipboard.writeText(textToCopy);
          setCopied(true)
          showToast('已写入剪切板', 'info');
        } catch (error) {
          // 现代 API 失败时，自动降级到传统方案
          await copyWithExecCommand(textToCopy);
          setCopied(true)
          showToast('已写入剪切板', 'info');
        }
      } else {
        // 不支持现代 API 时直接用传统方案
        await copyWithExecCommand(textToCopy);
        setCopied(true)
        showToast('已写入剪切板', 'info');
      }
    } catch (error) {
      console.error('复制失败:', error);
      showToast('复制失败，请手动复制', 'error');
    }
    setTimeout(() => setCopied(false), 2000);
  };



  useEffect(() => {
    setinputDisabled(true)
  }, [setinputDisabled]) // 依赖项添加 setinputDisabled，避免 eslint 警告
  console.log('copied', copied)
  return (
    <div className="p-2 bg-[white] mt-2 text-[#0a0a0a] rounded-[10px]" >
      <div className='p-2'>{data.text}</div>
      {data.copyable && (
        <div className='flex justify-end'>
          {copied ? (
            <motion.div
              key="check"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.15 }}
            >
              <div className='p-[6px] bg-[#0A0A0A08] rounded-[8px]'>
                <CopyCheck size={16} />
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="copy"
              onClick={handleCopy} // 直接绑定函数，避免额外箭头函数
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.15 }}
              className="cursor-pointer" // 添加鼠标指针样式，提示可点击
            >
              <div className='p-[6px] bg-[#0A0A0A08] rounded-[8px]'>
                <Copy size={16} />
              </div>
            </motion.div>
          )}
        </div>
      )}
    </div>
  )
}

export default CopyContent
