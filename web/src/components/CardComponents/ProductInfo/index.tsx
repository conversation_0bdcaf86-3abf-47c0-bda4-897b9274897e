import { tablePercentFixedRender } from '@/utils/renderFormatter'
import { Table } from 'antd'
// @ts-nocheck

const ProductInfo = ({ data }: { data: any }) => {
  const columns = [
    {
      title: '持有期',
      dataIndex: 'name',
    },
    {
      title: '认购费',
      dataIndex: 'return',
    },
    {
      title: '赎回费',
      dataIndex: 'type',
    },
  ]

  return <div>
    <div className="bg-white p-4 mb-5 rounded-lg">
      <div className="font-medium text-base text-[#0A0A0A] text-left h-6 mb-2">
        {data.product_name}
      </div>
      <div className="flex items-center gap-2">
        {
          data.product_status &&
          <span className="px-2 py-1 rounded text-sm font-medium bg-[#BF9267] text-white">
            {data.product_status}
          </span>
        }
        {data.risk_level_name &&
          <span className="px-2 py-1 rounded text-sm font-medium bg-[#FFF1B8] text-[#ED7B2F]">
            {data.risk_level_name}
          </span>
        }
        <span className="font-normal text-sm text-[rgba(10,10,10,0.6)] leading-4 text-left">
          {data.minimum_investment ? Number(data.minimum_investment) / 10000 : '--'}万起投 | 成立于{data.set_up_date || '--'}
        </span>
      </div>
      <div className="flex justify-between mx-12">
        <div className="font-normal text-sm text-[rgba(10,10,10,0.4)] text-center mb-6">
          <div className="font-bold text-xl text-[#0A0A0A] mb-1.5 font-['DIN_Alternate']">
            {tablePercentFixedRender(Number(data.last_month_return), 2, false)}
          </div>
          <div>近一月涨跌幅</div>
        </div>
        <div className="font-normal text-sm text-[rgba(10,10,10,0.4)] text-center mb-6">
          <div className="font-bold text-xl text-[#0A0A0A] mb-1.5 font-['DIN_Alternate']">
            {tablePercentFixedRender(Number(data.last_year_return), 2, false)}
          </div>
          <div>近一年涨跌幅</div>
        </div>
      </div>
      <div className="h-8 rounded">
        <span className="font-normal text-sm text-[rgba(10,10,10,0.6)] leading-8 pl-3">
          业绩基准：{data.performance_benchmark}
        </span>
      </div>
      <div className="flex justify-between mx-5 mt-1">
        <div className="font-normal text-sm text-[rgba(10,10,10,0.4)] text-center mb-6">
          <div className="font-bold text-lg text-[#0A0A0A] mb-1 font-['DIN_Alternate']">
            {data.lockup_period || '--'}
          </div>
          <div>锁定期</div>
        </div>
        <div className="font-normal text-sm text-[rgba(10,10,10,0.4)] text-center mb-6">
          <div className="font-bold text-lg text-[#0A0A0A] mb-1 font-['DIN_Alternate']">
            {data.redemption_schedule || '--'}
          </div>
          <div>开放申赎</div>
        </div>
        <div className="font-normal text-sm text-[rgba(10,10,10,0.4)] text-center mb-6">
          <div className="font-bold text-lg text-[#0A0A0A] mb-1 font-['DIN_Alternate']">
            {data.next_open_date || '--'}
          </div>
          <div>下一个开放日</div>
        </div>
      </div>
      <div className="font-medium text-base text-[#0A0A0A] text-left mb-4">费用信息</div>
      <div >
        <Table size='small' locale={{ emptyText: '暂无数据' }} bordered columns={columns} dataSource={[]}></Table>
      </div>
    </div>

  </div>
}

export default ProductInfo