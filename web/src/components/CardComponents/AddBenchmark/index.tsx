"use client";
import React, { useState, useEffect, useRef } from 'react';
import { Search, Trash2, RefreshCw } from 'lucide-react';
import { Divider, List, Button, Empty, Spin, Alert } from 'antd';
import { Popup, SpinLoading, Radio, SearchBar, InfiniteScroll } from 'antd-mobile';
// 获取基准列表（适配简单数据结构）
const fetchBenchmarks = async (
  page: number,
  pageSize: number = 10,
  searchText: string
): Promise<{ data: any[], hasMore: boolean }> => {
  try {
    // 实际接口调用
    const response = [
      { "symbol": "000001", "productname": "上证综合指数" },
      { "symbol": "000300", "productname": "沪深300指数" },
      { "symbol": "000905", "productname": "中证500指数" },
      { "symbol": "399001", "productname": "深证成份指数" },
      { "symbol": "399006", "productname": "创业板指数" },
      { "symbol": "000016", "productname": "上证50指数" },
      { "symbol": "000852", "productname": "中证1000指数" },
      { "symbol": "H11001", "productname": "恒生指数" },
      { "symbol": "SPX", "productname": "标普500指数" },
      { "symbol": "IXIC", "productname": "纳斯达克综合指数" },
      { "symbol": "DJI", "productname": "道琼斯工业平均指数" },
      { "symbol": "HSCEI", "productname": "恒生中国企业指数" },
      { "symbol": "FTSE", "productname": "富时100指数" },
      { "symbol": "DAX", "productname": "德国DAX指数" },
      { "symbol": "N225", "productname": "日经225指数" },
      { "symbol": "000010", "productname": "上证180指数" },
      { "symbol": "399330", "productname": "深证100指数" },
      { "symbol": "000005", "productname": "上证国债指数" },
      { "symbol": "000012", "productname": "上证企债指数" },
      { "symbol": "CBA00101.CS", "productname": "中债总指数" },
      { "symbol": "USDCNY.FX", "productname": "美元兑人民币汇率" },
      { "symbol": "CL1.COM", "productname": "美原油连续合约" },
      { "symbol": "XAUUSD.GC", "productname": "国际现货黄金" },
      { "symbol": "MSCI.CHINA", "productname": "MSCI中国指数" },
      { "symbol": "MSCI.EM", "productname": "MSCI新兴市场指数" }
    ]


    // 假设接口返回格式 { data: { list: [], totalCount: 0 } }
    const totalCount = 10000
    const benchmarks = response || [];

    return {
      data: benchmarks,
      hasMore: page * pageSize < totalCount
    };
  } catch (error) {
    console.error('获取基准失败:', error);
    // 模拟数据处理
  }
};

const AddBenchmarkPage = ({
  children,
  initialBenchmark, // 初始已选基准
  onSelect
}: {
  children: React.ReactNode,
  initialBenchmark?: { symbol: string; productname: string },
  onSelect: (benchmark: { symbol: string; productname: string } | null) => void
}) => {
  const [searchText, setSearchText] = useState('');
  const [selectedBenchmark, setSelectedBenchmark] = useState<{ symbol: string; productname: string } | null>(initialBenchmark || null);
  const [filteredBenchmarks, setFilteredBenchmarks] = useState<any[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [error, setError] = useState<string | null>(null);

  // 初始化基准列表
  const initBenchmarkList = async () => {
    setLoading(true);
    setError(null);
    setPage(1);
    try {
      const { data, hasMore } = await fetchBenchmarks(1, 10, searchText);
      setFilteredBenchmarks(data);
      setHasMore(hasMore);
    } catch (err) {
      setError('加载失败，请重试');
      console.error('初始化基准列表失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 弹窗打开或搜索变化时重新加载
  useEffect(() => {
    if (isModalOpen) {
      initBenchmarkList();
    }
  }, [isModalOpen, searchText]);

  // 移除 IntersectionObserver 相关代码

  // 加载更多基准
  const loadMoreBenchmarks = async () => {
    if (loading || !hasMore) return;
    setLoading(true);
    setError(null);
    try {
      const nextPage = page + 1;
      const { data, hasMore: newHasMore } = await fetchBenchmarks(
        nextPage, 10, searchText
      );
      setFilteredBenchmarks(prev => [...prev, ...data]);
      setPage(nextPage);
      setHasMore(newHasMore);
    } catch (err) {
      setError('加载更多失败，请重试');
      console.error('加载更多基准失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  // 选择基准（单选）
  const handleBenchmarkSelect = (benchmark: any) => {
    setSelectedBenchmark(benchmark);
  };

  // 移除已选基准
  const removeSelectedBenchmark = () => {
    setSelectedBenchmark(null);
  };

  // 检查是否已选中
  const isSelected = (symbol: string) => {
    return selectedBenchmark?.symbol === symbol;
  };

  // 确认选择
  const handleConfirmSelection = () => {
    setIsModalOpen(false);
    onSelect(selectedBenchmark);
  };

  return (
    <div>
      {/* 触发弹窗的元素 */}
      <div onClick={() => setIsModalOpen(true)} style={{ cursor: 'pointer' }}>
        {children}
      </div>

      <Popup
        visible={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        position="bottom"
        bodyStyle={{
          height: '80vh',
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          padding: '0'
        }}
      >
        {/* 标题 */}
        <div className="p-4 text-base font-bold sticky top-0 bg-white z-10 border-b border-gray-200">
          选择基准
        </div>

        <div className="bg-white">
          {/* 搜索框 */}
          <div className="p-2 bg-white">
            <SearchBar
              clearable
              clearOnCancel
              placeholder="请输入基准名称或代码"
              value={searchText}
              onChange={handleSearch}
              showCancelButton
              className="rounded-full"
            />
          </div>

          {/* 已选基准区域 */}
          <div className="mx-2 rounded-[8px] p-4 bg-white">
            <div className="mb-2">已选择基准</div>
            <List
              locale={{ emptyText: '暂无已选基准' }}
              dataSource={selectedBenchmark ? [selectedBenchmark] : []}
              renderItem={(item) => (
                <List.Item
                  className="flex justify-between items-center mb-2 border-0 bg-[#f7f7f7] rounded-[6px] p-2 py-3"
                >
                  <div className="pl-2">
                    <div className="font-medium text-sm">{item.productname}</div>
                    <div className="text-xs text-gray-500 mt-1">{item.symbol}</div>
                  </div>
                  <Button
                    icon={<Trash2 size={14} color="#BF9267FF" />}
                    size="small"
                    type="link"
                    onClick={removeSelectedBenchmark}
                  />
                </List.Item>
              )}
            />
          </div>

          {/* 基准列表（带无限滚动） */}
          <div className="mx-2 rounded-[8px] p-4 bg-white" style={{ height: '40vh', overflow: 'auto' }}>
            <Divider size="small" />
            <div className="mb-3 text-[#0a0a0a77] text-sm font-medium">
              基准列表
            </div>
            {filteredBenchmarks.length > 0 ? (
              <List
                dataSource={filteredBenchmarks}
                renderItem={(item) => (
                  <List.Item
                    className="flex justify-between items-center border-b last:border-0 hover:bg-[#fafafa]"
                  >
                    <div className="flex items-center">
                      <Radio
                        checked={isSelected(item.symbol)}
                        onChange={() => handleBenchmarkSelect(item)}
                        value={item.symbol}
                      />
                      <div className="ml-2 flex">
                        <div className="font-medium text-sm">{item.productname}</div>
                        <div className="text-xs ml-2 text-gray-500">{item.symbol}</div>
                      </div>
                    </div>
                  </List.Item>
                )}
              />
            ) : (
              <Empty description="未找到匹配的基准" className="my-8" />
            )}

            <InfiniteScroll
              loadMore={loadMoreBenchmarks}
              hasMore={hasMore}
              threshold={50}
            >
              {loading && (
                <div className="flex justify-center p-4 text-[#BF9267FF]">
                  <SpinLoading style={{ '--size': '14px' }} color='#00b578'></SpinLoading>加载中
                </div>
              )}
              {error && (
                <div className="flex justify-center p-4">
                  <span className="text-red-500 mr-2">{error}</span>
                  <Button
                    size="small"
                    onClick={loadMoreBenchmarks}
                  >
                    <RefreshCw size={12} className="mr-1" />
                    重试
                  </Button>
                </div>
              )}
              {!hasMore && (
                <div className="text-center p-4 text-sm text-gray-500">
                  已加载全部数据
                </div>
              )}
            </InfiniteScroll>
          </div>

          {/* 底部按钮 */}
          <div className="sticky bottom-0 p-4 bg-white border-t border-gray-200 flex justify-between">
            <Button
              onClick={() => setIsModalOpen(false)}
              className="flex-1 mr-2"
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={handleConfirmSelection}
              className="flex-1"
              disabled={!selectedBenchmark}
            >
              确认选择
            </Button>
          </div>
        </div>
      </Popup>
    </div>
  );
};

export default AddBenchmarkPage;
