import UserProfile from "../Components/UserProfile/index"
import { Table } from 'antd'
import { tablePercentFixedRender } from '@/utils/renderFormatter'
// @ts-nocheck

const TransactionRecord = ({ data }: { data: any }) => {
  const columns: any = [
    {
      title: '交易日期',
      dataIndex: 'trade_date',
      width: 100
    },
    {
      title: '业务类型',
      dataIndex: 'trade_type',
      width: 100
    },
    {
      title: '产品名称',
      dataIndex: 'product_name',
      width: 160
    },
    {
      title: '产品代码',
      dataIndex: 'product_code',
      width: 100
    },
    {
      title: '交易金额',
      dataIndex: 'trade_amount',
      width: 120
    },
    {
      title: '交易状态',
      dataIndex: 'trade_status',
      width: 100
    },
    {
      title: '双录状态',
      dataIndex: 'recording_status',
      width: 100
    },
  ]
  return <div>
    <UserProfile data={data.customer_info} />
    <div className="bg-white p-4 mb-3 rounded-lg">
      <div className="font-medium text-base text-[#0A0A0A] text-left mb-4">
        交易记录明细
      </div>
      <div>
        <Table
          scroll={{
            x: 800
          }}
          size='small'
          pagination={false}
          bordered
          columns={columns}
          dataSource={data.trade_info}></Table>
      </div>
    </div>

  </div>
}

export default TransactionRecord