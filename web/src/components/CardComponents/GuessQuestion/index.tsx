import { getGuessList } from '@/services/api'
import { useEffect, useState } from 'react'
import rightIcon from "@/app/icons/right.png";
import { useStreamContext } from "@/providers/Stream";
import { v4 as uuidv4 } from "uuid";
// @ts-nocheck

export default ({ threadId }: { threadId: string | null }) => {
  const [questions, setquestions] = useState([])
  const stream = useStreamContext();
  // @ts-ignore
  const { setShowGuessQuestion } = stream
  useEffect(() => {
    getGuessList({ thread_id: threadId }).then(res => {
      setquestions(res.data.data)
    })
  }, [])
  const handleSubmit = (input: any) => {
    if ((input.trim().length === 0))
      return;
    setShowGuessQuestion(false)
    const newHumanMessage: any = {
      id: uuidv4(),
      type: "human",
      content: input
    };

    stream.submit(
      {
        messages: [newHumanMessage],
        //@ts-ignore
        current_step: null,
        current_step_detail: "",
        next_node: null,
        next_state: null,
      },
    );

  }
  return (
    <div>
      {
        questions && questions.length > 0 && <div className="bg-[#F7F7F7FF] mb-8 p-3 pl-0 pb-0">
          {/* 标题区域 */}
          <div className="font-[PingFang_SCBold,PingFang_SC] font-medium text-14 text-[#0A0A0A] leading-[20px] text-left mb-4">
            🤔 猜你想问
          </div>

          {/* 问题列表区域 */}
          <div>
            {questions.map(item => (
              <div
                key={item}
                className="bg-white flex justify-between mb-2 p-[9px_12px] rounded-[10px] cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => handleSubmit(item)}
              >
                <div>
                  <span className="font-[PingFang_SC, PingFang_SC] font-normal text-14 text-[rgba(10,10,10,0.6)] leading-[20px] text-left">
                    {item}
                  </span>
                </div>
                <div>
                  <img
                    width={16}
                    height={16}
                    src={rightIcon.src}
                    className="inline-block relative top-[0px] ml-4"
                    alt="箭头"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      }
    </div>
  );
}