import UserProfile from "../Components/UserProfile/index"
import { Table } from 'antd'
import { tablePercentFixedRender } from '@/utils/renderFormatter'
// @ts-nocheck
/**
* 位置信息组件
*
* @param data 包含位置信息的对象
* @returns 返回渲染后的 JSX 元素
*/
const Positioninformation = ({ data }: { data: any }) => {
  const columns = [
    {
      title: '产品名称',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 100
    },
    {
      title: '持有期收益',
      dataIndex: 'holding_return',
      key: 'holding_return',
      width: 120,
      render: (text: any) => {
        return tablePercentFixedRender(data.holding_return, 2, false)
      } // 假设收益以百分比显示
    },
    {
      title: '产品类型',
      width: 100,
      dataIndex: 'project_type',
      key: 'project_type',
    },
    {
      title: '持仓市值',
      width: 100,
      dataIndex: 'position_value',
      key: 'position_value',
      render: (text: any) => `¥${text.toLocaleString()}`, // 格式化金额显示
    },
    {
      title: '持仓占比',
      dataIndex: 'position_ratio',
      key: 'position_ratio',
      width: 100,
      render: (text: any) => `${text}%`, // 假设占比以百分比显示
    }
  ];

  return <div>
    <UserProfile data={data.customer_info}></UserProfile>
    <div className="bg-white p-4 mb-3">
      <div className="flex justify-between mx-10">
        <div className="font-normal text-sm text-[rgba(10,10,10,0.4)] text-center mb-6">
          <div className="font-bold text-xl text-[#0A0A0A] mb-1 font-['DIN_Alternate']">
            {data.total_position_value}
          </div>
          <div>持有总市值</div>
        </div>
        <div className="font-normal text-sm text-[rgba(10,10,10,0.4)] text-center mb-6">
          <div className="font-bold text-xl text-[#DD4E3F] mb-1 font-['DIN_Alternate']">
            {tablePercentFixedRender(data.total_position_value_to_last_quarter, 2, false)}
          </div>
          <div>较上季</div>
        </div>
      </div>
      <div className="text-[#0A0A0A] font-bold mb-2 text-16">持仓信息</div>
      <div>
        <Table size='small' scroll={{ x: 520 }} pagination={false} bordered columns={columns} dataSource={data.position_info}></Table>
        <div className="flex justify-between px-10 py-4 bg-[rgba(191,146,103,0.05)] rounded-lg mt-2.5">
          <div className="font-normal text-sm text-[rgba(10,10,10,0.4)] text-center">
            <div
              className="font-bold text-xl mb-1 font-['DIN_Alternate']"
              style={{ color: data.total_annual_return > 0 ? 'red' : 'green' }}
            >
              {data.total_annual_return > 0 ? '+' : '-'} {data.total_profit_this_year}
            </div>
            <div>持有收益（近1年）</div>
          </div>
          <div className="font-normal text-sm text-[rgba(10,10,10,0.4)] text-center">
            <div className="font-bold text-xl text-[#DD4E3F] mb-1 font-['DIN_Alternate']">
              {tablePercentFixedRender(data.total_annual_return, 2, false)}
            </div>
            <div>年化收益率</div>
          </div>
        </div>
        <div className="font-normal text-sm text-[rgba(10,10,10,0.4)] pt-4">数据来源：国投泰康信托系统</div>
      </div>
    </div >

  </div>
}

export default Positioninformation
