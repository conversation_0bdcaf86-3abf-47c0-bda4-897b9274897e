import React, { memo, HTMLAttributes } from "react";
import { DotLoading } from 'antd-mobile'
import styles from "./index.module.scss";

export interface ChatProps extends HTMLAttributes<HTMLElement> {
  icon?: any;
  type?: "primary" | "danger";
  text?: string;
  bordered?: boolean;
  shadow?: boolean;
  className?: string;
  title?: string;
  disabled?: boolean;
  loading?: boolean;
  size?: number;
  onClick?: () => void;
}

const IconButton: React.FC<ChatProps> = (props) => {
  const { icon, disabled, loading } = props;

  return (
    <button
      className={
        styles["icon-button"] +
        ` ${props.bordered && styles.border} ${props.shadow && styles.shadow} ${props.className ?? ""
        } clickable ${styles[props.type ?? ""]}`
      }
      onClick={props.onClick}
      title={props.title}
      disabled={loading || disabled}
      role="button"
    >
      {loading && <DotLoading color='default'></DotLoading>}
      {!loading && icon && (
        <div
          className={
            styles["icon-button-icon"] +
            ` ${props.type === "primary" && "no-dark"}`
          }
          style={{
            width: props.size ?? 16,
            height: props.size ?? 16,
          }}
        >
          {icon}
        </div>
      )}

      {props.text && (
        <div
          className={styles["icon-button-text"]}
          style={{ marginLeft: icon ? 5 : 0 }}
        >
          {props.text}
        </div>
      )}
    </button>
  );
};

export default memo(IconButton);
