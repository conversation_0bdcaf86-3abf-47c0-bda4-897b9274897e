.icon-button {
    background-color: var(--white);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
    user-select: none;
    outline: none;
    border: none !important; /* 强制移除边框 */
    color: var(--black);
    box-sizing: border-box; /* 确保padding和border包含在元素总宽高内 */
    position: relative; /* 建立新的层叠上下文 */
    z-index: 1; /* 确保元素在正确的层级 */

    &[disabled] {
        cursor: not-allowed;
        opacity: 0.5;
    }

    &.primary {
        background-color: var(--primary);
        color: white;
        /* 确保背景色完全填充 */
        background-clip: padding-box;
        -webkit-background-clip: padding-box;

        path {
            fill: white !important;
        }
    }
}

.shadow {
    box-shadow: var(--card-shadow);
}

.border {
    border: var(--border-in-light);
}

.icon-button:hover {
    border-color: var(--primary);
}

.icon-button-icon {
    width: 16px;
    height: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.clickable:hover {
    filter: none;
}

@media only screen and (max-width: 600px) {
    .icon-button {
        padding: 16px;
    }
}

.icon-button-text {
    margin-left: 5px;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
  