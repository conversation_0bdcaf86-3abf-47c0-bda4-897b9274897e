import { Table } from 'antd'
import { tablePercentFixedRender } from '@/utils/renderFormatter'
// @ts-nocheck

const SimilarFunds = ({ data }: { data: any }) => {
  const columns: any = [
    {
      title: '产品名称',
      dataIndex: 'product_name',
      width: 100
    },
    {
      title: '产品类型',
      dataIndex: 'project_type',
      width: 100
    },
    {
      title: '产品策略',
      dataIndex: 'product_tags',
      width: 100
    },
    {
      title: '风险等级',
      dataIndex: 'risk_level',
      width: 140
    },
    {
      title: '成立日期',
      dataIndex: 'set_up_date',
      width: 140
    },
    {
      title: '起投金额',
      dataIndex: 'min_subscription_amount',
      width: 100
    },
    {
      title: '期限',
      dataIndex: 'deadline',
      width: 100
    },
    {
      title: '近一年涨跌幅',
      dataIndex: 'last_year_return',
      width: 120,
      render: (_: any, record: any) => {
        return tablePercentFixedRender(Number(record.last_year_return), 2, false)
      } // 假设收益以百分比显示
    },
    {
      title: '近一月涨跌幅',
      dataIndex: 'last_month_return',
      width: 140,
      render: (_: any, record: any) => {
        return tablePercentFixedRender(Number(record.last_month_return), 2, false)
      } // 假设收益以百分比显示
    }
  ]
  //style={{ width: "calc(100vw - 50px)" }}
  return (
    <div className='p-2 overflow-scroll scrollbar-pretty' >
      <h4 className='my-2'>{data?.title}</h4>
      <div className='w-full'>
        <Table
          size='small'
          scroll={{ x: 1040 }}
          pagination={false}
          bordered
          columns={columns}
          dataSource={data?.table}></Table>
      </div>
    </div>
  )
}

export default SimilarFunds