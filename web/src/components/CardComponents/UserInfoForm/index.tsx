import { Form, Checkbox, Input } from 'antd';
import { useEffect, useState } from 'react';
import { useStreamContext } from "@/providers/Stream";
import { v4 as uuidv4 } from "uuid";
import { useThreadActivities } from '@/contexts/ThreadActivitiesContext'
// @ts-nocheck

interface FormField {
  type: 'radio' | 'text';
  label: string;
  name: string;
  options?: { label: string; value: string }[];
  required?: boolean; // 字段是否必填（默认true，避免遗漏）
  disabled?: boolean; // 字段是否禁用（默认false，避免遗漏）
}

const UserInfoForm = ({ setNewMessages, data }: { setNewMessages: any, data: any }) => {
  const { title, completed } = data
  const [form] = Form.useForm();
  const thread = useStreamContext();
  const { setinputDisabled } = useThreadActivities();
  // 存储每个字段的选中/输入值
  const [fieldValues, setFieldValues] = useState<Record<string, string>>({});
  // 新增：控制按钮是否可点击的状态（true=可点击，false=禁用）
  const [isConfirmEnabled, setIsConfirmEnabled] = useState(false);

  // --- 关键修改1：定义所有必填字段的名称（覆盖所有选项）---
  // 包含radio类型字段 + text类型的核心字段（含流动性字段）
  const requiredFieldNames = [
    // Radio类型字段（10个单选选项）
    'risk_tolerance', 'investment_style', 'customer_age',
    'customer_asset', 'annualizedReturn', 'investment_period',
    'investment_environment', 'investment_experience', 'investment_objectives',
    // Text类型字段（新增投资金额 + 对应流动性）
    'new_investment', 'new_investment_liquidity'
  ];

  useEffect(() => {
    setinputDisabled(false)
  }, [])

  useEffect(() => {
    if (data.form) {
      // 转换formdata结构以匹配表单字段
      const baseData = { ...data.form };
      const formattedData = formFields.reduce((acc, field) => {
        const fieldValue = baseData[field.name];
        if (field.type === 'radio' && field.options && fieldValue) {
          const option = field.options.find(
            opt => opt.label === fieldValue || opt.value === fieldValue
          );
          acc[field.name] = option ? option.value : fieldValue;
        } else {
          acc[field.name] = fieldValue ?? '';
          // 确保流动性字段也被初始化（避免undefined）
          acc[`${field.name}_liquidity`] = baseData[`${field.name}_liquidity`] ?? '';
        }
        return acc;
      }, {} as Record<string, string>);
      form.setFieldsValue(formattedData);
      setFieldValues(formattedData);
    }
  }, [data.form, form])

  // --- 关键修改2：监听字段值变化，实时校验是否所有必填项已完成 ---
  useEffect(() => {
    // 校验逻辑：所有必填字段的value必须非空（排除空字符串/undefined）
    const allFieldsCompleted = requiredFieldNames.every(fieldName => {
      const value = fieldValues[fieldName];
      // 排除空字符串、undefined、纯空格（针对输入框）
      return !!value && value.trim() !== '';
    });

    // 同时考虑completed状态（原逻辑：completed时按钮禁用）
    setIsConfirmEnabled(allFieldsCompleted && !completed);
  }, [fieldValues, completed])

  const formFields: FormField[] = [
    {
      type: 'radio',
      label: '01.客户的风险承受能力？',
      name: 'risk_tolerance',
      options: [
        { label: '保守型', value: '保守型' },
        { label: '稳健型', value: '稳健型' },
        { label: '平衡型', value: '平衡型' },
        { label: '成长型', value: '成长型' },
        { label: '积极型', value: '积极型' }
      ],
      disabled: true,
      required: true
    },
    {
      type: 'radio',
      label: '02.本次规划期望的组合投资风格？',
      name: 'investment_style',
      options: [
        { label: '保守型', value: '保守型' },
        { label: '稳健型', value: '稳健型' },
        { label: '平衡型', value: '平衡型' },
        { label: '成长型', value: '成长型' },
        { label: '积极型', value: '积极型' }
      ],
      required: true
    },
    {
      type: 'radio',
      label: '03.客户的年龄？',
      name: 'customer_age',
      options: [
        { label: '小于25岁', value: '小于25岁' },
        { label: '25（含）~45岁', value: '25（含）~45岁' },
        { label: '45（含）~65岁', value: '45（含）~65岁' },
        { label: '60岁（含）及以上', value: '60岁（含）及以上' }
      ],
      required: true
    },
    {
      type: 'radio',
      label: '04.客户的金融资产量？',
      name: 'customer_asset',
      options: [
        { label: '小于100万', value: '小于100万' },
        { label: '100（含）~300万', value: '100（含）~300万' },
        { label: '300（含）~600万', value: '300（含）~600万' },
        { label: '600（含）~1000万', value: '600（含）~1000万' },
        { label: '1000万（含）以上', value: '1000万（含）以上' }
      ],
      required: true
    },
    {
      type: 'radio',
      label: '05.客户期里的年化收益率？',
      name: 'annualizedReturn',
      options: [
        { label: '最大回撤不超过3%', value: '最大回撤不超过3%' },
        { label: '最大回撤不超过5%', value: '最大回撤不超过5%' },
        { label: '最大回撤不超过8%', value: '最大回撤不超过8%' },
        { label: '最大回撤可以接受超过8%', value: '最大回撤可以接受超过8%' }
      ],
      required: true
    },
    {
      type: 'text',
      label: '06.客户计划的新增投资金额(万元)',
      name: 'new_investment',
      required: true // 输入框必填
    },
    {
      type: 'radio',
      label: '07.客户的投资期限？',
      name: 'investment_period',
      options: [
        { label: '小于1年', value: '小于1年' },
        { label: '1（含）~3年', value: '1（含）~3年' },
        { label: '3（含）~5年', value: '3（含）~5年' },
        { label: '5年（含）及以上', value: '5年（含）及以上' }
      ],
      required: true
    },
    {
      type: 'radio',
      label: '08.客户的投资环境？', // 注：选项与投资期限一致，建议确认业务逻辑
      name: 'investment_environment',
      options: [
        { label: '小于1年', value: '小于1年' },
        { label: '1（含）~3年', value: '1（含）~3年' },
        { label: '3（含）~5年', value: '3（含）~5年' },
        { label: '5年（含）及以上', value: '5年（含）及以上' }
      ],
      required: true
    },
    {
      type: 'radio',
      label: '09.客户的投资经验？',
      name: 'investment_experience',
      options: [
        { label: '银行存款、货币基金', value: '银行存款、货币基金' },
        { label: '债券、债券型基金、信托等类固收资产', value: '债券、债券型基金、信托等类固收资产' },
        { label: '股票、偏股型基金', value: '股票、偏股型基金' },
        { label: '期货、期权等衍生金融产品', value: '期货、期权等衍生金融产品' },
        { label: '私募股权、困境资产等另类投资', value: '私募股权、困境资产等另类投资' }
      ],
      required: true
    },
    {
      type: 'radio',
      label: '10.客户的投资目标？',
      name: 'investment_objectives',
      options: [
        { label: '资产增值', value: '资产增值' },
        { label: '资产保值', value: '资产保值' },
        { label: '退休准备', value: '退休准备' },
        { label: '财产传承', value: '财产传承' }
      ],
      required: true
    }
  ];


  // 处理单选框变化
  const handleRadioChange = (fieldName: string, value: string) => {
    setFieldValues(prev => ({
      ...prev,
      [fieldName]: value
    }));
    form.setFieldValue(fieldName, value);
  };

  // 处理输入框变化（保留原数字校验逻辑）
  const handleInputChange = (fieldName: string, value: string) => {
    // 只允许数字和小数点（适配金额输入）
    const numericValue = value.replace(/[^0-9.]/g, '');
    // 限制只能输入一个小数点
    const dotIndex = numericValue.indexOf('.');
    const filteredValue = dotIndex !== -1
      ? numericValue.slice(0, dotIndex + 1) + numericValue.slice(dotIndex + 1).replace(/\./g, '')
      : numericValue;

    setFieldValues(prev => ({
      ...prev,
      [fieldName]: filteredValue
    }));
    form.setFieldValue(fieldName, filteredValue);
  };

  // --- 关键修改3：按钮点击事件增加禁用判断（防止非法点击）---
  const save = () => {
    // 双重保险：即使UI上禁用失效，代码层面也拦截
    if (!isConfirmEnabled) return;

    if (!!thread.interrupt && thread.interrupt.value) {
      const newHumanMessage: any = {
        id: uuidv4(),
        type: "human",
        response_metadata: {
          interrupting: true
        },
        content: '我已确认当前规划需求，按照这个参数进行财富规划'
      };

      let newAiMessage = {
        // @ts-ignore
        ...thread.interrupt.value.message,
        tool_calls: [
          // @ts-ignore
          thread.interrupt.value.message.tool_calls[0],
          // @ts-ignore
          thread.interrupt.value.message.tool_calls[1],
          {
            // @ts-ignore
            ...thread.interrupt.value.message.tool_calls[2],
            args: {
              // @ts-ignore
              ...thread.interrupt.value.message.tool_calls[2].args,
              card_data: {
                // @ts-ignore
                ...thread.interrupt.value.message.tool_calls[2].args.card_data,
                form: fieldValues,
                completed: true
              }
            }
          }
        ]
      }
      setinputDisabled(true)
      setNewMessages([...thread.messages, newAiMessage, newHumanMessage])
      thread.submit({},
        {
          command: {
            resume: fieldValues,
            update: {
              messages: [
                newAiMessage,
                newHumanMessage
              ],
            }
          },
        })
    }
  }

  return (
    <div>
      <div className="bg-white mb-4 p-4 mt-4 rounded-lg shadow-[0_2px_10px_rgba(0,0,0,0.1)] [&_.ant-form-item]:mb-0 [&_input[type=text]]:border-none">
        <p>{title}</p>
        <Form form={form} layout="vertical" className="flex flex-col gap-1.5">
          {formFields.map((field) => {
            return (
              <Form.Item
                key={field.name}
                label={<div className="font-medium text-md text-[#0A0A0A] text-left">{field.label}</div>}
                name={field.name}
                // 表单内置校验（配合UI提示，可选但建议保留）
                rules={field.required ? [{ required: true, message: `请${field.type === 'radio' ? '选择' : '输入'}此项` }] : []}
              >
                {field.type === 'text' ? (
                  <div >
                    <div className='flex items-center'>
                      <div className='w-[150px] text-12'>新增投资金额</div>
                      <Input
                        value={fieldValues[field.name] || ''}
                        disabled={completed}
                        className="w-auto rounded-lg !ml-3 !mr-3 !bg-[#0A0A0A08] !min-h-8"
                        size='small'
                        bordered={false}
                        onChange={(e) => handleInputChange(field.name, e.target.value)}
                        placeholder="请输入金额" // 增加占位提示
                      />
                      <div className='w-[50px]'>万元</div>
                    </div>
                    <div className='flex items-center mt-2'>
                      <div className='w-[150px]  text-12' >流动性</div>
                      <Input
                        value={fieldValues[`${field.name}_liquidity`] || ''}
                        disabled={completed}
                        className="w-auto rounded-lg !ml-3 !mr-3 !bg-[#0A0A0A08] !min-h-8"
                        size='small'
                        bordered={false}
                        onChange={(e) => handleInputChange(`${field.name}_liquidity`, e.target.value)}
                        placeholder="请输入流动性金额" // 增加占位提示
                      />
                      <div className='w-[50px]'>万元</div>
                    </div>
                  </div>
                ) : (
                  <>
                    {field.options?.map((option: any) => (
                      <Checkbox
                        key={option.value}
                        disabled={completed || field.disabled}
                        checked={fieldValues[field.name] === option.value}
                        onChange={() => handleRadioChange(field.name, option.value)}
                        className="mr-3 mb-1.5"
                      >
                        {option.label}
                      </Checkbox>
                    ))}
                  </>
                )}
              </Form.Item>
            )
          })}
        </Form>
      </div>
      {/* --- 关键修改4：根据isConfirmEnabled控制按钮禁用状态和样式 --- */}
      <div
        className={`mt-2 cursor-pointer py-2 px-3 w-[160px] text-center rounded-[8px]
          ${completed
            ? 'bg-[#cacaca88] text-[#7b7b7b88] cursor-not-allowed'
            : isConfirmEnabled
              ? 'bg-[#BF9267FF] text-white hover:bg-[#a88058]' // 增加hover反馈
              : 'bg-[#cacaca88] text-[#7b7b7b88] cursor-not-allowed' // 未完成时禁用样式
          }`}
        onClick={save}
        // 原生disabled属性（增强兼容性，防止点击事件触发）
        style={{ cursor: isConfirmEnabled && !completed ? 'pointer' : 'not-allowed' }}
      >
        确认规划需求
      </div>
    </div>
  );
};

export default UserInfoForm;