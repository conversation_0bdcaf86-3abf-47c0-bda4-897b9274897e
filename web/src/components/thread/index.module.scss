.page {
    position: relative;
    display: flex;
    flex-direction: column;
    position: relative;
    height: 100vh;
    .aicon {
        position: absolute;
        right: -23px;
        top: 52px;
        width: 206px;
        height: 173px;
    }

    .chats {
        height: 100%;
        padding: 16px;
        overflow-y: auto;
        position: relative;
        .bg{
          position: absolute;
          top: 0px;
          left: 0px;
          width: 100%;
          height: 250px;
          // background: linear-gradient(to bottom, rgb(248, 231, 206), #F7F7F7FF)
        }
    }
}

.bg{
    // position: absolute;
    // top: 0px;
    // left: 0px;
    width: 100%;
    height: 250px;
    background: linear-gradient(to bottom, rgb(248, 231, 206), #F7F7F7FF)
  }
.container{
   height: 150px;
    background: linear-gradient(to bottom, #ff7e5f, #feb47b); /* 从橙红到浅橙 */
}

.buttonContainer{
  display: flex;
  padding: 12px;
  background-color: #F7F7F7FF;
  padding-bottom: 0px;
  padding-left: 20px;
  padding-top: 0px;
  .button{
    padding: 6px 12px;
    background: #FFFFFF;
    border-radius: 18px;
    border: 1px solid rgba(10,10,10,0.1);
    margin-right: 10px;
    span{
      font-family: 'PingFang SC, PingFang SC';
      font-weight: 500;
      font-size: 12px;
      color: #0A0A0A;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}