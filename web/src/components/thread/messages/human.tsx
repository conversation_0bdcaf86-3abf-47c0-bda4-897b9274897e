'use client'
import { Message } from "@langchain/langgraph-sdk";
import { getContentString } from "../utils";
import { cn } from "@/lib/utils";
import personImg from '@/app/icons/person.png'
import { showToast } from '../../../utils/toast'
import { useStreamContext } from "@/providers/Stream";

export function HumanMessage({
  toBottom,
  data,
}: {
  toBottom: any,
  data: Message;
}) {
  const contentString = getContentString(data.content);
  const stream = useStreamContext();

  const reTry = (context: string) => {
    if (!stream || !context) return;
    const newMessage: Message = { type: "human", content: context };
    toBottom()
    stream.submit(
      {
        messages: [newMessage],
        //@ts-ignore
        current_step: null,
        current_step_detail: "",
        next_node: null,
        next_state: null,
      },
      {
        streamMode: ["values"],
        optimisticValues: (prev: any) => ({
          ...prev,
          context,
          messages: [
            ...(prev.messages ?? []),
            newMessage,
          ],
        }),
      },
    );
  }

  function copyWithExecCommand(textToCopy: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // 创建临时文本框
      const textarea = document.createElement('textarea');
      textarea.value = textToCopy;
      // 隐藏元素（避免影响布局，同时确保能被选中）
      textarea.style.position = 'fixed';
      textarea.style.top = '-999px';
      textarea.style.left = '-999px';
      textarea.style.opacity = '0';
      document.body.appendChild(textarea);

      try {
        // 选中内容（兼容移动端和特殊环境）
        textarea.select();
        textarea.setSelectionRange(0, textToCopy.length); // 强制选中全部内容

        // 执行复制（execCommand 返回布尔值表示成功与否）
        const isSuccess = document.execCommand('copy');
        if (isSuccess) {
          resolve();
        } else {
          reject(new Error('传统复制命令执行失败'));
        }
      } catch (err) {
        reject(err);
      } finally {
        // 清理临时元素
        document.body.removeChild(textarea);
      }
    });
  }

  const handleCopy = async (textToCopy: string) => {
    try {
      // 检测是否为企业微信环境
      const isWeCom = /wxwork/i.test(navigator.userAgent);

      // 企业微信环境强制使用传统方案（Clipboard API 大概率被禁用）
      if (isWeCom) {
        await copyWithExecCommand(textToCopy);
        showToast('已写入剪切板', 'info');
        return;
      }

      // 非企业微信环境：优先用现代 API，失败则降级
      if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
        try {
          await navigator.clipboard.writeText(textToCopy);
          showToast('已写入剪切板', 'info');
        } catch (error) {
          // 现代 API 失败时，自动降级到传统方案
          await copyWithExecCommand(textToCopy);
          showToast('已写入剪切板', 'info');
        }
      } else {
        // 不支持现代 API 时直接用传统方案
        await copyWithExecCommand(textToCopy);
        showToast('已写入剪切板', 'info');
      }
    } catch (error) {
      console.error('复制失败:', error);
      showToast('复制失败，请手动复制', 'error');
    }
  };

  return (
    <div
      className={cn(
        "group ml-auto ",
      )}
    >
      <div className="flex justify-end mr-2 mb-2">
        <img style={{ width: 30, height: 30 }} src={personImg.src}></img>
      </div>

      <div className="relative">
        <div className={cn("flex text-[white] not-[]:flex-col ml-1",)}>
          <div className="flex flex-col  bg-[#29304BFF]" style={{ borderRadius: 10 }}>
            {contentString ? (
              <p className="bg-muted ml-auto w-fit  min-w-[100px] text-left  rounded-3xl px-4 py-2  text-16 whitespace-pre-wrap">
                {contentString}
              </p>
            ) : null}
          </div>
        </div>
        <div className="flex absolute top-[-22px] left-0 gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <div onClick={() => {
            console.log('contentString', contentString)
            handleCopy(contentString)
          }} className="text-left text-12 pl-2 cursor-pointer text-[#aaa] mb-1">复制</div>
          <div onClick={() => {
            reTry(contentString)
          }} className="text-left text-12 pl-2 cursor-pointer text-[#aaa] mb-1">重试</div>
        </div>
      </div>
    </div >
  );
}
