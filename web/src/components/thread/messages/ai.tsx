import { useStreamContext } from "@/providers/Stream";
import { Message } from "@langchain/langgraph-sdk";
import { getContentString } from "../utils";
import { MarkdownText } from "../markdown-text";
import { Tool<PERSON><PERSON><PERSON>, ToolR<PERSON>ult } from "./tool-calls";
import { useQueryState, parseAsBoolean } from "nuqs";
import aiImg from "@/app/icons/ai.png";
import { useEffect, useRef, useState } from 'react';
import TimeLine from "../../CardComponents/TimeLine";
import KnowledgeBase from "@/components/CardComponents/KnowledgeBase";
import FundInfo from "@/components/CardComponents/FundInfo";
import SimilarFunds from "@/components/CardComponents/SimilarFunds";
import CustomerProfile from '@/components/CardComponents/CustomerProfile'
import InformationOrigin from "@/components/CardComponents/InformationOrigin";

export const RenderCards = ({ message }: { message: any }) => {
  if (!message || !message.tool_calls || message.tool_calls.length === 0) {
    return null;
  }
  const hasToolCalls = message.tool_calls.length > 0;
  const toolCallsHaveContents = message.tool_calls.some(
    (tc: any) => tc?.args && Object.keys(tc.args).length > 0
  );

  if (!hasToolCalls || !toolCallsHaveContents) {
    return null;
  }

  const firstToolCall = message.tool_calls[0];
  const secordToolCall = message.tool_calls[1];

  const firsttoolData = firstToolCall?.args?.card_data;
  const secordtoolData = secordToolCall?.args?.card_data;


  if (!firsttoolData && !secordtoolData) {
    return null;
  }
  const isKnowledgeBase = ['dataset', 'search'].includes(firstToolCall.args.card_id)
  const isFundInfo = firstToolCall.args.card_id === "product_brief_info";
  const isCustomerProfile = firstToolCall.args.card_id === "customer_profile";
  const isSimilarProducts = secordToolCall?.args.card_id === "similar_products"
  const isInfoOrigin = secordToolCall?.args.card_id === "information_provenance"

  return (
    <div>
      {isKnowledgeBase && firsttoolData && firsttoolData.length > 0 &&
        <KnowledgeBase data={firsttoolData} />}
      {isFundInfo &&
        <FundInfo data={firsttoolData} />}
      {isSimilarProducts && (
        <SimilarFunds data={secordtoolData} />)}
      {isCustomerProfile &&
        <CustomerProfile data={firsttoolData} />}
      {isInfoOrigin &&
        <InformationOrigin noTitle={false} data={secordtoolData}></InformationOrigin>
      }
    </div>
  );
}

export function AssistantMessage({
  message,
  isLoading,
  processedEventsTimeline,
  historicalActivities
}: {
  message: Message | undefined;
  isLoading: boolean;
  processedEventsTimeline: {
    title: string
    data: string
  }
  historicalActivities: any
}) {
  const content = message?.content ?? [];
  const contentString = getContentString(content);
  const [hideToolCalls] = useQueryState(
    "hideToolCalls",
    parseAsBoolean.withDefault(false),
  );

  const thread = useStreamContext();
  const hasToolCalls =
    message &&
    "tool_calls" in message &&
    message.tool_calls &&
    message.tool_calls.length > 0

  const historydEventsTimeline = (historicalActivities || []).filter((item: any) => {
    return message && item.run_id == message.id
  })
  const historyEventData = historydEventsTimeline && historydEventsTimeline.length > 0
    ? JSON.parse(historydEventsTimeline[0].steps) : []
  const activityForThisBubble = processedEventsTimeline
  return (
    <div>
      {message && message.active && isLoading ? <div>
        <div className="group mr-auto ml-1 w-full">
          <div className="mb-2">
            <img src={aiImg.src} style={{ width: 30, height: 30 }}></img>
          </div>
          <TimeLine
            isActive={true}
            processedEvents={activityForThisBubble}
            isLoading={isLoading}
          />
          {
            contentString.length > 0 &&
            <div className="w-full flex flex-col bg-white p-2 " style={{ borderRadius: 10 }}>
              <div className="p-2">
                <MarkdownText>{contentString}</MarkdownText>
              </div>
            </div>
          }
        </div>
      </div> :
        <div className="group mr-auto ml-1 w-full">
          <div className="mb-2">
            <img src={aiImg.src} style={{ width: 30, height: 30 }}></img>
          </div>
          <TimeLine
            isActive={false}
            processedEvents={historyEventData}
            isLoading={isLoading}
          />
          {
            contentString.length > 0 &&
            <div className="w-full flex flex-col bg-white p-2 " style={{ borderRadius: 10 }}>
              <div className="p-2">
                <MarkdownText>{contentString}</MarkdownText>
              </div>
              <RenderCards message={message}></RenderCards>
            </div>
          }
          {!hideToolCalls && hasToolCalls && message && message.tool_calls && (
            <div className="mt-4">
              { /** @ts-ignore */}
              <ToolCalls toolCalls={message.tool_calls} />
            </div>
          )}
          <div className="text-12 text-[#0A0A0A66] mt-2">以上内容由AI技术生成</div>
        </div>
      }
    </div>
  );
}



export function AssistantMessageLoading() {
  const imgRef = useRef(null);
  const [isImgLoaded, setIsImgLoaded] = useState(false);

  // 预加载图片
  useEffect(() => {
    const img = new Image();
    img.src = aiImg.src;
    img.onload = () => {
      setIsImgLoaded(true);
    };
    // 如果图片已经缓存，直接标记为加载完成
    if (img.complete) {
      setIsImgLoaded(true);
    }
  }, []);

  return (
    <div className="mr-auto flex items-start gap-2">
      {/* 使用固定尺寸的容器，无论图片是否加载完成都保持布局稳定 */}
      <div className="mb-2 w-[30px] h-[30px] flex items-center justify-center">
        {/* 图片加载完成前显示占位背景，完成后显示图片 */}
        {isImgLoaded ? (
          <img
            ref={imgRef}
            src={aiImg.src}
            style={{ width: 30, height: 30, objectFit: 'cover' }}
            alt="AI助手"
          />
        ) : (
          <div className="w-full h-full bg-neutral-200 rounded-full animate-pulse" />
        )}
      </div>
      <div className="bg-muted flex h-8 items-center gap-1 rounded-2xl px-4 py-2">
        <div className="bg-[#BF9267FF] h-1.5 w-1.5 animate-[pulse_1.5s_ease-in-out_infinite] rounded-full"></div>
        <div className="bg-[#BF9267FF] h-1.5 w-1.5 animate-[pulse_1.5s_ease-in-out_0.5s_infinite] rounded-full"></div>
        <div className="bg-[#BF9267FF] h-1.5 w-1.5 animate-[pulse_1.5s_ease-in-out_1s_infinite] rounded-full"></div>
      </div>
    </div>
  );
}
