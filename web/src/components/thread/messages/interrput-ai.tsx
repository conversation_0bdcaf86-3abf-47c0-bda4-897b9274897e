import { useStreamContext } from "@/providers/Stream";
import { MarkdownText } from "../markdown-text";
import { ToolCalls, ToolResult } from "./tool-calls";
import TimeLine from "../../CardComponents/TimeLine";
import aiImg from "@/app/icons/ai.png";

export function InterrputAssistantMessage({
  isLoading,
  setNewMessages,
  historicalActivities,
  processedEventsTimeline
}: {
  isLoading: boolean
  setNewMessages: any;
  historicalActivities: any;
  processedEventsTimeline: any;
}) {
  const thread = useStreamContext();
  const threadInterrupt = thread.interrupt;
  // @ts-ignore
  const interruptValue = threadInterrupt?.value?.message
  const context = interruptValue?.content
  const historydEventsTimeline = (historicalActivities || []).filter((item: any) => {
    // @ts-ignore
    if (threadInterrupt && threadInterrupt.value && threadInterrupt.value.message) {
      // @ts-ignore
      return item.run_id === threadInterrupt.value.message.id
    }
    return interruptValue && item.run_id == interruptValue.id
  })
  const historyEventData = historydEventsTimeline && historydEventsTimeline.length > 0
    ? JSON.parse(historydEventsTimeline[0].steps) : []
  return (
    <div>
      <div className="group mr-auto ml-1 w-full">
        <div className="mb-2">
          <img src={aiImg.src} style={{ width: 30, height: 30 }}></img>
        </div>
        {
          historyEventData && historyEventData.length > 0 &&
          <TimeLine
            isActive={false}
            processedEvents={historyEventData}
            isLoading={false}
          />
        }
        <div className="flex flex-col bg-white p-2 " style={{ borderRadius: 10 }}>
          <>
            {context && context.length > 0 && (
              <div className="p-2">
                <MarkdownText>{context}</MarkdownText>
              </div>
            )}

          </>
        </div>
        {interruptValue && interruptValue.tool_calls && interruptValue.tool_calls.length > 0 &&
          <ToolCalls setNewMessages={setNewMessages} isInterrupt={true} toolCalls={interruptValue.tool_calls} />
        }
        <div className="text-12 text-[#0A0A0A66] mt-2">以上内容由AI技术生成</div>
      </div>
    </div>
  );
}
