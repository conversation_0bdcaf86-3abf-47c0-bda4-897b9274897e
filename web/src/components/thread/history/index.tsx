import { useThreads } from "@/providers/Thread";
import { Thread } from "@langchain/langgraph-sdk";
import { useEffect, useState } from "react";
import { useRouter, } from 'next/navigation';
import { useQueryState } from "nuqs";
import EditThread from "../EditThread";
import { format, parseISO } from 'date-fns';
import { Tooltip, message as messageApi, Popconfirm } from "antd";
import { Skeleton } from "@/components/ui/skeleton";
import c19Img from '@/app/icons/c19.png';
import c21Img from '@/app/icons/c21.png';
import c22Img from '@/app/icons/c22.png';
import c23Img from '@/app/icons/c23.png';
import c24Img from '@/app/icons/c24.png';
import c25Img from '@/app/icons/c25.png';
import React from "react";
import { useThreadActivities } from '@/contexts/ThreadActivitiesContext'


function ThreadList({
  threads,
  deleteThread,
  getList,
  onCheck
}: {
  threads: Thread[];
  deleteThread: any,
  getList: () => void
  onCheck: () => void
}) {
  const router = useRouter();
  const [threadId, setThreadId] = useQueryState("threadId");
  const [newTheads, setnewTheads] = useState<Thread[]>(threads);
  const { hisrotyInfo, setinputDisabled } = useThreadActivities()


  // 处理线程项点击跳转
  const handleThreadClick = (thread: Thread) => {
    if (thread.thread_id === threadId) return;
    if (onCheck) {
      onCheck()
    }
    setinputDisabled(true);
    localStorage.setItem("historicalActivities", '');
    router.push('/chat?threadId=' + thread.thread_id);
  };

  const toTop = (id: any, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡到父容器
    // 复制当前线程列表，避免直接修改原数组
    const updatedThreads = [...newTheads];

    // 找到目标线程的索引
    const targetIndex = updatedThreads.findIndex(thread => thread.thread_id === id);
    // 如果找到目标线程且它不在第一位，则调整顺序
    if (targetIndex !== -1 && targetIndex !== 0) {
      // 移除目标线程
      const [movedThread] = updatedThreads.splice(targetIndex, 1);
      // 将目标线程插入到数组开头
      updatedThreads.unshift(movedThread);
      // 更新状态
      setnewTheads(updatedThreads);
    }
  }

  const handleDelete = (id: any,) => {

    deleteThread(id).then((res: any) => {
      if (id === threadId) {
        setThreadId(null)
      }
      getList()
    })
  }

  // 阻止事件冒泡的工具函数
  const stopPropagation = (e) => {
    e.stopPropagation();
  };

  return (
    <div className="flex h-full w-full flex-col items-start justify-start gap-2 overflow-y-scroll [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-track]:bg-transparent">
      {newTheads.map((t, index) => {
        let itemText: any = t?.metadata?.name
        const isSelected = !!(threadId === t.thread_id);
        if (t && t.thread_id && hisrotyInfo && hisrotyInfo.threadId && t.thread_id === hisrotyInfo.threadId) {
          t.values = {
            messages: hisrotyInfo.messages
          }
        }
        return (
          // 在最外层容器添加点击事件处理跳转
          <div
            key={t.thread_id}
            className={`w-full p-3 bg-white transition-colors duration-300 cursor-pointer select-none hover:bg-gray-50 ${isSelected && 'bg-[rgba(10,10,10,0.03)]'
              }`}
            onClick={() => handleThreadClick(t)} // 点击整个项跳转
          >
            <div className="w-full box-border flex items-center justify-between">
              <div className="w-14">
                <img
                  src={index % 2 === 0 ? c21Img.src : c22Img.src}
                  width={56}
                  height={56}
                  alt="c20"
                />
              </div>
              <div className="w-[calc(100%-68px)] box-border">
                <div className="flex items-center justify-between">
                  <div className="text-base font-bold max-w-[calc(100%-90px)] overflow-hidden text-ellipsis whitespace-nowrap animate-[slide-in_ease_0.3s]">
                    {itemText || '新的聊天'}
                  </div>
                  <div className="flex items-center justify-between">
                    {index > 0 && (
                      <div onClick={(e) => toTop(t.thread_id, e)}>
                        <Tooltip placement="top" title="置顶">
                          <img
                            src={c23Img.src}
                            width={20}
                            height={20}
                            alt="置顶"
                          />
                        </Tooltip>
                      </div>
                    )}
                    <div style={{ marginLeft: 8 }}>
                      <EditThread onSuccess={() => getList()} thread={t}>
                        <Tooltip placement="top" title="重命名">
                          <img
                            src={c24Img.src}
                            width={20}
                            height={20}
                            alt="重命名"
                          />
                        </Tooltip>
                      </EditThread>
                    </div>
                    <Popconfirm
                      title="确定要删除吗？"
                      onConfirm={() => handleDelete(t.thread_id)}
                      onCancel={stopPropagation} // 取消时阻止冒泡
                      onClick={stopPropagation} // 点击确认框本身阻止冒泡
                      okText="确定"
                      cancelText="取消"
                    >
                      <div style={{ marginLeft: 8 }} >
                        <img
                          src={c25Img.src}
                          width={20}
                          height={20}
                          alt="删除"
                        />
                      </div>
                    </Popconfirm>

                  </div>
                </div>
                <div className="flex justify-between text-sm mt-2 animate-[slide-in_ease_0.3s]">
                  <div className="text-[rgba(10,10,10,0.6)] overflow-hidden text-ellipsis whitespace-nowrap">
                    { /**@ts-ignore */}
                    总计：{t?.values?.messages?.filter(item => item.type !== "system").length || 0}条
                  </div>
                  <div className="text-[rgba(10,10,10,0.4)] overflow-hidden text-ellipsis whitespace-nowrap">
                    {t?.created_at ? format(parseISO(t.created_at), 'yyyy-MM-dd HH:mm') : ''}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div >
  );
}

function ThreadHistoryLoading() {
  return (
    <div className="flex h-full w-full flex-col items-start justify-start gap-2 overflow-y-scroll [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-track]:bg-transparent">
      {Array.from({ length: 30 }).map((_, i) => (
        <Skeleton
          key={`skeleton-${i}`}
          className="h-10 w-full"
        />
      ))}
    </div>
  );
}



const ThreadHistory = ({ update, onCheck }: { update: number, onCheck?: any }) => {
  const [count, setCount] = useState(0)
  const { creatThread } = useThreads();
  const { setinputDisabled } = useThreadActivities()
  const { deleteThread, getThreads, threads, setThreads, threadsLoading, setThreadsLoading } =
    useThreads();
  const router = useRouter();
  useEffect(() => {
    if (typeof window === "undefined") return;
    setThreadsLoading(true);
    getThreads()
      .then(setThreads)
      .catch(console.error)
      .finally(() => setThreadsLoading(false));
  }, [count, update]);

  /**
  * 处理开始聊天按钮点击事件
  *
  * 当用户点击开始聊天按钮时，会调用此方法。
  * 该方法会将路由跳转到聊天页面。
  */
  const handleStartChat = () => {
    creatThread('').then((res: any) => {
      if (res && res.thread_id) {
        setCount(count + 1)
        setinputDisabled(true);
        router.push(`/chat?threadId=${res.thread_id}`)
      }
    })

  }
  return (
    <>
      <div className="mx-4">
        <div className="my-2 font-black text-20">对话历史</div>
        <div
          className="w-full mb-2 rounded-[10px] h-12 box-border bg-white border border-gray-200 flex items-center justify-center cursor-pointer hover:border-gray-300 transition-colors duration-200"
          onClick={handleStartChat}
        >
          <img
            src={c19Img.src}
            width={20}
            height={20}
            alt="新的聊天"
          />
          <span className="ml-2 font-medium text-gray-900 font-[PingFang SC]">
            新的聊天
          </span>
        </div>
      </div>
      {threadsLoading ? (
        <ThreadHistoryLoading />
      ) : (
        <ThreadList
          deleteThread={deleteThread}
          onCheck={onCheck}
          getList={() => {
            setCount(count + 1)
          }}
          threads={threads} />
      )}
    </>
  );
}

export default React.memo(ThreadHistory)