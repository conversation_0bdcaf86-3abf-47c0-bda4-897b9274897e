import { useState, useEffect } from 'react';
import { Modal, Input, Form, message } from 'antd';
import { useThreads } from "@/providers/Thread";

// 定义组件属性接口
interface EditThreadProps {
  thread: any
  onSuccess: () => void;
  children: React.ReactNode;
}

const EditThread = ({
  thread,
  children,
  onSuccess,
}: EditThreadProps) => {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const { updateThread } = useThreads();



  // 处理表单提交
  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      const threadName = values.dialogueName.trim();

      const res = await updateThread(thread.thread_id, threadName);
      if (res && !res.error) {
        console.log('更新成功，关闭弹窗');
        setVisible(false);
        form.resetFields();
        onSuccess();
      }
    } catch (error) {
      console.error('创建对话失败:', error);
      message.error(error instanceof Error ? error.message : '创建对话失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 强制关闭弹窗的方法
  const forceCloseModal = () => {
    console.log('执行强制关闭');
    // 使用setTimeout确保状态更新生效
    setTimeout(() => {
      setVisible(false);
      form.resetFields();
    }, 0);
  };

  // 键盘事件处理 - 移至document级别
  useEffect(() => {
    const handleDocKeyDown = (e: KeyboardEvent) => {
      if (!visible) return;

      if (e.key === 'Enter' && !loading) {
        e.preventDefault();
        handleSubmit();
      } else if (e.key === 'Escape' && !loading) {
        e.preventDefault();
        forceCloseModal();
      }
    };

    document.addEventListener('keydown', handleDocKeyDown);
    return () => {
      document.removeEventListener('keydown', handleDocKeyDown);
    };
  }, [visible, loading]);

  return (
    <div>
      <span
        onClick={() => {
          console.log('打开弹窗');
          setVisible(true);
        }}
        style={{ cursor: 'pointer' }}
        role="button"
        tabIndex={0}
        onKeyPress={(e) => e.key === 'Enter' && setVisible(true)}
      >
        {children}
      </span>

      {/* 使用visible状态直接控制Modal显示 */}
      {visible && (
        <Modal
          open={true}
          className="no-padding-modal"
          centered
          width={279}
          icon={null}
          title={
            <div className="w-full text-center text-lg py-2">
              修改聊天名称
            </div>
          }
          // 同时设置onCancel和maskClick处理关闭
          onCancel={forceCloseModal}
          onMaskClick={forceCloseModal}
          footer={null}
          destroyOnHidden
          maskClosable={true} // 允许点击遮罩关闭
          transitionName="fade"
        >
          <div className="flex flex-col items-center justify-center pb-6">
            <div className="text-sm text-gray-600 text-center my-3">
              请输入对话名称
            </div>
            <Form
              form={form}
              layout="vertical"
              initialValues={{ dialogueName: thread?.metadata?.name }}
              autoComplete="off"
            >
              <Form.Item
                name="dialogueName"
                rules={[
                  { required: true, message: '请输入对话名称' },
                  { max: 50, message: '名称不能超过50个字符' },
                  { whitespace: true, message: '名称不能全为空格' },
                  {
                    pattern: /^[^/\\:*?"<>|]+$/,
                    message: '名称不能包含特殊字符: /\\:*?"<>|'
                  }
                ]}
              >
                <Input
                  placeholder="请输入对话名称"
                  autoFocus
                  disabled={loading}
                  className="w-[215px] h-[36px] pl-3 rounded-md bg-gray-100 border-none focus:ring-2 focus:ring-blue-500 transition-all outline-none"
                />
              </Form.Item>
            </Form>
          </div>

          {/* 底部按钮区域 */}
          <div className="flex">
            <button
              type="button"
              className="flex-1 py-3 border-t border-gray-200 text-center text-base cursor-pointer hover:bg-gray-50 transition-colors"
              onClick={forceCloseModal}
              disabled={loading}
              style={{ borderRight: '1px solid #e5e7eb' }}
            >
              取消
            </button>
            <button
              type="button"
              className={`flex-1 py-3 border-t border-gray-200 text-center text-base font-medium cursor-pointer hover:bg-gray-50 transition-colors ${loading ? 'opacity-70' : ''}`}
              onClick={handleSubmit}
              disabled={loading}
            >
              修改
            </button>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default EditThread;
