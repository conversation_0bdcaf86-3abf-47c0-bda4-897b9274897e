import { v4 as uuidv4 } from "uuid";
import { useEffect, useRef, useState, useLayoutEffect } from "react";
import { cn } from "@/lib/utils";
import { useStreamContext } from "@/providers/Stream";
import { useThreads } from "@/providers/Thread";
import ThreadHistory from "@/components/thread/history";
import { AssistantMessage, AssistantMessageLoading } from "./messages/ai";
import { InterrputAssistantMessage } from './messages/interrput-ai'
import { HumanMessage } from "./messages/human";
import Welcome from '@/components/CardComponents/Welcome'
import { useRouter } from 'next/navigation';
import historyImg from '@/app/icons/history.png'
import c19Img from '@/app/icons/c19.png'
import { useQueryState } from "nuqs";
import { toast } from "sonner";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { useArtifactOpen, useArtifactContext } from "./artifact";
import InputPanel from "../CardComponents/InputPanel";
import { getThinkingList } from '@/services/api'
import { useThreadActivities } from '../../contexts/ThreadActivitiesContext'
import Questions from "../CardComponents/Questions";
import GuessQuestion from "../CardComponents/GuessQuestion";

// 优化后的滚动逻辑Hooks
function useScrollToBottom() {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [autoScroll, setAutoScroll] = useState(true);

  // 滚动到底部（使用requestAnimationFrame确保DOM更新后执行）
  const scrollToBottom = () => {
    const dom = scrollRef.current;
    if (dom) {
      requestAnimationFrame(() => {
        dom.scrollTop = dom.scrollHeight - 40;
      });
    }
  };

  // 修复useLayoutEffect依赖，仅在autoScroll或scrollToBottom变化时执行
  useLayoutEffect(() => {
    if (autoScroll) {
      scrollToBottom();
    }
  }, [autoScroll, scrollToBottom]);

  return {
    scrollRef,
    autoScroll,
    setAutoScroll,
    scrollToBottom,
  };
}

export interface ProcessedEvent {
  title: string;
  data: any;
}

export function Thread() {
  // 初始化滚动相关逻辑
  const { scrollRef, autoScroll, setAutoScroll, scrollToBottom } = useScrollToBottom();

  // 状态管理
  const [artifactContext, setArtifactContext] = useArtifactContext();
  const [artifactOpen, closeArtifact] = useArtifactOpen();
  const [threadId, _setThreadId] = useQueryState("threadId");
  const [input, setInput] = useState<any>("");
  const [update, setUpdate] = useState<number>(0)
  const [quickSelect, setQuickSelect] = useState<string | null>(null)
  //这里为了处理打断情况下，需要前端手动补充两条信息，分别是上一条打断和按钮点击自动输入的,因此默认的 messages不太符合，所以这里重新定义
  const [newMessages, setNewMessages] = useState<any>([]);
  const isLargeScreen = useMediaQuery("(min-width: 524px)");
  const router = useRouter()
  const stream = useStreamContext();
  console.log('stream===>', stream)
  // @ts-ignore
  const { processedEventsTimeline, showGuessQuestion, setShowGuessQuestion } = stream
  const {
    historicalActivities,
    setHistoricalActivities,
    inputDisabled,
    setinputDisabled,
    sethisrotyInfo
  } = useThreadActivities()
  const { creatThread } = useThreads();
  const messages = stream.messages.filter(m => {
    // 不需要再在这里设置状态
    const isFilterData = m.content === '' &&
      m.tool_calls &&
      m.tool_calls.length > 0 &&
      m.tool_calls[0].name === "RouteDecision";
    return m.type !== 'system' && !isFilterData;
  });
  const chatStarted = !!threadId || !messages
  const isLoading = stream.isLoading;
  const lastError = useRef<string | undefined>(undefined);


  // 主内容区域最大宽度（统一内容和输入框宽度）
  const contentMaxWidth = "w-full"; // 可根据实际设计调整

  // 处理滚动事件，控制自动滚动状态（优化阈值）
  const handleScroll = () => {
    if (!scrollRef.current) return;
    const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
    // 阈值改为20px，减少误判
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - 20;
    setAutoScroll(isNearBottom);
  };

  // 当消息变化时自动滚动（受autoScroll控制）
  useLayoutEffect(() => {
    if (autoScroll) {
      scrollToBottom();
    }
  }, [messages.length, autoScroll, scrollToBottom]);


  useEffect(() => {
    if (threadId) {
      getThinkingList({ thread_id: threadId }).then(res => {
        if (res && res.data) {
          setHistoricalActivities(res.data.data)
        }
      })
    }
  }, [threadId])

  useEffect(() => {
    if (!stream.error) {
      lastError.current = undefined;
      return;
    }
    try {
      const message = (stream.error as any).message;
      if (!message || lastError.current === message) {
        return;
      }

      lastError.current = message;
      toast.error("发生错误，请重试", {
        description: (
          <p>
            <strong>报错:</strong> <code>{message}</code>
          </p>
        ),
        richColors: true,
        closeButton: true,
      });
    } catch {
      // 忽略错误
    }
  }, [stream.error]);

  useEffect(() => {
    if (!!stream.interrupt) {
      setShowGuessQuestion(false)
    } else {
      // setShowGuessQuestion(true)
    }
  }, [stream.interrupt]);

  useEffect(() => {
    const handleScroll = () => {
      if (scrollRef.current) {
        // console.log('滚动距离:', scrollRef.current.scrollTop);
      }
    };

    const element = scrollRef.current;
    if (element) {
      element.addEventListener('scroll', handleScroll);
      return () => element.removeEventListener('scroll', handleScroll);
    }
  }, []);
  const prevMessageLength = useRef(0);
  useEffect(() => {
    if (
      messages.length !== prevMessageLength.current &&
      messages?.length &&
      messages[messages.length - 1].type === "ai"
    ) {
      if (autoScroll) {
        scrollToBottom();
      }
    }

    prevMessageLength.current = messages.length;
  }, [messages, scrollToBottom, autoScroll]); // 添加autoScroll依赖


  const handleSubmit = () => {
    if ((input.trim().length === 0) || isLoading)
      return;
    setQuickSelect(null)
    scrollToBottom()
    const newHumanMessage: any = {
      id: uuidv4(),
      type: "human",
      content: input
    };

    const context =
      Object.keys(artifactContext).length > 0 ? artifactContext : undefined;
    if (!!stream.interrupt && stream.interrupt.value) {
      stream.submit({},
        {
          command:
          {
            resume: input,
            update: {
              messages: [
                // @ts-ignore
                stream.interrupt.value.message,
                newHumanMessage
              ],
            }
          }
        })
    } else {
      setShowGuessQuestion(false)
      stream.submit(
        {
          messages: [newHumanMessage],
          context,
          //@ts-ignore
          current_step: null,
          current_step_detail: "",
          next_node: null,
          next_state: null,
        },
        {
          streamMode: ["values"],
          optimisticValues: (prev) => ({
            ...prev,
            context,
            messages: [
              ...(prev.messages ?? []),
              newHumanMessage,
            ],
          }),
        },
      );
    }
    setInput("");
  };
  const newThread = () => {
    //这里时间差可能会出现一直没有出现首页的情况
    creatThread('').then((res: any) => {
      if (res && res.thread_id) {
        setUpdate(update + 1)
        setinputDisabled(true)
        _setThreadId(res.thread_id)
      }
    })
  }

  useEffect(() => {
    sethisrotyInfo({
      threadId,
      messages: newMessages
    })
  }, [JSON.stringify(newMessages)])

  useEffect(() => {
    if (isLoading && messages[messages.length - 1].type == 'human') {
      let tempMessages = newMessages.filter(item => item.type != "system")
      //普通问答和打断分开处理
      //普通问答，没有 setMessages,所有的信息都来在于 messages
      if (messages.length > tempMessages.length) {
        setNewMessages([...messages, {
          id: uuidv4(),
          type: "ai",
          content: "",
          active: true,
        }
        ])
      } else {
        //打断问答，打断在过程中setMessages，newMessages更新的比messages早
        const hasActiveTrue = newMessages.some(item => item.active === true);
        if (hasActiveTrue) return
        setNewMessages([...tempMessages, {
          id: uuidv4(),
          type: "ai",
          content: "",
          active: true,
        }
        ])
      }
    } else {
      //当最后一条消息是打断的话，需要把active设置为false
      if (stream.interrupt) {
        setNewMessages(messages.filter(item => !item.active))
      } else {
        let tempMessages = messages.map((item, index) => {
          if (index === messages.length - 1 && item.type === 'ai') {
            return { ...item, active: true };
          }
          return item;
        });
        setNewMessages(tempMessages); 1
      }
    }
  }, [isLoading, messages.length, stream.interrupt])
  const token = localStorage.getItem("authToken");


  console.log(1111, messages, newMessages)
  console.info('链接地址', `https://uat-eww.sdictktrust.com:8892/zp/#/aihome?access_token=${token}&thread_id=${threadId}`)
  return (
    <div className="flex h-screen w-full overflow-hidden">
      {/* 历史记录固定在左侧 */}
      {isLargeScreen && (
        <div className="fixed left-0 top-0 h-full w-[300px] border-r border-gray-200 bg-white z-40 shadow-lg">
          <ThreadHistory update={update} onCheck={() => {
            setQuickSelect(null)
          }} />
        </div>
      )}
      <div
        className={cn(
          "grid w-full grid-cols-[1fr_0fr] transition-all duration-500",
          artifactOpen && "grid-cols-[3fr_2fr]",
        )}
      >
        <div
          className={cn(
            "relative flex min-w-0 flex-1 flex-col overflow-hidden transition-all duration-300",
            !chatStarted && "grid-rows-[1fr]",
            // 大屏幕时主内容区域从左侧偏移并计算宽度
            isLargeScreen ? "ml-[300px] w-[calc(100%-300px)]" : "w-full"
          )}
        >
          {/* 顶部导航栏 - 实现吸顶效果 */}
          <div className="sticky top-0 z-50 bg-white/90 backdrop-blur-sm px-4 py-1 shadow-sm">
            <div className="flex justify-between items-center">
              <img
                onClick={() => router.push('/history')}
                src={historyImg.src}
                className="p-1 w-[30px] h-[30px] rounded-[4px] cursor-pointer hover:bg-gray-100 transition-colors"
                alt="历史记录"
              />
              <img
                onClick={() => newThread()}
                src={c19Img.src}
                width={28}
                height={28}
                className="cursor-pointer hover:bg-gray-100 p-1 rounded transition-colors"
                alt="新的聊天"
              />
            </div>
          </div>

          {/* 主内容区域 - 分离滚动容器和输入框 */}
          <div className="relative flex flex-col overflow-hidden bg-[#f7f7f7] flex-1">
            {/* 滚动容器 - 仅包含消息内容 */}
            <div
              ref={scrollRef}
              onScroll={handleScroll}
              style={{ flex: 1, overflowY: "auto", overflowX: "hidden", minHeight: 0 }}
              className="[&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-track]:bg-transparent"
            >
              {/* 消息内容 - 使用统一的最大宽度 */}
              <div className="w-full">
                <Welcome></Welcome>
                {newMessages && newMessages.length > 0 && (
                  <div className={`${contentMaxWidth} w-full pl-2 pt-8 pb-4 px-4 mx-auto flex flex-col gap-4 `}>
                    {newMessages
                      .map((message: any, index: any) =>
                        message.type === "human" ? (
                          <HumanMessage
                            toBottom={scrollToBottom}
                            key={message.id || `${message.type}-${index}`}
                            data={message}
                          />
                        ) : (
                          <AssistantMessage
                            key={message.id || `${message.type}-${index}`}
                            message={message}
                            isLoading={isLoading}
                            historicalActivities={historicalActivities}
                            processedEventsTimeline={processedEventsTimeline}
                          />
                        ),
                      )}
                    { /**打断对话，不是一直出现，中间会出现stream.interrupt为 null 因此思考过程都放在AssistantMessage里面了 */}
                    {!!stream.interrupt && (
                      <InterrputAssistantMessage
                        key="interrupt-msg"
                        setNewMessages={setNewMessages}
                        historicalActivities={historicalActivities}
                        processedEventsTimeline={processedEventsTimeline}
                        isLoading={isLoading}
                      />
                    )}
                    {/* {isLoading && processedEventsTimeline.length === 0 && (
                      <AssistantMessageLoading />
                    )} */}
                  </div>
                )}

                {/* 快速选择区域 - 继承内容宽度 */}
                <div className={`${contentMaxWidth} mt-2  px-4`}>
                  {(quickSelect || messages.length == 0 || !messages) && <Questions setQuickSelect={setQuickSelect} type={quickSelect} />}
                </div>
                { /**猜你想问区域 - 继承内容宽度 */}
                <div className={`${contentMaxWidth} mt-2  px-4`}>
                  {(showGuessQuestion) && <GuessQuestion threadId={threadId} />}
                </div>
              </div>
            </div>

            {/* 底部输入框 - 作为独立元素固定在底部 */}
            <div style={{ boxShadow: '0 -4px 10px rgba(0, 0, 0, .07)', zIndex: 10 }} className="w-full bg-[white] py-3">
              <div className={`${contentMaxWidth} flex mt-2 mb-0 mx-2 px-4`}>
                <div
                  className="py-1 px-4 rounded-[28px] border-[#0A0A0A1A] mr-2 border-1 cursor-pointer"
                  onClick={() => {
                    setQuickSelect("answer")
                    setShowGuessQuestion(false)
                    scrollToBottom()
                  }}
                >
                  智能问答
                </div>
                <div
                  className="py-1 font-custom px-4 rounded-[28px]  border-1 border-[#0A0A0A1A] cursor-pointer"
                  onClick={() => {
                    setShowGuessQuestion(false)
                    setQuickSelect("advisoryScenarios")
                    scrollToBottom()
                  }}
                >
                  投顾场景
                </div>
              </div>
              {/* 输入框容器 - 使用统一的最大宽度 */}
              <div className={`${contentMaxWidth} w-full flex flex-col items-center  px-2`}>
                <InputPanel
                  inputDisabled={inputDisabled}
                  loading={stream.isLoading}
                  submitKey={"Enter"}
                  inputChange={(text: any) => setInput(text)}
                  inputFocus={() => { }}
                  submitClick={handleSubmit}
                ></InputPanel>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}